#!/bin/bash

# 验证 agent-transport 模块的 Gradle 构建文件
# Copyright (c) 2019 Of Him Code Technology Studio

echo "🔍 正在验证 agent-transport 模块的 Gradle 构建文件..."
echo

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证计数器
PASS_COUNT=0
FAIL_COUNT=0

# 验证函数
verify_file() {
    local file_path="$1"
    local description="$2"
    
    if [ -f "$file_path" ]; then
        echo -e "${GREEN}✅ PASS${NC} - $description"
        echo "   📁 位置: $file_path"
        ((PASS_COUNT++))
    else
        echo -e "${RED}❌ FAIL${NC} - $description"
        echo "   📁 缺失: $file_path"
        ((FAIL_COUNT++))
    fi
    echo
}

# 验证目录结构
echo "📂 验证目录结构..."
echo "-----------------------------------"

verify_file "build.gradle" "Agent Transport 父模块构建文件"
verify_file "agent-transport-common/build.gradle" "Agent Transport Common 模块构建文件"
verify_file "agent-transport-http/build.gradle" "Agent Transport HTTP 模块构建文件"

# 验证 Maven 原始文件（参考）
echo "📋 验证 Maven 参考文件..."
echo "-----------------------------------"

verify_file "pom.xml" "Agent Transport 父模块 Maven POM"
verify_file "agent-transport-common/pom.xml" "Agent Transport Common Maven POM"
verify_file "agent-transport-http/pom.xml" "Agent Transport HTTP Maven POM"

# 验证 SPI 服务配置
echo "🔌 验证 SPI 服务配置..."
echo "-----------------------------------"

verify_file "agent-transport-http/src/main/resources/META-INF/services/org.dromara.jpom.transport.TransportServer" "HTTP Transport SPI 服务配置"

# 验证 Java 源码目录
echo "☕ 验证 Java 源码结构..."
echo "-----------------------------------"

verify_file "agent-transport-common/src/main/java" "Common 模块 Java 源码目录"
verify_file "agent-transport-http/src/main/java" "HTTP 模块 Java 源码目录"

# 检查关键的 Java 文件
echo "🔍 验证关键 Java 文件..."
echo "-----------------------------------"

verify_file "agent-transport-common/src/main/java/org/dromara/jpom/transport/TransportServer.java" "传输服务接口"
verify_file "agent-transport-common/src/main/java/org/dromara/jpom/transport/TransportServerFactory.java" "传输服务工厂"
verify_file "agent-transport-http/src/main/java/org/dromara/jpom/transport/HttpTransportServer.java" "HTTP 传输服务实现"

# 验证构建文件内容关键配置
echo "⚙️  验证构建配置内容..."
echo "-----------------------------------"

# 检查父模块构建文件是否包含子模块配置
if grep -q "subprojects" build.gradle 2>/dev/null; then
    echo -e "${GREEN}✅ PASS${NC} - 父模块包含子项目配置"
    ((PASS_COUNT++))
else
    echo -e "${YELLOW}⚠️  WARN${NC} - 父模块可能缺少子项目配置"
fi
echo

# 检查 common 模块依赖
if grep -q "hutool-core" agent-transport-common/build.gradle 2>/dev/null; then
    echo -e "${GREEN}✅ PASS${NC} - Common 模块包含 Hutool 依赖"
    ((PASS_COUNT++))
else
    echo -e "${RED}❌ FAIL${NC} - Common 模块缺少 Hutool 依赖"
    ((FAIL_COUNT++))
fi
echo

# 检查 http 模块依赖
if grep -q "agent-transport-common" agent-transport-http/build.gradle 2>/dev/null; then
    echo -e "${GREEN}✅ PASS${NC} - HTTP 模块依赖 Common 模块"
    ((PASS_COUNT++))
else
    echo -e "${RED}❌ FAIL${NC} - HTTP 模块缺少 Common 模块依赖"
    ((FAIL_COUNT++))
fi
echo

if grep -q "hutool-http" agent-transport-http/build.gradle 2>/dev/null; then
    echo -e "${GREEN}✅ PASS${NC} - HTTP 模块包含 Hutool HTTP 依赖"
    ((PASS_COUNT++))
else
    echo -e "${RED}❌ FAIL${NC} - HTTP 模块缺少 Hutool HTTP 依赖"
    ((FAIL_COUNT++))
fi
echo

# 验证编码配置
if grep -q "UTF-8" build.gradle 2>/dev/null; then
    echo -e "${GREEN}✅ PASS${NC} - 父模块包含 UTF-8 编码配置"
    ((PASS_COUNT++))
else
    echo -e "${YELLOW}⚠️  WARN${NC} - 父模块可能缺少 UTF-8 编码配置"
fi
echo

# 总结报告
echo "📊 验证结果总结"
echo "=================================="
echo -e "通过测试: ${GREEN}$PASS_COUNT${NC}"
echo -e "失败测试: ${RED}$FAIL_COUNT${NC}"
echo

if [ $FAIL_COUNT -eq 0 ]; then
    echo -e "${GREEN}🎉 所有验证通过！agent-transport 模块的 Gradle 构建文件配置正确。${NC}"
    exit 0
else
    echo -e "${RED}⚠️  发现 $FAIL_COUNT 个问题，请检查上述失败项目。${NC}"
    exit 1
fi

echo
echo "📝 说明:"
echo "1. 这些 Gradle 构建文件是从对应的 Maven POM 文件转换而来"
echo "2. 保持了与原始 Maven 配置的一致性"
echo "3. 包含了必要的依赖管理和构建配置"
echo "4. 支持 Java 8 编译和 UTF-8 编码"
echo "5. 包含了发布和签名配置（release profile）"
echo