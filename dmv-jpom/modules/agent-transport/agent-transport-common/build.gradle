/*
 * Copyright (c) 2019 Of Him Code Technology Studio
 * Jpom is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *             http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

// Agent Transport Common 模块
// 提供传输层的通用接口和基础组件

plugins {
    id 'java-library'
    id 'io.spring.dependency-management'
}

description = 'Jpom Agent Transport Common - 传输层通用组件'

// 依赖管理 - 使用Spring Boot BOM
dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:3.4.7"
    }
    dependencies {
        dependency "com.alibaba.fastjson2:fastjson2:2.0.53"
        dependency "cn.hutool:hutool-core:5.8.38"
        dependency "cn.hutool:hutool-http:5.8.38"
        dependency "cn.hutool:hutool-system:5.8.38"
        dependency "cn.hutool:hutool-extra:5.8.38"
        dependency "cn.hutool:hutool-crypto:5.8.38"
        dependency "cn.hutool:hutool-cron:5.8.38"
        dependency "org.apache.commons:commons-compress:1.26.0"
        dependency "org.apache.commons:commons-exec:1.4.0"
        dependency "cn.keepbx.jpom:jpom-core-common:1.0.9"
        dependency "org.projectlombok:lombok:1.18.36"
    }
}

// Java 版本配置
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

// 编译配置
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

// 依赖配置
dependencies {
    // Hutool 工具包 - 核心工具类
    implementation 'cn.hutool:hutool-core'

    // SLF4J 日志 API - 可选依赖
    compileOnly 'org.slf4j:slf4j-api'

    // FastJSON2 - 可选依赖
    compileOnly 'com.alibaba.fastjson2:fastjson2'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
}

// 打包配置
jar {
    archiveBaseName = 'agent-transport-common'
    manifest {
        attributes(
            'Implementation-Title': project.description,
            'Implementation-Version': project.version,
            'Built-By': System.getProperty('user.name'),
            'Built-Date': new Date().format('yyyy-MM-dd HH:mm:ss'),
            'Built-JDK': System.getProperty('java.version')
        )
    }
}

// 源码 jar
task sourcesJar(type: Jar) {
    from sourceSets.main.allSource
    archiveClassifier = 'sources'
}

// Javadoc jar
task javadocJar(type: Jar) {
    from javadoc
    archiveClassifier = 'javadoc'

    // Javadoc 配置
    javadoc {
        options.encoding = 'UTF-8'
        options.charSet = 'UTF-8'
        options.addStringOption('Xdoclint:none', '-quiet')

        // 自定义标签支持
        options.tags = [
            'date:a:创建时间'
        ]
    }
}

// 构建时包含源码和文档 jar
artifacts {
    archives sourcesJar
    archives javadocJar
}

// 测试配置
test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}
