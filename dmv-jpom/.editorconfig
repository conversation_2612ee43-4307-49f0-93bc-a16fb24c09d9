#
# Copyright (c) 2019 Of Him Code Technology Studio
# Jpom is licensed under Mulan PSL v2.
# You can use this software according to the terms and conditions of the Mulan PSL v2.
# You may obtain a copy of Mulan PSL v2 at:
# 			http://license.coscl.org.cn/MulanPSL2
# THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
# See the Mulan PSL v2 for more details.
#

# http://editorconfig.org

root = true

[*]
indent_style = tab
charset = utf-8
end_of_line = lf
trim_trailing_whitespace = true
insert_final_newline = true
max_line_length = 200

[*.{json,yml}]
indent_style = space
indent_size = 2
max_line_length = off

[*.md]
insert_final_newline = false
trim_trailing_whitespace = false
max_line_length = off
indent_style = space
indent_size = 2

[*.bat]
end_of_line = crlf

[*.sh]
end_of_line = lf
indent_style = space
indent_size = 2
max_line_length = off

[*.{vue,js,ts}]
indent_style = space
indent_size = 2

[*.ts]
indent_style = space
indent_size = 2

[*.{java,xml,sql}]
indent_style = space
indent_size = 4
