#
# Copyright (c) 2019 Of Him Code Technology Studio
# Jpom is licensed under Mulan PSL v2.
# You can use this software according to the terms and conditions of the Mulan PSL v2.
# You may obtain a copy of Mulan PSL v2 at:
# 			http://license.coscl.org.cn/MulanPSL2
# THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
# See the Mulan PSL v2 for more details.
#

version: '1.0'
name: pipeline-20220528
displayName: docker-hub
triggers:
  trigger: manual
  push:
    branches:
      precise:
        - dev
    tags:
      prefix:
        - v
stages:
  - name: stage-1b917201
    displayName: 容器发布
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: execute@docker
        name: execute_by_docker
        displayName: 基于镜像的脚本执行
        image: hub.docker.com/bash:latest
        command:
          - echo 'success!'
          - '# 服务端'
          - docker buildx build --platform linux/amd64,linux/arm64 -t jpomdocker/jpom:2.8.22 -f ./modules/server/DockerfileRelease --push .
          - '#'
          - docker buildx build --platform linux/amd64,linux/arm64 -t jpomdocker/jpom:latest -f ./modules/server/DockerfileRelease --push .
        strategy:
          retry: '0'
strategy:
  blocking: true
permissions:
  - role: admin
    members: []
