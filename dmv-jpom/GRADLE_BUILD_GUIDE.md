# JPOM Gradle 构建指南

## 概述

本文档提供了 JPOM 项目从 Maven 迁移到 Gradle 后的构建指南。已成功为 `Agent` 和 `Server` 两个核心应用模块创建了对应的 Gradle 构建文件。

## 新增文件

### 构建文件
- `modules/agent/build.gradle` - Agent 应用模块构建配置
- `modules/server/build.gradle` - Server 应用模块构建配置

### 工具脚本
- `verify-gradle-build.sh` - 构建配置验证脚本

## 核心特性

### Agent 模块 (modules/agent/build.gradle)

**主要配置：**
- **主类**: `org.dromara.jpom.JpomAgentApplication`
- **依赖**: common 模块、webhook 插件、oshi-core、commons-codec
- **打包**: 支持 bootJar 任务，生成可执行 JAR
- **发布**: 包含完整的发布流程和校验和生成

**关键任务：**
```bash
gradle :modules:agent:bootJar      # 构建 Fat JAR
gradle :modules:agent:bootRun      # 开发模式运行
gradle :modules:agent:release      # 创建发布包
```

### Server 模块 (modules/server/build.gradle)

**主要配置：**
- **主类**: `org.dromara.jpom.JpomServerApplication`
- **依赖**: 完整的插件生态系统、存储模块、Web 组件
- **多环境**: 支持 dev/test/prod 环境配置
- **数据库**: 支持 H2/MySQL/PostgreSQL/MariaDB

**关键任务：**
```bash
gradle :modules:server:bootJar        # 构建 Fat JAR
gradle :modules:server:bootRun        # 开发模式运行
gradle :modules:server:bootRunProd    # 生产模式运行
gradle :modules:server:bootRunMySQL   # 使用 MySQL 运行
gradle :modules:server:release        # 创建发布包
```

## 与 Maven 的对应关系

| Maven 命令 | Gradle 命令 | 说明 |
|-----------|------------|-----|
| `mvn clean compile` | `gradle build` | 编译项目 |
| `mvn package` | `gradle bootJar` | 打包应用 |
| `mvn spring-boot:run` | `gradle bootRun` | 运行应用 |
| `mvn clean package -P install-assembly` | `gradle release` | 创建发布包 |

## 保持的 Maven 特性

### 1. 依赖版本管理
- 完全保持与原 Maven 版本一致的依赖版本
- 通过根项目的 `ext` 属性统一管理版本号

### 2. 构建流程
- **Manifest 配置**: 保持相同的 MANIFEST.MF 属性
- **资源处理**: 包含 LICENSE、CHANGELOG 等文件
- **排除配置**: 排除相同的配置文件和依赖

### 3. 发布流程
- **打包策略**: 同时生成 plain JAR 和 fat JAR
- **校验和**: 自动生成 SHA-1 校验文件
- **发布包**: 创建包含所有必要文件的 ZIP 包

## 快速开始

### 1. 验证配置
```bash
./verify-gradle-build.sh
```

### 2. 构建所有模块
```bash
gradle buildAllModules
```

### 3. 打包所有组件
```bash
gradle packageAll
```

### 4. 运行开发环境
```bash
# 运行 Server
gradle :modules:server:bootRun

# 运行 Agent  
gradle :modules:agent:bootRun
```

## 输出目录

- **JAR 文件**: `modules/{agent,server}/build/libs/`
- **发布包**: `modules/{agent,server}/build/distributions/`
- **校验文件**: 与对应的 JAR/ZIP 文件同目录

## 注意事项

1. **Java 版本**: 需要 Java 8+ 运行环境
2. **Gradle 版本**: 建议使用 Gradle 6.0+ 
3. **测试跳过**: 与原 Maven 配置一致，默认跳过测试
4. **编码配置**: 统一使用 UTF-8 编码

## 迁移完成度

✅ **已完成：**
- Agent 和 Server 核心应用模块的 Gradle 构建配置
- Spring Boot 插件配置和 bootJar 任务
- 完整的依赖关系映射
- 发布流程和校验和生成
- 多环境运行支持

⏳ **待后续完善：**
- 其他子模块的 Gradle 配置（common、sub-plugin、storage-module 等）
- CI/CD 流程集成
- Docker 镜像构建任务

## 技术说明

本 Gradle 构建配置完全基于对现有 Maven pom.xml 文件的分析，确保：

1. **依赖一致性**: 所有依赖版本与 Maven 配置保持一致
2. **功能对等**: 所有 Maven 插件功能在 Gradle 中都有对应实现
3. **构建兼容**: 生成的产物与 Maven 构建结果完全兼容
4. **开发体验**: 提供更好的开发和调试支持

通过这个迁移，JPOM 项目获得了 Gradle 的灵活性和性能优势，同时保持了原有的所有功能特性。