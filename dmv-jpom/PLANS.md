# 开发计划

## 2.11.x

1. **构建流水线**
2. **netty-agent**
3. 凭证管理
4. 升级 JDK 11 或者 17
5. 端口监控、监控报警、机器监控、ssh 监控报警
6. 资产监控
7. nginx 流量切换（nginx 功能可能下线）
8. acme.sh ui
9. 执行审计
10. 执行部分命令耗时和直接执行相差太大
11. **非 root 用户提升权限写入 root 用户文件**
12. 部分数据迁移工作空间（~~项目~~，构建，仓库、节点分发）
13. 前端表格用户自定义列显示
14. 节点取消，白名单配置和下载白名单（统一到服务端工作空间配置）
15. 隧道节点
16. docker-compose       sh
17. 监控通知模块优化支持更多（飞书）    zx
18. 数据库支持 mariadb

## 2.10.x

1. ~~前端升级 vue3~~
2. `导入云效仓库 (zx) 依赖太重，非单接口实现（需要标准验证流程）`
3. ~~仓库~~、构建、分发、~~项目~~导入导出
4. ~~docker 容器编辑重建(zx)~~
5. ~~前端主题切换~~
6. ~~仓库密码、ssh 密码引用环境变量支持使用下拉框   sh~~
7. ~~SSH 修改文件权限    zx~~
8. ~~vue3 资产管理       zs~~
9. ~~vue3 用户管理       zs~~

### DONE

1. ~~SSH 连接 docker (sh)~~
2. ~~批量删除镜像 (sh)~~
3. **资产管理**
    1. ~~机器管理~~
    2. ~~机器监控~~
    3. ~~SSH 管理~~
    4. ~~dokcer 管理~~
    5. ~~docker 集群~~
    6. ~~ssh 监控~~
    7. ~~仓库管理（待定，可以和凭证管理一起考虑）~~
4. ~~使用服务端的 git 插件~~
5. ~~日志组件卡顿~~
6. ~~清理触发器表~~
7. ~~scp 发布实践案例~~
8. ~~SSH 上传文件进度（前端分片+进度）~~
9. ~~**用户体系支持接入第三方系统**~~
10. ~~传输信息加密（混淆，避免 http 明文传输）~~
11. ~~插件端证书验证迁移到服务端~~
12. ~~稳定版/体验版~~
13. ~~插件端自定义发布文件~~
14. ~~容器构建基础镜像的管理~~
15. ~~tomcat 实践案例~~
16. ~~**分片上传文件**~~
17. ~~**支持 mysql 数据库**~~
18. ~~配置文件优化~~
19. ~~项目触发器~~
20. ~~节点转发模块优化~~
21. ~~构建事件触发新增更多（前后）~~
22. ~~复制项目~~
23. ~~测命令行参数~~
24. ~~标签页缓存问题（定时器未清空）~~
25. ~~发布到指定目录~~

## 2.8.x

1. ~~h2 数据库升级 2.0~~
2. ~~文件管理支持备份~~
3. ~~节点工作空间变量~~
4. ~~操作监控优化~~
5. ~~节点日志文件搜索~~
6. ~~监控 webhook~~
7. ~~构建判断仓库代码是否变动~~
8. ~~批量执行节点脚本~~

## 2.8.x
--

1. ~~ssh 批量执行命令~~
2. ~~容器编排~~
3. ~~自定义检查项目状态~~
	1. `for /f "tokens=1 delims= " %i in ('jps -l ^| findstr "JpomServer"') do @echo %i`
4. ~~自定义启动项目脚本~~
5. ~~快速安装导入插件端~~
6. ~~容器构建~~
7. ~~docker ui~~
8. ~~节点大屏~~
9. ~~实时阅读日志文件~~
10. ~~配置分发~~
11. ~~修改数据库密码~~

## 2.7.x

1. ~~重构用户角色（计划引入工作空间）~~
2. ~~数据导入导出~~

## 2.6.0

1. ~~取消thymeleaf~~
2. ~~git 证书拉取代码~~
3. ~~新增代码仓库（构建）~~
4. ~~节点管理页面支持刷新当前节点页面~~
5. ~~项目日志输出路径自定义~~
6. ~~节点分发功能增加SSH部署-按指定地址下载应用~~
7. ~~项目文件管理支持远程下载~~
8. ~~jwt 更换~~
9. ~~压缩工具类优化~~

## 2.5.1

1. ~~token 机制问题（@ArnoHand 4月4日前）~~
2. ~~控制台文件太大问题（@蒋小小 4月4日前）~~
3. ~~IP白名单（@蒋小小 4月4日前）~~
4. ~~开机自动启动-文档（@Hotstrip 4月10日前）~~

======================

## 2.5.2

1. ~~agent统一升级管理（@蓝枫 4月17日前）~~
2. ~~git 证书拉取代码~~
3. ~~项目列表新增运行方式筛选~~
4. ~~及时刷新菜单~~
5. ~~远程升级（jpom 官方服务）~~

======================

* 主要管理页面兼容移动端
* ssl 到期提醒、快捷续签
* Tomcat管理优化
	* 优化启动(期望能使用到tomcat原生配置文件中的参数)
	* 在线修改配置
* ~~支持docker容器部署~~
* ~~jdk选择~~
* ~~开机自动启动~~
* ~~IP白名单~~
* ~~socket 日志~~
* ~~单节点支持项目副本~~
* 构建发布到同一个节点多个项目
* ~~集群环境自动注册节点~~
* ~~文件管理优化~~
* ~~agent统一升级管理~~
* ~~ssh文件管理（还原，之前有pr被合并错了）~~


* ~~用户权限~~
* ~~数据存储~~
* ~~账号密码安全性~~

--------------------

* Jpom 接口文档整理
* ~~部分服务器ssh不能退格~~
* ~~Token 机制问题~~
* ~~控制台日志文件过大~~
   
