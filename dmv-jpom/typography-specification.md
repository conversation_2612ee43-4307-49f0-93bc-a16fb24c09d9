# 中英文排版规范

## 编写目的

统一团队成员的书写风格与规范，增强对外网站用户的浏览体验。



## 和其他排版指南的区别

市面上已经有很多相关的排版指南了，但是我个人阅读后感觉都不是特别好，要么就是一个点一个点的列举，然后下面跟着一大堆例子，这样不方便我们查阅某个点；要么就是只列出了点，没有具体的例子和说明，读完之后还是不知所以然。本指南整合了这两种的优缺点，先把点列出来，可以方便查阅，如果想查看具体的例子可以点击超链接跳转，或者在文章的末尾查看例子和说明。

**注意：本文所有的超链接都可以进行点击，点击后可以跳转到对应的示例或者原文。**

文章的末尾是编写本文所有参考过的排版指南以及资料，感谢前辈们的贡献。



## 正确使用单词的大小写

以下例子均为**正例**，下面列出经常用错的大小写单词。

在使用的时候，如果您不确定单词正确的拼写，请前往官网查看。

- [Java](https://www.oracle.com/java/technologies/downloads/)
- [JDK](https://www.oracle.com/java/technologies/downloads/)
- [OpenJDK](https://openjdk.java.net/)
- [GitHub](https://github.com/)
- [GitLab](https://gitlab.com/)
- [HTML](https://developer.mozilla.org/en-US/docs/Learn/Getting_started_with_the_web/HTML_basics)
- [CSS](https://www.w3schools.com/css/)
- [JavaScript](https://www.javascript.com/)
- [Spring](https://spring.io/)
- [MyBatis](https://mybatis.org/)
- [MyBatis-Plus](https://baomidou.com/)
- [Android](https://www.android.com/)
- [iOS](https://www.apple.com/ios/ios-15/)
- [Google](https://www.google.com/)
- [Docker](https://www.docker.com/)

> 备注：
>
> 在 Docker 的 logo 中，使用的是小写的 docker，但是在文档中均使用的首字母大写的 Docker。可以参考 GitLab 官方中通过 Docker 方式安装 GitLab 的文档：[https://docs.gitlab.com/ee/install/docker.html](https://docs.gitlab.com/ee/install/docker.html)



## 空格的使用

### 加空格的情况

- [英文半角句号后接单词要加空格](#英文半角句号后接单词要加空格)
- [英文半角逗号后接单词要加空格](#英文半角逗号后接单词要加空格)
- [英文半角小括号前后都要加空格](#英文半角小括号前后都要加空格)
- [中英文之间要加空格](#中英文之间要加空格)
- [中文与数字之间要加空格](#中文与数字之间要加空格)
- [数字与单位之间要加空格](#数字与单位之间要加空格)
- [超链接英文文本前后都要加空格](#超链接英文文本前后都要加空格)

- [英文单词和英文单词之间要有一个空格](#英文单词和英文单词之间要有一个空格)
- [中文和英文单词之间要有一个空格](#中文和英文单词之间要有一个空格)

### 不加空格的情况

- [中文全角标点符号前后都不加空格](#中文全角标点符号前后都不加空格)

- [数字与度之间不加空格](#数字与度之间不加空格)
- [数字与百分比之间不加空格](#数字与百分比之间不加空格)
- [全角标点与其他字符之间不加空格](#全角标点与其他字符之间不加空格)
- [英文单词和半角句号之间不加空格](#英文单词和半角句号之间不加空格)
- [超链接中文文本前后都不加空格](#超链接中文文本前后都不加空格)



## 标点符号的使用

- [中文句子中使用破折号 ——](#中文句子中使用破折号 ——)
- [不连续使用相同标点符号](#不连续使用相同标点符号)
- [中文句子中使用全角标点符号](#中文句子中使用全角标点符号)
- [英文句子中使用半角标点符号](#英文句子中使用半角标点符号)
- [中英文混用时使用全角标点符号](#中英文混用时使用全角标点符号)
- [数字使用半角字符](#数字使用半角字符)
- [遇到完整的英文整句、特殊名词，其内容使用半角标点](#遇到完整的英文整句、特殊名词，其内容使用半角标点)



## 参考例子

下面的例子可以帮助您进一步理解对应的规范，以及为什么要有这样的规范，大公司的网站是怎么用到的。

### 空格的使用

#### 加空格的情况

##### 英文半角句号后接单词要加空格

正例：

```
GitHub is a code hosting platform for version control and collaboration. It lets you and others work together on projects from anywhere.
```

反例：

```
GitHub is a code hosting platform for version control and collaboration.It lets you and others work together on projects from anywhere.
```

> 参考资料：[https://docs.github.com/en/get-started/quickstart/hello-world](https://docs.github.com/en/get-started/quickstart/hello-world)



##### 英文半角逗号后接单词要加空格

正例：

```
You can download, install and maintain your own GitLab instance.
```

反例：

```
You can download,install and maintain your own GitLab instance.
```

> 参考资料：[https://about.gitlab.com/install/](https://about.gitlab.com/install/)



##### 英文半角小括号前后都要加空格

正例：

```
Virtual machines (VMs) are an abstraction of physical hardware turning one server into many servers.
```

反例：

```
Virtual machines(VMs)are an abstraction of physical hardware turning one server into many servers.
```

> 参考资料：[https://www.docker.com/resources/what-container/](https://www.docker.com/resources/what-container/)



##### 中英文之间要加空格

正例：

```
将使用情况统计信息和崩溃报告自动发送给 Google，帮助我们完善 Google Chrome。
```

反例：

```
将使用情况统计信息和崩溃报告自动发送给 Google，帮助我们完善 Google Chrome。
```

> 参考资料：[https://www.google.com/intl/zh-CN/chrome/](https://www.google.com/intl/zh-CN/chrome/)



##### 中文与数字之间要加空格

正例：

```
适用于 Windows 11/10/8.1/8/7 64 位。
```

反例：

```
适用于 Windows 11/10/8.1/8/7 64 位。
```

> 参考资料：[https://www.google.com/intl/zh-CN/chrome/](https://www.google.com/intl/zh-CN/chrome/)



##### 数字与单位之间要加空格

正例：

```
The Omnibus GitLab package requires about 2.5 GB of storage space for installation.
```

> 说明：数字 2.5 和单位 GB 之间需要添加一个空格。

反例：

```
The Omnibus GitLab package requires about 2.5GB of storage space for installation.
```

> 参考资料：[https://docs.gitlab.com/ee/install/requirements.html](https://docs.gitlab.com/ee/install/requirements.html)



##### 超链接英文文本前后都要加空格

正例：

```
如果您是来自 React 的开发者，您可能会对 Vuex 和 [Redux](https://github.com/reactjs/redux) 间的差异表示关注，Redux 是 React 生态环境中最流行的 Flux 实现。
```

正例显示效果：如果您是来自 React 的开发者，您可能会对 Vuex 和 [Redux](https://github.com/reactjs/redux) 间的差异表示关注，Redux 是 React 生态环境中最流行的 Flux 实现。

反例：

```
如果您是来自 React 的开发者，您可能会对 Vuex 和[Redux](https://github.com/reactjs/redux)间的差异表示关注，Redux 是 React 生态环境中最流行的 Flux 实现。
```

反例显示效果：如果您是来自 React 的开发者，您可能会对 Vuex 和[Redux](https://github.com/reactjs/redux)间的差异表示关注，Redux 是 React 生态环境中最流行的 Flux 实现。

> 参考资料：[https://docs.github.com/en/rest/overview/resources-in-the-rest-api](https://docs.github.com/en/rest/overview/resources-in-the-rest-api)



##### 英文单词和英文单词之间要有一个空格

正例：

```
Hello World!
```

反例：

```
HelloWorld!
```



##### 中文和英文单词之间要有一个空格

正例：

```
通过 Gmail、Google Pay 和 Google 助理等 Google 应用，Chrome 可帮助您保持工作效率并充分利用您的浏览器。
```

反例：

```
通过Gmail、Google Pay和Google助理等Google应用，Chrome可帮助您保持工作效率并充分利用您的浏览器。
```

> 参考资料：[https://www.google.com/intl/zh-CN/chrome/](https://www.google.com/intl/zh-CN/chrome/)



#### 不加空格的情况

##### 中文全角标点符号前后都不加空格

正例：

```
您可为标签页分组，以便将彼此相关的网页保存在同一个工作区内。如需创建标签页组，只需右键点击任一标签页并选择“向新组添加标签页”即可。
```

> 参考资料：[https://www.google.com/intl/zh-CN/chrome/tips/](https://www.google.com/intl/zh-CN/chrome/tips/)



##### 数字与度之间不加空格

正例：

```
角度为 90° 的角，就是直角。
```

> 说明：90° 是一个整体，不应该用空格分开。

反例：

```
角度为 90 ° 的角，就是直角。
```



##### 数字与百分比之间不加空格

正例：

```
我现在手机的电量是 100%。
```

反例：

```
我现在手机的电量是 100 %。
```



##### 全角标点与其他字符之间不加空格

正例：

```
期末考试通过了，好开心！
```

反例：

```
期末考试通过了， 好开心！
```



##### 英文单词和半角句号之间不加空格

正例：

```
All Roads Lead to Rome.
```

反例：

```
All Roads Lead to Rome .
```



##### 超链接中文文本前后都不加空格

正例：

```
Redux 事实上无法感知视图层，所以它能够轻松的通过一些[简单绑定](https://classic.yarnpkg.com/en/packages?q=redux%20vue&p=1)和 Vue 一起使用。
```

正例显示效果：Redux 事实上无法感知视图层，所以它能够轻松的通过一些[简单绑定](https://classic.yarnpkg.com/en/packages?q=redux%20vue&p=1)和 Vue 一起使用。

反例：

```
Redux 事实上无法感知视图层，所以它能够轻松的通过一些 [简单绑定](https://classic.yarnpkg.com/en/packages?q=redux%20vue&p=1) 和 Vue 一起使用。
```

反例显示效果：Redux 事实上无法感知视图层，所以它能够轻松的通过一些 [简单绑定](https://classic.yarnpkg.com/en/packages?q=redux%20vue&p=1) 和 Vue 一起使用。

> 参考资料：[https://cn.vuejs.org/v2/guide/state-management.html](https://cn.vuejs.org/v2/guide/state-management.html)



### 标点符号的使用

#### 中文句子中使用破折号 ——

正例：

```
我们刚才简单介绍了 Vue 核心最基本的功能——本教程的其余部分将更加详细地涵盖这些功能以及其它高级功能，所以请务必读完整个教程！
```

> 说明：破折号占两个字的位置，中间不能断开，上下居中。

反例：

```
我们刚才简单介绍了 Vue 核心最基本的功能—本教程的其余部分将更加详细地涵盖这些功能以及其它高级功能，所以请务必读完整个教程！
```

> 参考资料：[https://cn.vuejs.org/v2/guide/](https://cn.vuejs.org/v2/guide/)



#### 不连续使用相同标点符号

正例：

```
如果您刚开始学习前端开发，将框架作为您的第一步可能不是最好的主意——掌握好基础知识再来吧！
```

反例：

```
如果您刚开始学习前端开发，将框架作为您的第一步可能不是最好的主意——掌握好基础知识再来吧！！！
```

> 说明：有时我们在书写的时候想进行感叹，可能会情不自禁地连续使用了相同的标点符号，但是请克制住自己，不要连续使用相同的标点符号。

> 参考资料：[https://cn.vuejs.org/v2/guide/#Vue-js-%E6%98%AF%E4%BB%80%E4%B9%88](https://cn.vuejs.org/v2/guide/#Vue-js-%E6%98%AF%E4%BB%80%E4%B9%88)



#### 中文句子中使用全角标点符号

正例 1：

```
借助个人资料，您可单独保存自己的所有 Chrome 信息，例如书签、历史记录、密码及其他设置。个人资料最适合用于以下情况：多人共用一台计算机，或者您需要分隔自己的不同帐号（例如工作帐号和个人帐号）。
```

> 参考资料：[https://www.google.com/intl/zh-CN/chrome/browser-features/](https://www.google.com/intl/zh-CN/chrome/browser-features/)

正例 2：

```
您可以根据自己的需求或心情，选择主题和颜色（例如“深色模式”）。
```

> 参考资料：[https://www.google.com/intl/zh-CN/chrome/browser-features/](https://www.google.com/intl/zh-CN/chrome/browser-features/)



#### 英文句子中使用半角标点符号

正例：

```
You build it, you run it.
```

反例：

```
You build it，you run it。
```



#### 中英文混用时使用全角标点符号

正例：

```
通过 Gmail、Google Pay 和 Google 助理等 Google 应用，Chrome 可帮助您保持工作效率并充分利用您的浏览器。
```

> 参考资料：[https://www.google.com/intl/zh-CN/chrome/](https://www.google.com/intl/zh-CN/chrome/)



#### 数字使用半角字符

正例：

```
这个蛋糕价值 1000 元。
```

反例：

```
这个蛋糕只卖 １０００ 元。
```



#### 遇到完整的英文整句、特殊名词，其内容使用半角标点

正例：

```
乔布斯那句话是怎么说的？「Stay hungry, stay foolish.」
```

> 说明：中文句子使用中文全角标点符号，英文句子使用英文半角标点符号。





> 参考文献：
>
> [chinese-copywriting-guidelines](https://github.com/sparanoid/chinese-copywriting-guidelines)
>
> [Basic writing and formatting syntax - GitHub Docs](https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax)
>
> [写给大家看的中文排版指南](https://zhuanlan.zhihu.com/p/20506092)
>
> [Dubbo 博客文档中文排版指南](https://dubbo.apache.org/zh/blog/2018/01/01/dubbo-%E5%8D%9A%E5%AE%A2%E6%96%87%E6%A1%A3%E4%B8%AD%E6%96%87%E6%8E%92%E7%89%88%E6%8C%87%E5%8D%97/)
>
> [中文文本排版指南](https://zhiqiang.org/it/chinese-copywriting-guidelines.html)
>
> [Vue.js 内部文档规范](https://github.com/vuejs/cn.vuejs.org/wiki)



