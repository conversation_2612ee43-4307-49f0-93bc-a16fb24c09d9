/*
 * Copyright (c) 2019 Of Him Code Technology Studio
 * Jpom is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *             http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

plugins {
    // 不在根项目应用 java 插件，避免与子项目的 java-platform 插件冲突
    // 使用根项目定义的 Spring Boot 插件版本
    id 'org.springframework.boot' version '3.4.7' apply false
    id 'io.spring.dependency-management' version '1.1.4' apply false
}

// ========================================
// JPOM 项目集成说明
// ========================================
//
// JPOM (Java Project Online Manage) 是一个简而轻的低侵入式在线构建、自动部署、日常运维、项目监控软件
//
// 重要说明：JPOM 作为独立子模块集成到 plat_index 项目中
// - 版本管理：JPOM 使用自己的版本管理策略（当前版本 2.11.12）
// - 依赖管理：JPOM 保持独立的依赖版本控制，不受主项目版本约束影响
// - 构建系统：从 Maven 迁移到 Gradle，但保持原有的构建逻辑和配置
// - 部署策略：JPOM 可独立打包、部署，也可通过主项目统一管理
//
// 集成原则：
// 1. 保持 JPOM 的独立性和完整性
// 2. 不修改 JPOM 的核心业务逻辑
// 3. 遵循 Gradle 最佳实践，但尊重 JPOM 的特定需求
// 4. 提供与主项目的集成点，但不强制依赖
// ========================================

// JPOM 项目基本信息
group = 'org.dromara.jpom'
version = '2.11.12'
description = 'Jpom (Java Project Online Manage) - 简而轻的低侵入式在线构建、自动部署、日常运维、项目监控软件'

// Java 版本配置 - 升级到 Java 17 (在 subprojects 中配置)

// JPOM 特定的属性配置 - 已移动到 allprojects 配置中

// 前端构建任务配置
task buildFrontend(type: Exec) {
    group = 'build'
    description = '构建前端Vue3项目'

    workingDir file('web-vue')

    // 根据操作系统选择命令
    if (System.getProperty('os.name').toLowerCase().contains('windows')) {
        commandLine 'cmd', '/c', 'npm run build'
    } else {
        commandLine 'bash', '-c', 'npm run build'
    }

    // 输入：前端源代码
    inputs.dir file('web-vue/src')
    inputs.file file('web-vue/package.json')
    inputs.file file('web-vue/vite.config.ts')

    // 输出：构建后的静态资源
    outputs.dir file('modules/server/src/main/resources/dist')

    doFirst {
        println "🎨 开始构建前端项目..."

        // 检查Node.js环境
        try {
            if (System.getProperty('os.name').toLowerCase().contains('windows')) {
                exec {
                    commandLine 'cmd', '/c', 'node --version'
                }
            } else {
                exec {
                    commandLine 'node', '--version'
                }
            }
        } catch (Exception e) {
            throw new GradleException("❌ 未找到Node.js，请先安装Node.js 18+")
        }

        // 检查并安装依赖
        def nodeModules = file('web-vue/node_modules')
        if (!nodeModules.exists()) {
            println "📦 安装前端依赖..."
            if (System.getProperty('os.name').toLowerCase().contains('windows')) {
                exec {
                    workingDir file('web-vue')
                    commandLine 'cmd', '/c', 'npm install'
                }
            } else {
                exec {
                    workingDir file('web-vue')
                    commandLine 'bash', '-c', 'npm install'
                }
            }
        }
    }

    doLast {
        def distDir = file('modules/server/src/main/resources/dist')
        def indexFile = new File(distDir, 'index.html')
        if (indexFile.exists()) {
            println "✅ 前端构建成功！输出目录: ${distDir.absolutePath}"
        } else {
            throw new GradleException("❌ 前端构建失败！未找到index.html")
        }
    }
}

// 前端依赖安装任务
task installFrontendDeps(type: Exec) {
    group = 'build setup'
    description = '安装前端依赖'

    workingDir file('web-vue')

    if (System.getProperty('os.name').toLowerCase().contains('windows')) {
        commandLine 'cmd', '/c', 'npm install'
    } else {
        commandLine 'bash', '-c', 'npm install'
    }

    inputs.file file('web-vue/package.json')
    outputs.dir file('web-vue/node_modules')

    onlyIf {
        !file('web-vue/node_modules').exists()
    }
}

// 前端开发服务器任务
task serveFrontend(type: Exec) {
    group = 'application'
    description = '启动前端开发服务器'

    workingDir file('web-vue')

    if (System.getProperty('os.name').toLowerCase().contains('windows')) {
        commandLine 'cmd', '/c', 'npm run dev'
    } else {
        commandLine 'bash', '-c', 'npm run dev'
    }

    dependsOn installFrontendDeps
}

// 完整构建任务（前端+后端）
task buildAll {
    group = 'build'
    description = '完整构建JPOM项目（前端+后端）'

    dependsOn buildFrontend

    // 依赖所有子项目的build任务
    subprojects.each { subproject ->
        if (subproject.tasks.findByName('build')) {
            dependsOn "${subproject.path}:build"
            // 确保前端构建在后端构建之前完成
            tasks.getByPath("${subproject.path}:build").mustRunAfter buildFrontend
        }
    }

    doLast {
        println ""
        println "🎉 JPOM构建完成！"
        println ""
        println "📦 构建产物:"
        println "   Server端: modules/server/build/libs/server-${version}.jar"
        println "   Agent端:  modules/agent/build/libs/agent-${version}.jar"
        println ""
        println "🚀 部署说明:"
        println "   1. Server端包含完整的前端界面，默认端口: 2122"
        println "   2. Agent端为纯后端服务，默认端口: 2123"
        println "   3. 访问地址: http://your-server-ip:2122"
        println ""
        println "💡 快速启动:"
        println "   java -jar modules/server/build/libs/server-${version}.jar"
        println "   java -jar modules/agent/build/libs/agent-${version}.jar"
    }
}

// 清理前端构建产物任务
task cleanFrontend {
    group = 'build'
    description = '清理前端构建产物'

    doLast {
        delete file('web-vue/dist')
        delete file('web-vue/node_modules')
        delete file('modules/server/src/main/resources/dist')
        println "✅ 清理前端构建产物完成"
    }
}

// 全局配置
allprojects {
    group = rootProject.group
    version = rootProject.version

    // 确保所有项目都能访问版本变量
    ext {
        springFrameworkVersion = '6.2.7'
        springBootVersion = '3.4.7'
        hutoolVersion = '5.8.38'
        fastjson2Version = '2.0.53'
        fastjsonVersion = '2.0.53'
        jnaVersion = '5.14.0'
        mwiedeJschVersion = '0.2.18'
        oshiVersion = '6.6.5'
        tomcatVersion = '10.1.30'
        yamlVersion = '2.2'
        h2Version = '2.1.214'
        postgresqlVersion = '42.7.4'
        logbackVersion = '1.4.14'
        mysqlVersion = '8.2.0'
        jpomPluginVersion = '2.11.12'
    }

    // 编码配置
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }

    tasks.withType(Test) {
        systemProperty 'file.encoding', 'UTF-8'
        useJUnitPlatform()
    }

    repositories {
        // 使用国内镜像源提高下载速度
        maven {
            url 'https://maven.aliyun.com/repository/public'
        }
        maven {
            url 'https://maven.aliyun.com/repository/central'
        }
        maven {
            url 'https://maven.aliyun.com/repository/spring'
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

// 子项目通用配置
subprojects {
    // 只对 agent 和 server 应用 Spring Boot 插件
    if (project.path == ':modules:agent' || project.path == ':modules:server') {
        apply plugin: 'java'
        apply plugin: 'org.springframework.boot'
        apply plugin: 'io.spring.dependency-management'

        // 依赖管理 - 导入 Spring Boot BOM
        dependencyManagement {
            imports {
                mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
            }
            dependencies {
                // JPOM 特定依赖版本管理 - 直接指定版本而不是使用BOM
                dependency "cn.hutool:hutool-core:${hutoolVersion}"
                dependency "cn.hutool:hutool-http:${hutoolVersion}"
                dependency "cn.hutool:hutool-system:${hutoolVersion}"
                dependency "cn.hutool:hutool-extra:${hutoolVersion}"
                dependency "cn.hutool:hutool-crypto:${hutoolVersion}"
                dependency "cn.hutool:hutool-cron:${hutoolVersion}"
                dependency "cn.hutool:hutool-script:${hutoolVersion}"
                dependency "cn.hutool:hutool-jwt:${hutoolVersion}"
                dependency "cn.hutool:hutool-captcha:${hutoolVersion}"
                dependency "cn.hutool:hutool-cache:${hutoolVersion}"
                dependency "cn.hutool:hutool-db:${hutoolVersion}"
                dependency "com.alibaba.fastjson2:fastjson2:${fastjson2Version}"
                dependency "org.bouncycastle:bcprov-jdk18on:1.78"
                dependency "com.github.mwiede:jsch:${mwiedeJschVersion}"
                dependency "org.yaml:snakeyaml:${yamlVersion}"
                dependency "org.apache.tomcat.embed:tomcat-embed-core:${tomcatVersion}"
                dependency "org.apache.tomcat.embed:tomcat-embed-el:${tomcatVersion}"
                dependency "org.apache.tomcat.embed:tomcat-embed-websocket:${tomcatVersion}"
                dependency "com.h2database:h2:${h2Version}"
                dependency "org.postgresql:postgresql:${postgresqlVersion}"
            }
        }

        // 通用依赖
        dependencies {
            testImplementation 'junit:junit:4.13.2'
            testImplementation 'org.databene:contiperf:2.3.4'
            compileOnly 'org.projectlombok:lombok'
            annotationProcessor 'org.projectlombok:lombok'
            testCompileOnly 'org.projectlombok:lombok'
            testAnnotationProcessor 'org.projectlombok:lombok'
        }

        // 编译配置
        tasks.withType(JavaCompile) {
            options.encoding = 'UTF-8'
            sourceCompatibility = JavaVersion.VERSION_17
            targetCompatibility = JavaVersion.VERSION_17
        }

        // 测试配置
        test {
            enabled = false // 跳过测试，与原 Maven 配置保持一致
        }
    }
}

// 主要构建任务
task buildAllModules {
    group = 'build'
    description = '构建所有 JPOM 模块'
    // 简化配置，只构建主要的应用模块
    dependsOn ':modules:agent:build', ':modules:server:build'
}

task packageAgent {
    group = 'package'
    description = '打包 JPOM Agent'
    dependsOn ':modules:agent:build'
}

task packageServer {
    group = 'package'
    description = '打包 JPOM Server'
    dependsOn ':modules:server:build'
}

task packageAll {
    group = 'package'
    description = '打包所有 JPOM 组件'
    dependsOn packageAgent, packageServer

    doLast {
        println ''
        println '🎉 JPOM 打包完成!'
        println '📁 Agent 包位置: modules/agent/build/libs/'
        println '📁 Server 包位置: modules/server/build/libs/'
        println ''
        println '✅ 可用的 JPOM 组件:'

        def agentJar = file('modules/agent/build/libs').listFiles()?.find { it.name.endsWith('.jar') && !it.name.contains('plain') }
        def serverJar = file('modules/server/build/libs').listFiles()?.find { it.name.endsWith('.jar') && !it.name.contains('plain') }

        if (agentJar) {
            println "  - Agent: ${agentJar.name}"
        }
        if (serverJar) {
            println "  - Server: ${serverJar.name}"
        }
    }
}

// 清理任务
task cleanAll {
    group = 'build'
    description = '清理所有 JPOM 模块构建产物（包含前端）'
    // 清理主要的应用模块和前端
    dependsOn ':modules:agent:clean', ':modules:server:clean', 'cleanFrontend'
}

// 获取 Git 版本信息
def getGitVersion() {
    try {
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'describe', '--tags', '--always', '--dirty'
            standardOutput = stdout
            workingDir = projectDir
        }
        return stdout.toString().trim()
    } catch (Exception e) {
        return version
    }
}

// 版本信息任务
task showVersion {
    group = 'help'
    description = '显示 JPOM 版本信息'

    doLast {
        println "JPOM Version: ${version}"
        println "Git Version: ${getGitVersion()}"
        println "Java Version: ${System.getProperty('java.version')}"
        println "Gradle Version: ${gradle.gradleVersion}"
    }
}

// 复制 JPOM 构建产物到部署包目录（供 bat 调用）
task copyToDeployPackages {
    group = 'deploy'
    description = '复制 JPOM 构建产物到 ../dmv-deploy/packages 目录'
    dependsOn packageAll

    doLast {
        def deployPackagesDir = file("../dmv-deploy/packages")
        deployPackagesDir.mkdirs()

        // 复制 JPOM Agent
        def agentJarDir = file("modules/agent/build/libs")
        if (agentJarDir.exists()) {
            agentJarDir.listFiles().each { jarFile ->
                if (jarFile.name.endsWith('.jar') && !jarFile.name.contains('plain')) {
                    copy {
                        from jarFile
                        into deployPackagesDir
                        rename { "jpom-agent-${version}.jar" }
                    }
                    println "✅ 复制完成: jpom-agent-${version}.jar"
                }
            }
        }

        // 复制 JPOM Server
        def serverJarDir = file("modules/server/build/libs")
        if (serverJarDir.exists()) {
            serverJarDir.listFiles().each { jarFile ->
                if (jarFile.name.endsWith('.jar') && !jarFile.name.contains('plain')) {
                    copy {
                        from jarFile
                        into deployPackagesDir
                        rename { "jpom-server-${version}.jar" }
                    }
                    println "✅ 复制完成: jpom-server-${version}.jar"
                }
            }
        }

        println "📦 JPOM 包已复制到: ${deployPackagesDir.absolutePath}"
    }
}
