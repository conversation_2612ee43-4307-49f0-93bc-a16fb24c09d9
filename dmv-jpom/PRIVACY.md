# Jpom项目运维本地部署版隐私协议

**一、引言**

**1.1** 我们诚挚欢迎并感谢您选用开源软件——Jpom项目运维的本地部署版本（以下简称“本软件”）。本隐私协议旨在全面介绍和阐明我们在您本地部署和使用本软件时，关于数据处理的各种活动规范，覆盖但不限于数据的搜集、运用、存储和防护等各个阶段。敬请您在安装并启用本软件之前，务必详尽阅读并深刻理解本协议各项条款。

**二、数据处理与本地化规范**

**2.1** **本地数据操作**
本软件所有数据处理活动严格限制在您指定和管控的本地服务器或设备上执行，我们不会直接获取或远程访问这些数据资源。为保证软件的正常运行，可能需要处理如配置信息、错误日志、临时文件等本地生成的各类数据。

**2.2** **必要数据操作原则**
为确保软件基础功能稳定、性能卓越、技术问题有效解决以及优化用户使用体验，本软件可能会对上述部分或全部数据进行必要的处理。请注意，此类数据操作完全局限于本地环境，并严格遵循软件设计的规范与标准。

**三、开源与透明度**

**3.1** 本软件基于 MulanPSL2 开源许可协议发布，其源代码面向公众开放，意味着您有权审查软件源码以验证其数据处理行为符合您的隐私期待。我们积极倡议用户共同参与和监督，共同营造一个高度尊重数据隐私的应用生态体系。

**四、数据主权与控制**

**4.1** 用户对其在本地部署环境中生成的所有数据享有完整的控制权和所有权，这涵盖了从数据的创建、修改、删除到数据传输方式及存储地点的决定权。

**五、数据安全保护**

**5.1** 尽管本软件自带一定的内在安全机制，但用户仍需自行负责在本地部署环境中实施高效的数据安全措施，包括但不限于数据加密、访问控制、安全审计及数据备份策略等。对于因用户本地部署环境引发的数据安全事件，我们不承担直接法律责任。

**六、数据交互与隐私承诺**

本软件坚守最高的数据隐私保护原则，明确规定未经用户明确授权，本软件决不会主动对外向任何第三方透露您系统内的任何敏感信息。

我们尤为重视并全力支持用户对数据安全性的深度审视诉求，确保每位用户有权详尽审查软件源代码，以亲手验证我们在数据处理与安全防护措施层面的严密性和合规性。
我们通过这种开诚布公、负责任的方式，共同致力于最大程度地保障用户数据安全与隐私不受侵犯。

**6.1** 个人信息收集政策

我们严格遵循不收集原则，除非必要，否则不涉及收集用户的个人身份识别信息，例如姓名、电子邮件地址、电话号码、身份证件号码等。

**6.2** 可能收集的数据类别

在您使用我们产品和服务的过程中，为确保正常运行和提供更好的服务体验，我们有可能会收集与产品使用紧密相关的一些必要信息，包括但不限于IP地址、Cookies、页面路由、地理位置信息、设备制造商、设备型号、设备唯一标识符以及设备相关数据等。

**6.3** 数据信息处理方式

在必须收集相关信息的情况下，我们会在遵循相关法律法规及本隐私协议的基础上，将这些数据传输至我们的官方服务器（https://jpom.top），并在严格的规则约束下进行管理和使用。

**七、法律合规与用户责任**

**7.1** 在本软件的研发和运营过程中，我们严格遵守中国各地关于数据保护和隐私权的各项法律法规。

**7.2** 用户在部署和使用本软件时，同样需遵循相关法律法规，承诺在使用过程中秉持合法、合理原则，不得利用本软件从事非法行为、侵犯他人合法权益，也不得在违反我国法律法规的项目或平台上部署和使用本软件。

**八、隐私政策更新**

**8.1** 我们保留在必要时适时更新和完善本隐私协议的权利，并将通过官方网站、软件更新公告或其他醒目位置公布修订内容。对于重要的政策变更，我们将提前通知用户，用户在变更后继续使用本软件即视作已同意并接受新的隐私协议。