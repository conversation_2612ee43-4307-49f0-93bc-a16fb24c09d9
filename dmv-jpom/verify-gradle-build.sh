#!/bin/bash

# JPOM Gradle 构建验证脚本
# 验证 Agent 和 Server 模块的 Gradle 构建配置

echo "🔍 验证 JPOM Gradle 构建配置..."
echo "================================="

# 检查基础文件
echo "📝 检查构建文件..."
if [ -f "build.gradle" ]; then
    echo "✅ 根目录 build.gradle 存在"
else
    echo "❌ 根目录 build.gradle 不存在"
    exit 1
fi

if [ -f "settings.gradle" ]; then
    echo "✅ settings.gradle 存在"
else
    echo "❌ settings.gradle 不存在"
    exit 1
fi

if [ -f "modules/agent/build.gradle" ]; then
    echo "✅ Agent 模块 build.gradle 存在"
else
    echo "❌ Agent 模块 build.gradle 不存在"
    exit 1
fi

if [ -f "modules/server/build.gradle" ]; then
    echo "✅ Server 模块 build.gradle 存在"
else
    echo "❌ Server 模块 build.gradle 不存在"
    exit 1
fi

echo ""
echo "📊 构建文件统计信息："
echo "- 根目录 build.gradle: $(wc -l < build.gradle) 行"
echo "- Agent build.gradle: $(wc -l < modules/agent/build.gradle) 行"
echo "- Server build.gradle: $(wc -l < modules/server/build.gradle) 行"

echo ""
echo "🔧 关键配置检查..."

# 检查 Agent 配置
echo "📋 Agent 模块配置："
if grep -q "org.dromara.jpom.JpomAgentApplication" modules/agent/build.gradle; then
    echo "  ✅ 主类配置正确"
else
    echo "  ❌ 主类配置缺失"
fi

if grep -q "bootJar" modules/agent/build.gradle; then
    echo "  ✅ bootJar 任务配置存在"
else
    echo "  ❌ bootJar 任务配置缺失"
fi

# 检查 Server 配置
echo "📋 Server 模块配置："
if grep -q "org.dromara.jpom.JpomServerApplication" modules/server/build.gradle; then
    echo "  ✅ 主类配置正确"
else
    echo "  ❌ 主类配置缺失"
fi

if grep -q "bootJar" modules/server/build.gradle; then
    echo "  ✅ bootJar 任务配置存在"
else
    echo "  ❌ bootJar 任务配置缺失"
fi

echo ""
echo "📦 依赖关系检查..."

# 检查关键依赖
if grep -q "project(':modules:common')" modules/agent/build.gradle; then
    echo "  ✅ Agent 依赖 common 模块"
else
    echo "  ⚠️  Agent 可能缺少 common 模块依赖"
fi

if grep -q "project(':modules:common')" modules/server/build.gradle; then
    echo "  ✅ Server 依赖 common 模块"
else
    echo "  ⚠️  Server 可能缺少 common 模块依赖"
fi

echo ""
echo "🎯 可用的构建任务："
echo "  gradle :modules:agent:bootJar     - 构建 Agent Fat JAR"
echo "  gradle :modules:server:bootJar    - 构建 Server Fat JAR"
echo "  gradle :modules:agent:release     - 创建 Agent 完整发布包"
echo "  gradle :modules:server:release    - 创建 Server 完整发布包"
echo "  gradle packageAll                 - 打包所有组件"
echo "  gradle buildAllModules            - 构建所有模块"

echo ""
echo "✨ 开发模式运行："
echo "  gradle :modules:agent:bootRun     - 运行 Agent（开发模式）"
echo "  gradle :modules:server:bootRun    - 运行 Server（开发模式）"
echo "  gradle :modules:server:bootRunProd - 运行 Server（生产模式）"

echo ""
echo "🗂️  构建输出位置："
echo "  Agent JAR: modules/agent/build/libs/"
echo "  Server JAR: modules/server/build/libs/"
echo "  发布包: modules/{agent,server}/build/distributions/"

echo ""
echo "🎉 Gradle 构建配置验证完成！"
echo ""
echo "💡 使用建议："
echo "1. 确保已安装 Java 8+ 和 Gradle 6+"
echo "2. 首次构建前运行: gradle build"
echo "3. 如需完整发布包，运行: gradle packageAll"
echo "4. 开发调试时使用: gradle :modules:server:bootRun"