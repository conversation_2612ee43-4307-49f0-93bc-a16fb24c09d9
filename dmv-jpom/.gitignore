# Created by .ignore support plugin (hsz.mobi)
target/
.idea/
*.iml
venv/
node_modules/

# Jrebel
rebel.xml

#
apidoc/


# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
dist/
#/.vscode

# misc
.DS_Store
npm-debug.log*
yarn-error.log

/coverage
.idea
yarn.lock
package-lock.json
pnpm-lock.yaml
*bak
pom.xml.versionsBackup
#.vscode

# visual studio code
.history
*.log
functions/*
.temp/**

# umi
.umi
.umi-production

# screenshot
screenshot
.firebase
.eslintcache

web-vue/stats.html
.baidubce.token
*.class
