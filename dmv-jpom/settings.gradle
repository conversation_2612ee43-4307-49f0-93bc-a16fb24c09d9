/*
 * Copyright (c) 2019 Of Him Code Technology Studio
 * Jpom is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *             http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

rootProject.name = 'jpom-parent'

// 包含所有子模块 - 与原 Maven 结构保持一致
include 'modules:agent'
include 'modules:server' 
include 'modules:common'
include 'modules:sub-plugin'
include 'modules:agent-transport'
include 'modules:storage-module'

// Sub-plugin 子模块
include 'modules:sub-plugin:webhook'
include 'modules:sub-plugin:svn-clone'
include 'modules:sub-plugin:ssh-jsch'
include 'modules:sub-plugin:git-clone'
include 'modules:sub-plugin:encrypt'
include 'modules:sub-plugin:email'
include 'modules:sub-plugin:docker-cli'

// Storage 子模块
include 'modules:storage-module:storage-module-postgresql'
include 'modules:storage-module:storage-module-mysql'
include 'modules:storage-module:storage-module-mariadb'
include 'modules:storage-module:storage-module-h2'
include 'modules:storage-module:storage-module-common'

// Agent Transport 子模块
include 'modules:agent-transport:agent-transport-http'
include 'modules:agent-transport:agent-transport-common'