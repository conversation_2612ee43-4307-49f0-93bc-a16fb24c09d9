<template>
  <div class="init-wrapper" :style="backgroundImage">
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 1440 500"
      stroke="none"
      stroke-width="1"
      fill="none"
      fill-rule="evenodd"
    >
      <g>
        <circle stroke="#13C2C2" cx="500" cy="-20" r="6"></circle>
        <circle fill-opacity="0.4" fill="#9EE6E6" cx="166" cy="76" r="8"></circle>
        <circle stroke="#13C2C2" cx="1165" cy="240" r="5"></circle>
        <circle stroke="#CED4D9" cx="1300" cy="10" r="8"></circle>
        <circle stroke="#ffffff" cx="1326.5" cy="180" r="6"></circle>
        <circle fill-opacity="0.4" fill="#9EE6E6" cx="944" cy="250" r="5"></circle>
      </g>
      <g>
        <path
          d="M1182.79367,448.230356 L1186.00213,453.787581 C1186.55442,454.744166 1186.22667,455.967347 1185.27008,456.519632 C1184.96604,456.695168 1184.62116,456.787581 1184.27008,456.787581 L1177.85315,456.787581 C1176.74858,456.787581 1175.85315,455.89215 1175.85315,454.787581 C1175.85315,454.436507 1175.94556,454.091619 1176.1211,453.787581 L1179.32957,448.230356 C1179.88185,447.273771 1181.10503,446.946021 1182.06162,447.498305 C1182.36566,447.673842 1182.61813,447.926318 1182.79367,448.230356 Z"
          stroke="#CED4D9"
        ></path>
        <path
          d="M1376.79367,204.230356 L1380.00213,209.787581 C1380.55442,210.744166 1380.22667,211.967347 1379.27008,212.519632 C1378.96604,212.695168 1378.62116,212.787581 1378.27008,212.787581 L1371.85315,212.787581 C1370.74858,212.787581 1369.85315,211.89215 1369.85315,210.787581 C1369.85315,210.436507 1369.94556,210.091619 1370.1211,209.787581 L1373.32957,204.230356 C1373.88185,203.273771 1375.10503,202.946021 1376.06162,203.498305 C1376.36566,203.673842 1376.61813,203.926318 1376.79367,204.230356 Z"
          stroke="#2F54EB"
        ></path>
      </g>
      <g>
        <rect stroke="#13C2C2" stroke-opacity="0.6" x="120" y="322" width="12" height="12" rx="1"></rect>
        <rect stroke="#CED4D9" x="108" y="1" width="9" height="9" rx="1"></rect>
      </g>
    </svg>
    <div class="box">
      <slot name="content" />
    </div>

    <div v-show="showFooter" class="footer">
      <a-space>
        <template #split>
          <a-divider type="vertical" />
        </template>
        <a-button type="text">
          <a href="https://jpom.top" target="_blank">
            Jpom ©2019-{{ new Date().getFullYear() }} Of Him Code Technology Studio
          </a>
        </a-button>
        <a-dropdown>
          <a-button type="text">
            {{
              supportLang.find((item) => {
                return item.value === nowLang
              })?.label
            }}
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item
                v-for="item in supportLang"
                :key="item.value"
                :disabled="nowLang === item.value"
                @click="useGuideStore.changeLocale(item.value)"
              >
                <span>
                  <a-tooltip :title="`${item.label}(${item.value})`">
                    {{ item.label }}
                  </a-tooltip>
                </span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-dropdown>
          <a-button type="text">
            {{
              useGuideStore.getSupportThemes.find((item) => {
                return item.value === themeValue
              })?.label
            }}
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item
                v-for="item in useGuideStore.getSupportThemes"
                :key="item.value"
                :disabled="themeValue === item.value"
                @click="useGuideStore.toggleThemeView(item.value)"
              >
                <span>
                  <a-tooltip :title="`${item.label}(${item.value})`">
                    {{ item.label }}
                  </a-tooltip>
                </span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { supportLang } from '@/i18n'

const useGuideStore = guideStore()

const themeValue = computed(() => {
  return useGuideStore.getCatchThemeView()
})

const nowLang = computed({
  get() {
    return useGuideStore.getLocale()
  },
  set(newValue) {
    useGuideStore.changeLocale(newValue)
  }
})

defineProps({
  showFooter: {
    type: Boolean,
    default: true
  }
})

const backgroundImage = computed(() => {
  const color =
    useGuideStore.getThemeView() === 'light'
      ? 'linear-gradient(#1890ff, #66a9c9)'
      : 'linear-gradient(rgb(38 46 55), rgb(27 33 36))'

  // background: linear-gradient(#1890ff, #66a9c9);
  return {
    background: color
  }
})
</script>
<style scoped>
.init-wrapper {
  /* width: 100vw; */
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  overflow: auto;
}

.box {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

:global(.ant-result-content) {
  background-color: unset !important;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex; /* 添加Flex布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中（如果需要） */
  /* 添加必要的高度和内边距 */
  /* height: 60px; */
  padding: 10px;
}
</style>
