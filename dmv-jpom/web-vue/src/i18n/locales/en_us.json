{"i18n_0006600738": "Join a Docker cluster", "i18n_005de9a4eb": "The build history is used to record the information of each build, which can keep bundle information and build logs. At the same time, it can also quickly roll back the release", "i18n_0079d91f95": "Are you sure you want to top this data?", "i18n_007f23e18f": "Turn off TLS authentication", "i18n_00a070c696": "Click to copy.", "i18n_00b04e1bf0": "send packet", "i18n_00d5bdf1c3": "number of calls", "i18n_00de0ae1da": "Scripts to be executed before file upload (non-blocking commands)", "i18n_01081f7817": "Please enter the suffix and file encoding that allow editing of the file. If you do not set the encoding, the system encoding will be taken by default, and multiple line breaks will be used. Example: Set the encoding: txt {'@'} utf-8, do not set the encoding: txt", "i18n_010865ca50": "Do you really want to stop the project?", "i18n_0113fc41fc": "full screen log", "i18n_01198a1673": "Upload small file", "i18n_01226f48fc": "For each subexpression, the following forms are also supported:", "i18n_0128cdaaa3": "assignment type", "i18n_01ad26f4a9": "Reset the trigger token information, the previous trigger token will be invalid after reset", "i18n_01b4e06f39": "restart", "i18n_01e94436d1": "original password", "i18n_020d17aac6": "Send size", "i18n_020f1ecd62": "Start uploading", "i18n_020f31f535": "The path needs to be configured with an absolute path, and soft chain is not supported.", "i18n_0215b91d97": "The build serial number id needs to be replaced with the actual situation.", "i18n_0221d43e46": "Remote download URL is not empty", "i18n_0227161b3e": "execution method", "i18n_022b6ea624": "Are you sure you want to delete the current volume?", "i18n_0253279fb8": "cloning depth", "i18n_02d46f7e6f": "Do you really want to delete these build histories?", "i18n_02d9819dda": "prompt", "i18n_02db59c146": "Prohibited IP address", "i18n_02e35447d4": "Download the bundle. If the button is not available, it means that the product file does not exist. Generally, the corresponding file is not generated by the build or the file related to the build history is deleted.", "i18n_0306ea1908": "Delete Mirror", "i18n_031020489f": "Current workspace The build record you triggered", "i18n_03580275cb": "Please select the item you want to restart", "i18n_0360fffb40": "And turn on this switch", "i18n_036c0dc2aa": "System Cancel Distribution", "i18n_0373ba5502": "You need to install the agent on the server that needs to be managed and add the agent information to the system", "i18n_03816381ec": "Switch view", "i18n_0390e2f548": "Parameter {count} description", "i18n_03a74a9a8a": "log path", "i18n_03c1f7c142": "Please fill in and select the built repository.", "i18n_03d9de2834": "Project operation and maintenance", "i18n_03dcdf92f5": "privacy variable", "i18n_03e59bb33c": "compact", "i18n_03f38597a6": "speed", "i18n_0428b36ab1": "copy", "i18n_04412d2a22": "The operation cannot be withdrawn", "i18n_044b38221e": "Java project (for example reference, the specifics need to be determined according to the actual situation of the project)", "i18n_045cd62da3": "Model:", "i18n_045f89697e": "Publish compressed packages", "i18n_047109def4": "pending", "i18n_04a8742dd7": "plugin runtime", "i18n_04edc35414": "template node", "i18n_051fa113dd": "How to connect to docker is realized through end point, and each operation of docker related api needs to log in to end point once.", "i18n_05510a85b0": "All your operation logs in the system", "i18n_059ac641c0": "Privilege:", "i18n_059b86dbe1": "Are you sure you want to delete this publishing task template?", "i18n_05b52ae2db": "{Slot1} Select container function for container build (fromTag)", "i18n_05cfc9af9d": "receive error", "i18n_05e6d88e29": "The distribution node refers to the script that automatically synchronizes the script content to the node after editing the script. The DSL mode in the general user node distribution function", "i18n_05e78c26b1": "In a single trigger address: the first random string is the command script ID, and the second random string is the token.", "i18n_05f6e923af": "execution error", "i18n_0647b5fc26": "Stop first", "i18n_066431a665": "Please enter a certificate description", "i18n_066f903d75": "Moving up or down after operation may not achieve the expected sequence", "i18n_067638bede": "CPU number", "i18n_067eb0fa04": "If the alarm contact here cannot be selected, it means that the administrator here has not set the mailbox, which can be set in the user profile in the drop-down menu in the upper right corner.", "i18n_0693e17fc1": "Does it automatically scroll to the bottom when there is new content?", "i18n_06986031a7": "Need to go to the original workspace to control node distribution", "i18n_06e2f88f42": "Please enter a name", "i18n_0703877167": "Close MFA", "i18n_0719aa2bb0": "Reset password", "i18n_0728fee230": "Please enter an announcement title", "i18n_072fa90836": "compression ", "i18n_0739b9551d": "port protocol", "i18n_07683555af": "Current version number:", "i18n_0793aa7ba3": "Maven sdk image usage: https://mirrors.tuna.tsinghua.edu.cn/apache/maven/maven-3/", "i18n_07a03567aa": "virtual memory usage", "i18n_07a0e44145": "Hostname:", "i18n_07a828310b": "parallelism", "i18n_07a8af8c03": "The actual process ID for the current project", "i18n_07b6bb5e40": "Strict execution of scripts (build commands, event scripts, local publish scripts, container build commands) must return a status code of 0, otherwise the build status will be marked as failed", "i18n_07d2261f82": "The default is the current time to the end of the year.", "i18n_080b914139": "Upload package", "i18n_0836332bf6": "upgrade protocol", "i18n_083b8a2ec9": "A physical node bound to multiple server levels can also generate lonely data", "i18n_08902526f1": "Skin:", "i18n_0895c740a6": "swap memory usage", "i18n_089a88ecee": "System time:", "i18n_08ab230290": "Operation instructions", "i18n_08ac1eace7": "Scripts to be executed after a successful file upload (non-blocking commands)", "i18n_08b1fa1304": "Please enter your username", "i18n_08b55fea3c": "manage", "i18n_0934f7777a": "New label end point", "i18n_095e938e2a": "Stop", "i18n_09723d428d": "alarm contact", "i18n_09d14694e7": "You need to get docker information in SSH monitoring.", "i18n_09e7d24952": "Actual memory usage:", "i18n_0a056b0d5a": "dynamic file", "i18n_0a1d18283e": "build confirmation pop-up", "i18n_0a47f12ef2": "If the lonely data is associated with other functions in the workspace, the corrected associated data will be invalid, and the corresponding function cannot query the associated data", "i18n_0a54bd6883": "Gmail mailbox configuration", "i18n_0a60ac8f02": "Yes", "i18n_0a63bf5b41": "Soft memory limitations.", "i18n_0a9634edf2": "Address wild-card, * means all addresses will use proxies", "i18n_0aa60d1169": "You have not logged in yet", "i18n_0aa639865c": "Do you really want to delete the machine SSH?", "i18n_0ac4999a4c": "Network interface card information", "i18n_0ac9e3e675": "After the binding is successful, it will no longer be displayed. It is strongly recommended to save this QR code or the following MFA key.", "i18n_0af04cdc22": "Supports filling in two ways:", "i18n_0af5d9f8e8": "The current area is a system management and asset management center", "i18n_0b23d2f584": "differential construction", "i18n_0b2fab7493": "For the current SSH authorization directory (file directory, file suffix, prohibit command), please go to [System Management] - > [Asset Management] - > [SSH Management] - > Operation Bar - > Association Button - > Corresponding Workspace - > Operation Bar - > Configuration Button", "i18n_0b3edfaf28": "Set memory limits.", "i18n_0b58866c3e": "Breakpoint/sharding single file download", "i18n_0b6811e5b1": "Using language", "i18n_0b76afbf5d": "The CPU allowed to execute (e.g., 0-3, 0", "i18n_0b9d5ba772": "Please respect the open-source agreement and do not modify the version information without authorization, otherwise you may be held legally responsible.", "i18n_0baa0e3fc4": "Publishing", "i18n_0bac3db71c": "Failure after server level restart", "i18n_0bbc7458b4": "Back to home page", "i18n_0bc45241af": "The incoming parameters are: outGivingId, outGivingName, status, statusMsg, executeTime", "i18n_0bf9f55e9d": "Can't close", "i18n_0bfcab4978": "Node sdk mirror usage: https://registry.npmmirror.com/-/binary/node", "i18n_0c0633c367": "The default workspace cannot be deleted", "i18n_0c1de8295a": "Independence", "i18n_0c1e9a72b7": "The micro-queue will be used to queue the build to avoid triggering the build to be interrupted almost simultaneously (the general user warehouse merge code will trigger multiple requests), the queue is kept in memory, and the restart will be lost.", "i18n_0c1f1cd79b": "Do not restart automatically", "i18n_0c1fec657f": "second", "i18n_0c2487d394": "Drop-down search for the top 10 keywords related to the default search, as well as the machine nodes that have been selected", "i18n_0c256f73b8": "Container name:", "i18n_0c4eef1b88": "When it is 6 digits, the first digit indicates", "i18n_0c5c8d2d11": "Basic information:", "i18n_0c7369bbee": "Enable SSH access", "i18n_0cbf83cc07": "Contact us", "i18n_0ccaa1c8b2": " : Indicates that it matches this position all the time", "i18n_0ce54ecc25": "paid community", "i18n_0cf4f0ba82": "Do you really want to save the current configuration? If the configuration is wrong, the service may not be started. You need to manually restore Austria!!! Please pay attention to the restart status in time after successful saving!!", "i18n_0cf81d77bb": "Please fill in the warehouse address.", "i18n_0d44f4903a": "Do you really want to release (delete) the current project?", "i18n_0d467f7889": "#Whether to enable the log backup function", "i18n_0d48f8e881": "Please enter the service address", "i18n_0d50838436": "data directory", "i18n_0d98c74797": "other", "i18n_0da9b12963": "user data", "i18n_0de68f5626": "Log in to JPOM", "i18n_0e052223a4": "Restart server level needs to be regained", "i18n_0e16902c1e": "check status", "i18n_0e1ecdae4a": "Full sequence execution (if there is an execution failure, it will end this time)", "i18n_0e25ab3b51": "The allowed IP of the certificate needs to be the same as the docker host.", "i18n_0e44ae17ae": "Server level machine networking", "i18n_0e502fed63": "The restart timed out, please go to the server to check the console log to troubleshoot the problem.", "i18n_0e55a594fd": "monitoring project", "i18n_0e5f01b9be": "associative workspace ssh", "i18n_0ea78e4279": "View log", "i18n_0ec9eaf9c3": "more", "i18n_0eccc9451d": "#Number of backup files retained", "i18n_0ee3ca5e88": "Scan the code to appreciate and support the long-term development of open-source projects", "i18n_0ef396cbcc": "Distribute results", "i18n_0f004c4cf7": "third-party login", "i18n_0f0a5f6107": "normal connection", "i18n_0f189dbaa4": "No users", "i18n_0f4f503547": "Please enter the version", "i18n_0f539ff117": "Do you really want to batch delete the selected images? Images that are already in container use cannot be deleted!", "i18n_0f59fe5338": "Firewall port", "i18n_0f5fc9f300": "Document Management Center", "i18n_0f8403d07e": "Refresh countdown", "i18n_0fca8940a8": "No nodes.", "i18n_0ff425e276": "File ID", "i18n_1012e09849": "Processing failed", "i18n_10145884ba": "N lines after file", "i18n_1014b33d22": "group name", "i18n_101a86bc84": "Please enter...", "i18n_1022c545d1": "The plug-in side automatically checks the project when it starts. If it doesn't start, it will try to start it.", "i18n_102dbe1e39": "Note: environment variables have scope: current workspace or global, cannot be referenced across workspaces", "i18n_102e8ec6d5": "Network traffic information", "i18n_104000e24a": "Template source", "i18n_1058a0be42": "Enable TLS authentication, certificate information:", "i18n_1062619d5a": "The node account password is generated by the system by default: you can use the agent in the plug-in data directory.", "i18n_108d492247": "Regular grammar reference", "i18n_10c385b47e": "One-click distribution synchronizes system configuration of multiple nodes", "i18n_10d6dfd112": "N lines after display", "i18n_10f6fc171a": "SSH name", "i18n_111e786daa": "Fill in the remarks Only this build will take effect", "i18n_1125c4a50b": "Do you really want to delete the distribution information? After deleting, the items under the node will also be deleted", "i18n_113576ce91": "Product catalog:", "i18n_1149274cbd": "total number of users", "i18n_115cd58b5d": "] Backup folder?", "i18n_1160ab56fd": "Build command:", "i18n_116d22f2ab": "Project ID:", "i18n_11724cd00b": "Cluster creation time", "i18n_117a9cbc8d": "Language:", "i18n_11957d12e4": "Alarm", "i18n_11e88c95ee": " Find the previous one", "i18n_121e76bb63": "Please select the corresponding branch to build", "i18n_1235b052ff": "Node Address (*************:2123)", "i18n_1278df0cfc": "Associated Node If the server has a java environment, but the plug-in is not running, a quick install button will be displayed", "i18n_127de26370": "SMTP address: [smtp.qq.com], the username is generally the QQ number, the password is the email authorization code, and the port defaults to 587/465.", "i18n_12934d1828": "The log directory refers to the console log storage directory", "i18n_12afa77947": "Opening the cache build directory will keep the repository files, the second build will pull the code, and if the cache directory is not opened, the repository code will be re-pulled each time the build is not opened (larger projects are not recommended to close the cache)", "i18n_12d2c0aead": "Please copy this password to inform this user", "i18n_12dc402a82": "reference data", "i18n_130318a2a1": "The route is invalid and cannot be redirected", "i18n_1303e638b5": "Modification time", "i18n_13627c5c46": "Configure ssh", "i18n_138776a1dc": "The default is in the plugin-side data directory/{'${project Id }'}/{'${ project Id}'} .log", "i18n_138a676635": "Attention", "i18n_13c76c38b7": "#scriptId can refer to the script in the script library (G {'@'} xxx) where xxx is the script tag in the script library, provided that the corresponding script needs to be extracted and synchronized to the corresponding machine node", "i18n_13d10a9b78": "No asset SSH", "i18n_13d947ea19": "You need to add the asset machine first and then assign the machine node (logical node) to the current workspace.", "i18n_13f7bb78ef": "Default statistics in the machine except the local interface (loopback or no hardware address) network interface card traffic sum", "i18n_13f931c5d9": "View task", "i18n_1432c7fcdb": "System Announcement", "i18n_143bfbc3a1": "Click to resynchronize the current workspace logical node project information", "i18n_143d8d3de5": "Otherwise, all data that meets the conditions will be deleted", "i18n_148484b985": "You need to configure the docker container to be managed at the server level and assigned to the current workspace", "i18n_1498557b2d": "Only one menu can be expanded at a time", "i18n_14a25beebb": "Every 10 seconds", "i18n_14d342362f": "label", "i18n_14dcfcc4fa": "No reload has been performed", "i18n_14dd5937e4": "Additional environment variables.env Added multiple comma-separated", "i18n_14e6d83ff5": "Time:", "i18n_14ee5b5dc5": "The command file will be executed in {'${plugin-side data directory}'}/script/xxxx.sh, bat", "i18n_14feaa5b3a": "Refresh countdown ", "i18n_1535fcfa4c": "send", "i18n_156af3b3d1": "menu configuration", "i18n_1593dc4920": "Do you really want to delete this record? Container tags that build associations after deletion will not be available", "i18n_159a3a8037": "Update mirror", "i18n_15c0ba2767": "Upload project file", "i18n_15c46f7681": "Modify the interface HTTP status code to 200 and the response content is: success to determine the success of the operation, otherwise it may fail", "i18n_15d5fffa6a": "response result", "i18n_15e9238b79": "receive", "i18n_15f01c43e8": "log backup list", "i18n_15fa91e3ab": "Sky level", "i18n_1603b069c2": "Monday", "i18n_1622dc9b6b": "unknown", "i18n_162e219f6d": "lost", "i18n_164cf07e1c": "clear cover", "i18n_16646e46b1": "Product file size:", "i18n_16a3a4ed35": "Template marking", "i18n_16b5e7b472": "build directly", "i18n_16f7fa08db": "Really?", "i18n_17006d4d51": "Whether to automatically jump to the system page", "i18n_170fc8e27c": "Thursday", "i18n_174062da44": "distribution method", "i18n_1775ff0f26": "It is recommended to add a specified time range", "i18n_178ad7e9bc": "The id, token and trigger build in the parameters are consistent, buildNumId build sequence id", "i18n_17a101c23e": "Lonely data means that there is data in the machine node, but it cannot be bound to the current system (relationship binding = node ID + workspace ID corresponds). Generally, such data will not appear.", "i18n_17a74824de": "build method", "i18n_17acd250da": "Move Down", "i18n_17b4c9c631": "There are no nodes.", "i18n_17b5e684e5": "You need to configure the file suffix that allows editing in the authorization configuration of [Plug-in Side Configuration] in the node management.", "i18n_17c06f6a8b": "Last execution time", "i18n_17d444b642": "operation mode", "i18n_1810e84971": "To connect using SSH", "i18n_1818e9c264": "JVM total memory", "i18n_1819d0cdda": "If Sync to File Management is enabled, the sync to File Management is automatically performed during the build release process.", "i18n_181e1ad17d": "Long press to drag and sort.", "i18n_1857e7024c": "System version", "i18n_185926bf98": "full screen", "i18n_1862c48f72": "Local status:", "i18n_1880b85dc5": "Black and white ambiance", "i18n_18b0ab4dd2": "machine SSH name", "i18n_18b34cf50d": "Do not scroll", "i18n_18c63459a2": "default", "i18n_18c7e2556e": "If the current build information has been updated on another page, you need to click the refresh button to get the latest information. Unsaved data will also be lost after clicking refresh.", "i18n_18d49918f5": "Account is locked", "i18n_18eb76c8a0": "Memory minimum 4M", "i18n_192496786d": "event script", "i18n_19675b9d36": "The clearing code (warehouse directory) is to delete everything in the storage warehouse directory in the server. After deleting, the next build will pull up the files in the warehouse again. It is generally used to solve conflicts between files in the server and files in the remote warehouse. The execution time depends on the size of the source code directory and the number of files. Please be patient if it expires, or try again later.", "i18n_1974fe5349": "Binding successful", "i18n_197be96301": "To be perfected", "i18n_19f974ef6a": "When opening the difference release and opening the clear release, the files under the project directory will be automatically deleted, but the files not under the bundle directory will be automatically deleted. [Clear the release difference upload will delete the difference file before uploading the difference file]", "i18n_19fa0be4d2": " official document", "i18n_19fcb9eb25": "time", "i18n_1a2c905e87": "Select Unit", "i18n_1a44b9e2f7": "Sync to other workspaces", "i18n_1a55f76ace": "Build command, the relative path of the bundle is:", "i18n_1a56bb2237": "Select at least one node and project", "i18n_1a6aa24e76": "execute", "i18n_1a704f73c2": "Please select a file", "i18n_1a8f90122f": "prompt message ", "i18n_1abf39bdb6": "#Cache this directory globally (multiple builds can share this cache directory)", "i18n_1ad696efdc": "Build execution commands (non-blocking commands), such as: mvn clean package, npm run build. Supported variables: {'${BUILD_ID BUILD_NAME BUILD_SOURCE_FILE }'}、{'${ BUILD_NUMBER_ID}'}, .env in the warehouse directory, workspace variables", "i18n_1ae2955867": "Specify the pom file package mvn -f xxx/pom.xml clean package", "i18n_1afdb4a364": "Hide scroll bar. Longitudinal scroll mode Reminder: scroll wheel, horizontal scroll mode: Shift + scroll wheel", "i18n_1b03b0c1ff": "The Docker or cluster that has been assigned to the workspace is not directly deleted. You need to delete the assets Docker or cluster one by one after the assigned workspaces are deleted.", "i18n_1b38c0bc86": "Backup file storage directory:", "i18n_1b5266365f": "original IP", "i18n_1b5bcdf115": "Session has been closed [node-system-log]", "i18n_1b7cba289a": "data statistics", "i18n_1b8fff7308": "Open MFA", "i18n_1b963fd303": "[Recommended] Tencent identity verification code", "i18n_1b973fc4d1": "Group name:", "i18n_1ba141c9ac": "Please select the item of the soft chain.", "i18n_1ba584c974": "Configuration container", "i18n_1baae8183c": "Whether to decompress", "i18n_1c040e6b87": "In general, downgrade operations are not recommended", "i18n_1c10461124": "Example: key, key1 or key = value, key1 = value1", "i18n_1c13276448": "Current workspace association building", "i18n_1c2e9d0c76": "No build", "i18n_1c3cf7f5f0": "association", "i18n_1c61dfb86f": "mount point", "i18n_1c8190b0eb": "Please fill in the project DSL configuration content, you can click the switch tab above to view the configuration example", "i18n_1c83d79715": "execution failed", "i18n_1c9d3cb687": "Username ID", "i18n_1cc82866a4": "sharding operand", "i18n_1d0269cb77": "The SSH that has been assigned to a workspace cannot be deleted directly. You need to delete the asset SSH one by one in each assigned workspace.", "i18n_1d263b7efb": "This option is only valid for this build", "i18n_1d38b2b2bc": "Please select the project authorization path", "i18n_1d53247d61": "Please select a logical node", "i18n_1d650a60a5": "Hard disk", "i18n_1d843d7b45": "This node has no projects yet", "i18n_1dc518bddb": "Folder where the project is stored", "i18n_1dc9514548": "It is not the same as the PING test. A successful test here means that the network must be unobstructed, and a failed test here means that the network is not necessarily unobstructed.", "i18n_1de9b781bd": "To build with containers, the host where the docker container is located needs to have a public network, because the SDK and images that the environment depends on need to be downloaded remotely", "i18n_1e07b9f9ce": "Please select the machine node to synchronize the system configuration", "i18n_1e4a59829d": "Plug-in side boot self-start", "i18n_1e5533c401": "configuration directory", "i18n_1e5ca46c26": "Exclude publishing ANT expressions, multiple separated by commas", "i18n_1e88a0cfaf": "Do not publish to docker cluster", "i18n_1e93bdad2a": "Search project name", "i18n_1eb378860a": "Do You Really Want to Kill This Process?", "i18n_1eba2d93fc": "Reason for ban", "i18n_1ece1616bf": "If the plug-in is running normally but the connection fails, check if the port is open, firewall rules, Cloud as a Service security group inbound rules", "i18n_1ed46c4a59": "Distribution name (project name)", "i18n_1f08329bc4": "Search command name", "i18n_1f0c93d776": " : Executed every minute", "i18n_1f0d13a9ad": "The server level distribution synchronization script cannot be deleted directly, it needs to be operated at the server level", "i18n_1f1030554f": "Total {total}", "i18n_1f130d11d1": "SMTP server", "i18n_1f4c1042ed": "folder", "i18n_1fa23f4daa": "expiration time", "i18n_1fd02a90c3": "user", "i18n_200707a186": "The post-creation build method does not support modification", "i18n_2025ad11ee": "Do you really want to unbind the node script?", "i18n_2027743b8d": "System name:", "i18n_204222d167": "network latency", "i18n_2064fc6808": "Do not show", "i18n_207243d77a": "If you want to assign the workspace to other users, you also need to go to permission group management", "i18n_207d9580c1": "Indicates Saturday", "i18n_209f2b8e91": "Please enter your login password", "i18n_20a9290498": "You come to the system management center.", "i18n_20c8dc0346": "demo account", "i18n_20e0b90021": "Do you really want to delete the surveillance?", "i18n_20f32e1979": "Role:", "i18n_211354a780": "The root inside is just a normal user privilege outside. Default false", "i18n_21157cbff8": "millisecond", "i18n_211a60b1d6": "Edit some basic parameters of the container", "i18n_2141ffaec9": "Status data is acquired asynchronously with a time lag", "i18n_2168394b82": "File id, precision search", "i18n_2171d1b07d": "default parameter", "i18n_2191afee6e": "The upgrade timed out, please go to the server to check the console log to troubleshoot the problem.", "i18n_21d81c6726": "Configure labels for containers in the current workspace", "i18n_21da885538": "You can use the node script:", "i18n_21dd8f23b4": "open source protocol", "i18n_21e4f10399": "Priority judgment disabled period", "i18n_21efd88b67": "No data yet", "i18n_220650a1f5": "After configuration, it will be saved to the current build", "i18n_2213206d43": "Click Delay to view the network delay history data of the corresponding node.", "i18n_222316382d": "associated node", "i18n_2223ff647d": "clear release", "i18n_2245cf01a3": "You do not have permission to access", "i18n_2246d128cb": "WeCom notification address", "i18n_22482533ff": "Private key content, if not filled in, the configuration in the default $HOME/.ssh directory will be used. Supported configuration file directory: file:/xxxx/xx", "i18n_224aef211c": "build information", "i18n_224e2ccda8": "configuration", "i18n_2256690a28": "Node ID:", "i18n_22670d3682": "Please select the script to use", "i18n_226a6f9cdd": "Please check if the ws proxy is turned on.", "i18n_226b091218": "type", "i18n_2296651945": "#Currently supported uses plugin Java maven、node、go、python3、gradle 。 If the requirements are not met, you can configure the plugin yourself", "i18n_22b03c024d": "QR code", "i18n_22c799040a": "container", "i18n_22cf31df5d": "Current access IP:", "i18n_22e4da4998": "Indicates that the project is not currently running", "i18n_22e888c2df": "expiration time", "i18n_2300ad28b8": "read and write", "i18n_2314f99795": "New version detected ", "i18n_231f655e35": "Current program packaging time:", "i18n_23231543a4": "correction", "i18n_2331a990aa": "Scan the code to transfer money to support the long-term development of open-source projects", "i18n_233fb56ab2": "Get it in Settings -- > Security Settings -- > Private Tokens", "i18n_234e967afe": "Commands executed before publishing (non-blocking commands), usually close project commands, support variable substitution: {'${BUILD_ID BUILD_NAME BUILD_RESULT_FILE }'}、{'${ BUILD_NUMBER_ID}'}", "i18n_2351006eae": "Additional environment variables", "i18n_23559b6453": "#Cache the maven repository file in the container to the docker volume", "i18n_2356fe4af2": "Custom project management with script templates", "i18n_2358e1ef49": "Affiliated workspace: ", "i18n_235f0b52a1": "sending error", "i18n_23b38c8dad": "Session closed [upgrade]", "i18n_23b444d24c": "Quick configuration", "i18n_23eb0e6024": "nickname", "i18n_242d641eab": "Suffix", "i18n_2432b57515": "Remarks", "i18n_24384ba6c1": "Are you sure you want to resynchronize the current node project cache information?", "i18n_24384dab27": "Please enter the value of value", "i18n_244d5a0ed8": "build parameters", "i18n_2456d2c0f8": "If the container exits with a non-zero exit code, restart the container. You can specify the number of times: on-failure: 2", "i18n_2457513054": "Saturday", "i18n_2482a598a3": "Plugin version number", "i18n_248c9aa7aa": "build status", "i18n_2493ff1a29": "custom process type", "i18n_2499b03cc5": "Retained products:", "i18n_249aba7632": "days", "i18n_24ad6f3354": "If the project data in the machine before the crashed machine (asset machine) migration is only logically deleted (project files and logs are preserved)", "i18n_24cc0de832": "Execute command", "i18n_24d695c8e2": "cluster hostname", "i18n_250688d7c9": "publish failed", "i18n_250a999bb2": "Container labels, such as: xxxx: latest multiple separated by commas", "i18n_25182fb439": "Workspace menu", "i18n_251a89efa9": "View current status", "i18n_252706a112": "[Recommended] WeChat Mini Program Search, Digital Shield OTP", "i18n_2527efedcd": "User information URL", "i18n_2560e962cf": "Please select a distribution item", "i18n_257dc29ef7": "Search Configuration Reference", "i18n_25b6c22d8a": "In order to avoid displaying too much content and causing the browser card, read the last few lines of the log. After modification, you need to enter to read it again. If it is less than 1, read all", "i18n_25be899f66": "After filtering, this release operation only publishes the filter items, and it will only take effect for this operation", "i18n_25c6bd712c": "Please enter the number of scheduled runs obtained", "i18n_25f29ebbe6": "Number of script logs:", "i18n_25f6a95de3": "Make sure you want to cancel the build [Name:", "i18n_2606b9d0d2": "Distribution machine", "i18n_260a3234f2": "Please select SSH.", "i18n_2611dd8703": "When there is no corresponding node in the target workspace, a new node (logical node) will be automatically created.", "i18n_26183c99bf": "File Center", "i18n_2646b813e8": "login password", "i18n_267bf4bf76": "Distributing to nodes requires attention that cross-workspace duplicate names will be overwritten by the last synchronization", "i18n_2684c4634d": "Version:", "i18n_26a3378645": "Please select a running method", "i18n_26b5bd4947": "Loading...", "i18n_26bb841878": "new build", "i18n_26bd746dc3": "Do you really want to empty the project directory and files?", "i18n_26c1f8d83e": "Last Operator", "i18n_26ca20b161": "source", "i18n_26eccfaad1": "Mirror image:", "i18n_26f95520a5": "The execution command contains:", "i18n_26ffe89a7f": "Project name:", "i18n_27054fefec": "Execute script input parameters are: startReady, pull, executeCommand, release, done, stop, success", "i18n_2770db3a99": "Loading project data...", "i18n_2780a6a3cf": "TLS authentication", "i18n_27b36afd36": "Most operations with a status code of 0 have no operation results or are executed asynchronously", "i18n_27ba6eb343": "Gateway:", "i18n_27ca568be2": "continue", "i18n_27d0c8772c": "If mishandled, redundant data will be generated!!!", "i18n_27f105b0c3": "Please select the node to upgrade", "i18n_280379cee4": "Save and close", "i18n_282c8cda1f": "If the reported node information contains multiple IP addresses, the user needs to confirm the use of specific IP address information", "i18n_288f0c404c": "empty", "i18n_28b69f9233": "The process of building an image does not use caching", "i18n_28b988ce6a": "file type", "i18n_28bf369f34": "The published file name is: file ID. Suffix, not the real name of the file (you can modify it at will using the uploaded script)", "i18n_28c1c35cd9": "The master node cannot be directly deleted", "i18n_28e0fcdf93": "You cannot use containers to build Olympics without containers or unconfigured labels", "i18n_28e1c746f7": "SSH name", "i18n_28e1eec677": "authorization path", "i18n_28f6e7a67b": "static file", "i18n_29139c2a1a": "file name", "i18n_2926598213": "Project log", "i18n_293cafbbd3": "crop", "i18n_2953a9bb97": "You need to create an account for subsequent login to the management system, please remember the super administrator account password", "i18n_295bb704f5": "language", "i18n_29b48a76be": "Please choose a publishing method", "i18n_29efa328e5": "undistributed", "i18n_2a049f4f5b": "Distribution failed", "i18n_2a0bea27c4": "execution domain", "i18n_2a0c4740f1": "file", "i18n_2a1d1da97a": "Package testing environment package mvn clean package -Dmaven.test.skip = true -Ptest", "i18n_2a24902516": "Cluster ID:", "i18n_2a38b6c0ae": "The upgrade was not successful:", "i18n_2a3b06a91a": "Virtual MAC", "i18n_2a3e7f5c38": "manual", "i18n_2a6a516f9d": "Fill out the run command", "i18n_2a813bc3eb": "Download now", "i18n_2ad3428664": "Please select the service name to publish to the cluster", "i18n_2adbfb41e9": "Parameters if passed in", "i18n_2ae22500c7": "disabled period", "i18n_2b04210d33": "Process number:", "i18n_2b0623dab9": "Independent container", "i18n_2b0aa77353": "Are you sure you want to start the current container?", "i18n_2b0f199da0": "Do not execute or compile test cases mvn clean package -Dmaven.test.skip = true", "i18n_2b1015e902": "Parameter descriptions have no practical effect", "i18n_2b21998b7b": "Are you sure you want to turn off two-step verification? Account security will be affected after closing, and existing mfa keys will be invalid after closing", "i18n_2b36926bc1": "There is no construction history.", "i18n_2b4bb321d7": "Content Area Topic Switch", "i18n_2b4cf3d74e": "Please select the build to use", "i18n_2b52fa609c": "Abnormality occurs", "i18n_2b607a562a": "line by line execution", "i18n_2b696d1fec": "Silence time", "i18n_2b6bc0f293": "operation", "i18n_2b788a077e": "and other commonly used usernames to avoid excessive login failures caused by intentional or unintentional operations by other users, so that the super administrator account is abnormally locked.", "i18n_2b94686a65": "Add environment variables to the container", "i18n_2ba4c81587": "Please enter your email address", "i18n_2bb1967887": "Please ask us for authorization, otherwise there will be legal risks.", "i18n_2be2175cd7": "Execution container, label", "i18n_2be75b1044": "global", "i18n_2bef5b58ab": "If you don't fill it in, you won't update it.", "i18n_2c014aeeee": "packing time", "i18n_2c5b0e86e6": "User password reset successful", "i18n_2c635c80ec": "The publish operation refers to publishing (uploading) the files in the bundle directory to the corresponding place in different ways after executing the build command", "i18n_2c74d8485f": "After the download is completed, you need to manually select Update to Node to complete the node update.", "i18n_2c8109fa0b": "Current directory: ", "i18n_2c921271d5": "Vue project (for example reference, the specifics need to be determined according to the actual situation of the project)", "i18n_2cdbbdabf1": "The bundle directory, the path to the relative repository, such as the target/xxx.jar vue dist of the java project", "i18n_2cdcfcee15": "Feature-rich, designed for 2-step verification codes", "i18n_2ce44aba57": "log directory", "i18n_2d05c9d012": "Key words, support regularization", "i18n_2d2238d216": "Account added successfully", "i18n_2d3fd578ce": "Are you sure you want to get a batch deselected build? Note: Canceling/stopping a build may not necessarily shut down all associated processes normally", "i18n_2d455ce5cd": "Downloading", "i18n_2d58b0e650": "Select the build tab, not the latest commit", "i18n_2d7020be7d": "For example, the common .env file", "i18n_2d711b09bd": "content", "i18n_2d842318fb": "cycle", "i18n_2d94b9cf0e": "Dockerfile build method does not support rollback here", "i18n_2d9569bf45": "Parameter value, after adding default parameters, you need to fill in the parameter value when manually executing the script", "i18n_2d9e932510": "new directory", "i18n_2de0d491d0": "hour", "i18n_2e0094d663": "Do you really want to delete the cluster information? 1", "i18n_2e1f215c5d": "Automatically create users", "i18n_2e505d23f7": "Download import template", "i18n_2e51ca19eb": "If the node option is disabled, it means that there is a recommended associated node for the corresponding data (this may happen in lower version project data)", "i18n_2e740698cf": "cluster IP", "i18n_2ea7e70e87": "The command file will be uploaded to {'${user.home}'}/.jpom/xxxx.sh will be automatically deleted upon completion", "i18n_2ef1c35be8": "Executing CPU", "i18n_2f4aaddde3": "delete", "i18n_2f5e828ecd": "alias code", "i18n_2f5e885bc6": "Get a single build log address", "i18n_2f67a19f9d": "You need to select the corresponding service name published to the cluster, and you need to go to the cluster in advance to create a service.", "i18n_2f6989595f": "Manage the list:", "i18n_2f8d6f1584": "Yesterday", "i18n_2f8dc4fb66": "Do you really want to release the distribution information? After the release, the project information under the node will still be retained. If you want to delete the project, you need to go to the node management.", "i18n_2f8fd34058": "Script templates are command scripts stored at the server level to manage some script commands online, such as initializing the software environment, managing applications, etc", "i18n_2f97ed65db": "occupy", "i18n_2fc0d53656": "Machine state (cache)", "i18n_2ff65378a4": "Do you really want to delete the SSH of the corresponding workspace?", "i18n_2fff079bc7": "Published successfully", "i18n_3006a3da65": "System version:", "i18n_300fbf3891": "Stop before publishing means to close the project when publishing the file to the project file, and then replace the file. Avoid file occupation in the windows environment", "i18n_302ff00ddb": "super administrator", "i18n_3032257aa3": "Details", "i18n_30849b2e10": "Process/Port", "i18n_30aaa13963": "Serial Number (SN)", "i18n_30acd20d6e": "user ID", "i18n_30d9d4f5c9": "new connection", "i18n_30e6f71a18": "Custom label wildcard expression", "i18n_30e855a053": "Cancel distribution", "i18n_30ff009ab3": "#Java mirror source https://mirrors.tuna.tsinghua.edu.cn/Adoptium/", "i18n_3103effdfd": "Please enter your account name", "i18n_31070fd376": "manual rollback", "i18n_310c809904": "Bind to the current workspace", "i18n_312e044529": " : Range: 0 (Sunday)~ 6 (Saturday), 7 can also represent Sunday, and supports case-insensitive aliases: \"sun\",\"mon\",\"tue\",\"wed\",\"thu\",\"fri\",\"sat\",", "i18n_312f45014a": "Creation time:", "i18n_31353ecf96": "Download authorization code:", "i18n_314f5aca4e": "In a single trigger address: the first random string is the build ID, and the second random string is the token.", "i18n_315eacd193": "Move Up", "i18n_31691a647c": "{Slot1} port", "i18n_3174d1022d": "Container Construction Notes", "i18n_3181790b4b": "Server level system configuration", "i18n_318ce9ea8b": "User password prompt", "i18n_31aaaaa6ec": "Build ID:", "i18n_31ac8d3a5d": "thread synchronizer", "i18n_31bca0fc93": "Joining the beta program can get the latest features, some optimized features, and the fastest bug-fixing version in time, but the beta version may also be unstable in some new features. You need to evaluate whether you can join the beta according to your business situation. If you encounter problems during the use of the beta version, you can give us feedback at any time, and we will answer you as soon as possible.", "i18n_31eb055c9c": "Parallelism, the number of containers upgraded at the same time", "i18n_31ecc0e65b": "project", "i18n_3200fba1c6": "Download source:", "i18n_32112950da": "Bulk Cancel", "i18n_3241c7c05f": "It is recommended to use server level scripts to distribute to scripts:", "i18n_32493aeef9": "Under construction", "i18n_329e2e0b2e": "Specify directory packaging yarn & & yarn --cwd xxx build", "i18n_32a19ce88b": "Console log path", "i18n_32ac152be1": "update", "i18n_32c65d8d74": "title", "i18n_32cb0ec70e": "Please enter a node name", "i18n_32d0576d85": "token", "i18n_32dcc6f36e": "Restart strategy: no, always, less-stopped, on-failure", "i18n_32e05f01f4": "cluster information", "i18n_32f882ae24": "Matches zero or more characters", "i18n_330363dfc5": "success", "i18n_3306c2a7c7": "read default", "i18n_33130f5c46": "Operation successful", "i18n_3322338140": "Please select Post-Publish Action", "i18n_332ba869d9": "Generally used in cases where the node environment is consistent", "i18n_334a1b5206": "Install node", "i18n_335258331a": "The default configuration file has been read into the editor", "i18n_33675a9bb3": "The docker information associated with the cluster is lost, and the management function cannot continue to be used", "i18n_339097ba2e": "Ready to distribute", "i18n_33c9e2388e": "Project ID", "i18n_3402926291": "Current log file size:", "i18n_340eb70415": "Log encoding format", "i18n_346008472d": "Matches lines containing exceptions", "i18n_3477228591": "mirror image", "i18n_35134b6f94": "View node script", "i18n_3517aa30c2": "Variables supported in the script are: {'${PROJECT_ID }'}、{'${ PROJECT_NAME }'}、{'${ PROJECT_PATH}'}", "i18n_353707f491": "You can go to [Node Distribution] = > [Distribution Authorization Configuration] to modify it.", "i18n_353c7f29da": "Please select the template node", "i18n_35488f5ba8": "Please select a node item", "i18n_354a3dcdbd": "Every 30 seconds", "i18n_3574d38d3e": "Remaining memory:", "i18n_35b89dbc59": "Are you sure you want to download the latest version?", "i18n_35cb4b85a9": "[Currently only the first item matched is used]", "i18n_35fbad84cb": "Description The first one in ascending order according to the creation time", "i18n_3604566503": "Please fill in the container address.", "i18n_364bea440e": "Please select the script to reference", "i18n_368ffad051": "{Slot1} Table of Contents", "i18n_36b3f3a2f6": "Alarm title", "i18n_36b5d427e4": "Please enter a workspace description", "i18n_36d00eaa3f": "Differential construction:", "i18n_36d4046bd6": "Reference script template", "i18n_36df970248": "#version needs to exist in the corresponding mirror source", "i18n_3711cbf638": "pre-occupied resources", "i18n_37189681ad": "Data Id", "i18n_373a1efdc0": "Please select the item you want to close", "i18n_374cd1f7b7": "Create a cluster", "i18n_375118fad1": "Physical Node Script Template Data:", "i18n_375f853ad6": "Hardware information", "i18n_3787283bf4": "Do you really want to delete the current file?", "i18n_37b30fc862": "Please select a skin.", "i18n_37c1eb9b23": "configuration file path", "i18n_37f031338a": "Upload the compressed package and automatically decompress it.", "i18n_37f1931729": "Data directory occupies space:", "i18n_383952103d": "This shard upload is implemented using simple logic. When uploading the first shard, overwrite mode is used, and subsequent shards use append mode. If the file corresponding to the upload interruption is a incomplete file, it will not be able to be used normally.", "i18n_384f337da1": "Synchronization mechanism is adopted", "i18n_3867e350eb": "environment variables", "i18n_386edb98a5": "Custom script projects (python, nodejs, go, interface exploration, es) [recommended]", "i18n_38a12e7196": "Select certificate file", "i18n_38aa9dc2a0": "More configurations", "i18n_38ce27d846": "Next step", "i18n_38cf16f220": "OK.", "i18n_38da533413": "The following command will be", "i18n_3904bfe0db": "Set up a super administrator account", "i18n_3929e500e0": "It is often the case that some operations such as migrating workspaces, migrating physical machines, etc. for projects may generate lonely data", "i18n_396b7d3f91": "file size", "i18n_398ce396cd": "Workspace synchronization", "i18n_39b68185f0": "The node address is the IP: PORT of the plug-in side. The default port of the plug-in side is: 2123.", "i18n_39c7644388": "port number", "i18n_39e4138e30": "Cluster creation time:", "i18n_39f1374d36": "time consuming", "i18n_3a1052ccfc": "Reference environment variables", "i18n_3a17b7352e": "minute", "i18n_3a3778f20c": "Task ID", "i18n_3a3c5e739b": "batch build parameters", "i18n_3a3ff2c936": "volume label", "i18n_3a536dcd7c": "126 mailboxes", "i18n_3a57a51660": "Script version: {item}", "i18n_3a6000f345": "Running thread synchronizer", "i18n_3a6970ac26": "file sharing", "i18n_3a6bc88ce0": "Do you really want to delete the file?", "i18n_3a6c2962e1": "Key algorithm", "i18n_3a71e860a7": "Current end point not open", "i18n_3a94281b91": "Free scripting refers to the execution of arbitrary scripts directly in the machine node", "i18n_3aa69a563b": "Node distribution refers to the fact that a project needs to run in multiple nodes (servers), and node distribution is used to manage the project uniformly (distributed project management functions can be realized).", "i18n_3ac34faf6d": "wild-card", "i18n_3adb55fbb5": "Migrate workspace", "i18n_3ae4c953fe": "When the timed task has run to a time that matches these expressions, the task is started.", "i18n_3ae4ddf245": "Do you really want to delete the Docker? Deleting only checks the data associations of the local system, not the data in the docker container", "i18n_3aed2c11e9": "automatic", "i18n_3b14c524f6": "number of reads", "i18n_3b19b2a75c": "Do you really want to delete the script?", "i18n_3b885fca15": "cache version number", "i18n_3b9418269c": "Please fill in the associated container label", "i18n_3b94c70734": "project status", "i18n_3ba621d736": "Processing successful", "i18n_3baa9f3d72": "Batch build parameters also support specified parameters, delay (delayed execution of builds, in seconds) branchName (branch name), branchTagName (tag), script (build script), resultDirFile (bundle), webhook (notification webhook)", "i18n_3bc5e602b2": "email", "i18n_3bcc1c7a20": "Last Modifier", "i18n_3bdab2c607": "10 Minutes", "i18n_3bdd08adab": "describe", "i18n_3bf3c0a8d6": "Node", "i18n_3bf9c5b8af": " Group name:", "i18n_3c014532b1": "Construction time:", "i18n_3c070ea334": "If the associated build, the associated repository is bound (used) by multiple builds and cannot be migrated", "i18n_3c48d9b970": "Batch build parameter BODY json ： [ { \" id \":\" 1 \",\" token \":\" a \"}]", "i18n_3c586b2cc0": "Custom host", "i18n_3c6248b364": "cache information", "i18n_3c6fa6f667": "Cron expression", "i18n_3c8eada338": "Please select an encoding method", "i18n_3c91490844": "publish action", "i18n_3c99ea4ec2": "For example, in 2, 3, 6/3, since \"/\" has a high priority, it is equivalent to 2, 3, (6/3), and the result is equivalent to 2, 3, 6", "i18n_3c9eeee356": "Do you really want to delete log files?", "i18n_3cc09369ad": "Really want to delete [", "i18n_3d06693eb5": "Resources:", "i18n_3d0a2df9ec": "parameter", "i18n_3d3b918f49": "execute build", "i18n_3d3d3ed34c": "Please enter Select Association Group", "i18n_3d43ff1199": "top", "i18n_3d48c9da09": "authorization configuration", "i18n_3d61e4aaf1": "Specify label", "i18n_3d6acaa5ca": "This container has no network", "i18n_3d83a07747": "Host", "i18n_3dc5185d81": "private", "i18n_3dd6c10ffd": "Upload upgrade package", "i18n_3e445d03aa": "File doesn't exist", "i18n_3e51d1bc9c": "Please select the published SSH.", "i18n_3e54c81ca2": "receive traffic", "i18n_3e7ef69c98": "Monitor operation", "i18n_3e8c9c54ee": "Select group", "i18n_3ea6c5e8ec": "End of distribution", "i18n_3eab0eb8a9": "local script", "i18n_3ed3733078": "End point log", "i18n_3edddd85ac": "day", "i18n_3ee7756087": "Please select the node first.", "i18n_3f016aa454": "Mirror tag:", "i18n_3f18d14961": "2-step verification code", "i18n_3f1d478da4": "Server level scripts, SSH scripts can be referenced using G {'@'}(\" xxxx \") format, and the system will automatically replace the script content in the referenced script library when there is a reference", "i18n_3f2d5bd6cc": "Search in file lines 2 - 2", "i18n_3f414ade96": "Parameter description, {slot1}, is only used to hint at the meaning of the parameter", "i18n_3f553922ae": "Directories and files?", "i18n_3f5af13b4b": "#scriptId can be the name of the script file in the project path or the script template ID in the system", "i18n_3f719b3e32": "number of conflicts", "i18n_3f78f88499": "Packing time:", "i18n_3f8b64991f": "Automatically remove the excess folder names in the compressed package when decompressing.", "i18n_3f8cedd1d7": "For static file binding and reading (it is not recommended to configure large directories to avoid scanning consuming too many resources)", "i18n_3fb2e5ec7b": "login log", "i18n_3fb63afb4e": "exit code", "i18n_3fbdde139c": "Confirm password", "i18n_3fca26a684": "Batch trigger parameter BODY json ： [ { \" id \":\" 1 \",\" token \":\" a \"}]", "i18n_3fea7ca76c": "state", "i18n_401c396b51": "Log encoding format refers to the encoding format of the log file", "i18n_402d19e50f": "login", "i18n_40349f5514": "Number:", "i18n_4055a1ee9c": "Common fields are: createTimeMillis, modifyTimeMillis", "i18n_406a2b3538": "What is solitary data?", "i18n_4089cfb557": "Associated grouping is mainly used for asset monitoring to achieve different server levels to perform asset monitoring under different groupings", "i18n_40aff14380": "mirror ID", "i18n_40da3fb58b": "new state", "i18n_40f8c95345": "Temporary file directory", "i18n_411672c954": "Please enter a file description", "i18n_412504968d": "When there is no corresponding SSH in the target workspace, a new SSH will be automatically created.", "i18n_41298f56a3": "Build failed", "i18n_413d8ba722": "Legacy packages take up space:", "i18n_413f20d47f": "The system uses the oshi library to monitor the system, and uses /proc/meminfo in oshi to obtain memory usage.", "i18n_41638b0a48": "Used to distinguish whether files are of the same type, download management can be performed for the same type", "i18n_417fa2c2be": "Parameter {index} description", "i18n_4188f4101c": "No docker.", "i18n_41d0ecbabd": "Block IO weight", "i18n_41e8e8b993": "dark", "i18n_41e9f0c9c6": "worker node", "i18n_41fdb0c862": "Please upload or download the new version first.", "i18n_4244830033": "Please select a certificate file", "i18n_424a2ad8f7": "prepare", "i18n_429b8dfb98": "project distribution", "i18n_429d4dbc55": "#This example is for reference only. In practice, you need to configure it yourself according to the warehouse situation and construction process", "i18n_42a93314b4": "base image", "i18n_42b6bd1b2f": "Warehouse path", "i18n_42f766b273": "mount partition", "i18n_42fd64c157": "Start first", "i18n_4310e9ed7d": "Please select how the project will run", "i18n_43250dc692": "trigger management", "i18n_434d888f6f": "Please select the file in the file center", "i18n_434d9bd852": "After creating a user, automatically associate the corresponding permission group", "i18n_4360e5056b": "Loading data", "i18n_436367b066": "Project Management", "i18n_4371e2b426": "Please enter a project name", "i18n_43886d7ac3": "New operating parameters", "i18n_4393b5e25b": "loopback", "i18n_43c61e76e7": "Note: Currently ssh-keygen -t rsa -C is not supported for SSH key access to git repository addresses.", "i18n_43d229617a": "To be selected", "i18n_43e534acf9": "loose", "i18n_43ebf364ed": "Please select a backup type", "i18n_4403fca0c0": "clear", "i18n_44473c1406": "Open the cache build directory will keep the repository files, the second build will pull the code, and if the cache directory is not opened, the repository code will be re-pulled for each build (it is not recommended to close the cache for larger projects). Special instructions If the cache directory is missing version control related files will be automatically deleted and then re-pulled code", "i18n_4482773688": "Please enter a permission group name", "i18n_44876fc0e7": "If not, it means that the corresponding user has not configured a mailbox.", "i18n_449fa9722b": "In order to consider system security, we strongly recommend that the super administrator enable two-step verification to ensure the security of the account", "i18n_44a6891817": "new build", "i18n_44c4aaa1d9": "operating mode", "i18n_44d13f7017": "limited time", "i18n_44ed625b19": "network exception", "i18n_44ef546ded": "Project monitoring [Migration is not supported for the time being]", "i18n_44efd179aa": "log out", "i18n_45028ad61d": "certificate password", "i18n_4524ed750d": "Workspace name", "i18n_456d29ef8b": "log", "i18n_458331a965": "Are you sure you want to upload the file to update to the latest version?", "i18n_45a4922d3f": "Linked Data", "i18n_45b88fc569": "Match zero or more directories in the path", "i18n_45f8d5a21d": "Do you really want to delete the user?", "i18n_45fbb7e96a": "Project Loneliness Data", "i18n_46032a715e": "No build method selected yet", "i18n_4604d50234": "error message", "i18n_46097a1225": "Solitary data correction", "i18n_46158d0d6e": "Disable monitoring", "i18n_461e675921": "The current data is the default state. Moving up or down after the operation may not achieve the expected sorting. You also need to operate on the relevant data to achieve the expected sorting.", "i18n_461ec75a5a": "Path:", "i18n_461fdd1576": "Package production environment package mvn clean package -Dmaven.test.skip = true -Pprod", "i18n_4637765b0a": "Not enabled", "i18n_463e2bed82": "batch update", "i18n_4642113bba": "Click on the dashboard to view the monitoring historical data", "i18n_4645575b77": "Workspace description", "i18n_464f3d4ea3": "role", "i18n_465260fe80": "year", "i18n_4696724ed3": "trigger", "i18n_46a04cdc9c": "File description:", "i18n_46aca09f01": "Unbinding will check the data correlation and will not actually request the node to delete the project information.", "i18n_46ad87708f": "SSH name", "i18n_46c8ba7b7f": "If the button is not available, please go to the association of the asset management ssh list to add the authorization folder allowed to be managed in the current workspace", "i18n_46e3867956": "in progress", "i18n_46e4265791": "Build ID", "i18n_4705b88497": "scope", "i18n_47072e451e": "Management Node:", "i18n_470e9baf32": "Memory nodes allowed to execute", "i18n_471c6b19cf": "Before migration, you check the connection status and network status of the outgoing and incoming machines to avoid unknown errors or interruptions that cause process failure and generate redundant data!!!!", "i18n_4722bc0c56": "end point", "i18n_473badc394": "published node", "i18n_4741e596ac": "alarm time", "i18n_475a349f32": "The current build has not generated a trigger", "i18n_475cd76aec": "Statistical network interface cards:", "i18n_47768ed092": "Extremely unsafe", "i18n_47bb635a5c": "Data may have a time lag", "i18n_47d68cd0f4": "service", "i18n_47dd8dbc7d": "Search project ID", "i18n_47e4123886": "new distribution", "i18n_47ff744ef6": "Edit file", "i18n_481ffce5a9": "match second", "i18n_4826549b41": "Command templates are used to manage some scripting commands online, such as initializing software environments, managing applications, etc", "i18n_48281fd3f0": "Do you really want to delete the build information? Deleting will also delete all build history information simultaneously", "i18n_4838a3bd20": "Press and hold the Ctr or Alt/Option keys and click the button to quickly return to the first page", "i18n_4871f7722d": "task update time", "i18n_48735a5187": "Free space (unallocated)", "i18n_48a536d0bb": "Modify the container configuration and rerun it", "i18n_48d0a09bdd": "Light color", "i18n_48e79b3340": "] Documents?", "i18n_48fe457960": "(There are compatibility problems, you need to test in advance in actual use) go sdk image use: https://studygolang.com/dl/golang/go {'${GO_VERSION}'} .linux- {'${ARCH}'} .tar.gz", "i18n_4956eb6aaa": "load", "i18n_49574eee58": "Are you sure you want to operate?", "i18n_49645e398b": "If the configuration is wrong, you need to restart the server level and add the command line parameter --rest: ip_config will restore the default configuration", "i18n_497bc3532b": "JVM parameters", "i18n_497ddf508a": "Create a new blank file", "i18n_498519d1af": "refresh data", "i18n_499f058a0b": "Logout successful", "i18n_49a9d6c7e6": "Make a one-time donation sponsorship through the following QR code and invite the author to have a cup of coffee☕️", "i18n_49d569f255": "Please enter the host to check", "i18n_49e56c7b90": "Confirm modification", "i18n_4a00d980d5": "Simple and easy to use", "i18n_4a0e9142e7": "DingTalk", "i18n_4a346aae15": "Plugin version:", "i18n_4a4e3b5ae4": "Description:", "i18n_4a5ab3bc72": "Operation:", "i18n_4a6f3aa451": " : Executed at 5 minutes of each o'clock, 00:05, 01:05...", "i18n_4a98bf0c68": "Task details", "i18n_4aac559105": "weight", "i18n_4ab578f3df": "Environment variables:", "i18n_4ad6e58ebc": "Machine SSH", "i18n_4af980516d": "For the security of your account, the system requires that two-step verification must be turned on to ensure the security of the account.", "i18n_4b027f3979": "remind", "i18n_4b0cb10d18": "Please enter SMTP host", "i18n_4b1835640f": "Get it in Settings-- > Developer settings-- > Personal access tokens", "i18n_4b386a7209": "Get variable value address", "i18n_4b404646f4": "Container labels, such as: key1 = values1 & keyvalue2", "i18n_4b5e6872ea": "resident set", "i18n_4b96762a7e": "Last modification time", "i18n_4b9c3271dc": "reset", "i18n_4ba304e77a": "DingTalk account login", "i18n_4bbc09fc55": "Search on file lines 3 - 20", "i18n_4c096c51a3": "Port number:", "i18n_4c0eead6ff": "new parameter", "i18n_4c28044efc": "Confirm that the selected one  ", "i18n_4c69102fe1": "Then determine the allowable period. After configuring the allowable period, the user can only perform the operation of the corresponding function in the corresponding period", "i18n_4c7c58b208": "Please select the node status", "i18n_4c7e4dfd33": "When there is no corresponding node in the target workspace, a new docker (logical docker) will be automatically created.", "i18n_4c83203419": "Jump to a third-party system", "i18n_4c9bb42608": "prefix", "i18n_4cbc136874": "Folder:", "i18n_4cbc5505c7": "Differential build refers to whether the warehouse code has changed during the build, and if there is no change, the build will not be executed", "i18n_4ccbdc5301": "menu", "i18n_4cd49caae4": "distribution time", "i18n_4ce606413e": "Warehouse type", "i18n_4cfca88db8": "Select distribution file", "i18n_4d18dcbd15": "Do you really want to restore backup information?", "i18n_4d351f3c91": "IP ban", "i18n_4d49b2a15f": "Automatic execution: docker", "i18n_4d775d4cd7": "show", "i18n_4d7dc6c5f8": "write", "i18n_4d85ac1250": "System Management", "i18n_4d85c37f0d": "Workspace:", "i18n_4d9c3a0ed0": "Script content", "i18n_4dc781596b": "We use the following open-source software, and we are grateful that their open-source Jpom can be improved", "i18n_4df483b9c7": "project file ", "i18n_4e33dde280": "Current directory:", "i18n_4e54369108": "File type does not have trigger function", "i18n_4e7e04b15d": "Service name required", "i18n_4ed1662cae": "Please select a connection method", "i18n_4ee2a8951d": "Interface response ContentType is: text/plain", "i18n_4ef719810b": "There are no active tasks", "i18n_4effdeb1ff": "Search in file lines 1-2", "i18n_4f08d1ad9f": "Algorithm OID", "i18n_4f095befc0": "This configuration is only valid for server level administration, the ssh configuration of the workspace needs to be configured separately", "i18n_4f35e80da6": "path", "i18n_4f4c28a1fb": "File content format requirements: env_name = xxxxx Lines that do not meet the format will be automatically ignored", "i18n_4f50cd2a5e": "Compact Mode", "i18n_4f52df6e44": "Closing", "i18n_4f8a2f0b28": "not running", "i18n_4f8ca95e7b": "name", "i18n_4f9e3db4b8": "Choose to build", "i18n_4fb2400af7": "The container is running and can enter the end point", "i18n_4fb95949e5": "Opening", "i18n_4fdd2213b5": "Project ID", "i18n_500789168c": "Empty and restore will first delete the files in the project directory and then restore the corresponding backup file to the current directory", "i18n_5011e53403": "release cluster", "i18n_503660aa89": "Exclude:", "i18n_50411665d7": "number of reservations", "i18n_50453eeb9e": "The current workspace has no logical nodes and cannot create node scripts.", "i18n_504c43b70a": "Port/PID", "i18n_5068552b18": "historical monitoring chart", "i18n_50940ed76f": "Download successful", "i18n_50951f5e74": "Please select a branch", "i18n_50a299c847": "build name", "i18n_50c7929dd9": " Welcome ", "i18n_50d2671541": "Sure it's the same script", "i18n_50ed14e70b": "Dark dracula", "i18n_50f472ee4e": "Unit seconds, default 10 seconds, minimum 3 seconds", "i18n_50f975c08e": "The number of days the bundle is retained, less than or equal to 0 is to follow the global retention configuration. Note that automatic cleaning will only clean the data with the record status of: (end of build, in release, failed release, failed release) to avoid some abnormal builds affecting the number of reservations", "i18n_50fb61ef9d": "script name", "i18n_50fe3400c7": "Do you really want to delete this execution record?", "i18n_50fefde769": "Is it a compressed package?", "i18n_512e1a7722": "Please select an operator", "i18n_51341b5024": "Scripts distributed at the server level", "i18n_514b320d25": "How to choose a construction method", "i18n_5169b9af9d": "information loss", "i18n_5177c276a0": "Clusters cannot be created manually, creating requires multiple server levels to use a database, and configuring different cluster IDs to automatically create cluster information", "i18n_518df98392": "Search from the end", "i18n_5195c0d198": "Can manage {count} workspaces", "i18n_51c92e6956": "synchronization system configuration", "i18n_51d47ddc69": "Callback URL", "i18n_51d6b830d4": "Online build directory", "i18n_52409da520": "contact person", "i18n_527466ff94": "request parameters", "i18n_527f7e18f1": "Please read the instructions and precautions in the update log before uploading and before updating", "i18n_52a8df6678": "Is it a folder?", "i18n_52b526ab9e": "Empty the browser cache configuration will restore the default.", "i18n_52b6b488e2": "The script template is stored in the node (plug-in side), and the execution will also be executed in the node. The server level will regularly pull the execution log, and the pulling frequency is 100 pieces/minute", "i18n_52c6af8174": "Please enter client side key [clientSecret]", "i18n_52d24791ab": "Do you really want to delete these files?", "i18n_52eedb4a12": "alarm method", "i18n_52ef46c618": "Do not publish: only execute the build process and save the build history", "i18n_532495b65b": "number of copies", "i18n_53365c29c8": "Download status:", "i18n_534115e981": "Incomplete information cannot be edited", "i18n_5349f417e9": "Search keywords", "i18n_536206b587": "The current machine has not monitored any data", "i18n_537b39a8b5": "Required", "i18n_53bdd93fd6": "View script library", "i18n_541e8ce00c": "Regarding open-source software", "i18n_542a0e7db4": "synchronous authorization", "i18n_543296e005": "Please enter the authorization url [authorizationUri]", "i18n_543a5aebc8": "Is it really necessary to delete the current variable?", "i18n_543de6ff04": "distribution status message", "i18n_54506fe138": "Reset selection", "i18n_5457c2e99f": "#Use copy file to cache, otherwise use soft chain. copy file to cache node_modules can avoid npm WARN reify Removing non-directory", "i18n_547ee197e5": "new directory", "i18n_5488c40573": "Node project", "i18n_54f271cd41": "Script template", "i18n_5516b3130c": "Feishu account login", "i18n_551e46c0ea": " Name: ", "i18n_55405ea6ff": "export", "i18n_556499017a": "The project file will be stored in", "i18n_5569a840c8": "Please enter IP disallowed, multiple use newlines, support to configure IP segments ***********/*************, ***********/24", "i18n_55721d321c": "parameter description", "i18n_55939c108f": "Enter a file or folder name", "i18n_55abea2d61": "server level", "i18n_55b2d0904f": "It is used when performing multi-node distribution. Sequential restart and complete sequential restart need to ensure that the project can be restarted normally.", "i18n_55cf956586": "Join the cluster", "i18n_55d4a79358": "The configuration needs to declare the use of a specific docker to perform build-related operations (it is recommended to use the docker in the server where the server level is located)", "i18n_55da97b631": " The range is 0 to 59, but the first bit does not match. When it is 7 bits, the last bit indicates", "i18n_55e690333a": "There is no Docker cluster in the current workspace", "i18n_55e99f5106": "DingTalk notification address", "i18n_55f01e138a": "WeChat Appreciation", "i18n_56071a4fa6": "timeout", "i18n_56230405ae": "Unbinding does not actually request the node to delete the script information.", "i18n_562d7476ab": "Sunday", "i18n_56469e09f7": "Please go to [System Administration] - > [Asset Management] - > [Docker Management] to add <PERSON><PERSON>, or associate and assign the newly added Docker authorization to this workspace", "i18n_56525d62ac": "scan", "i18n_566c67e764": "Machines that have been assigned to a workspace cannot be deleted directly. You need to delete the assigned workspaces one by one, and then delete the asset machines.", "i18n_5684fd7d3d": "The new password for the account is:", "i18n_56bb769354": "Please read the instructions and precautions in the update log before downloading and before updating", "i18n_56d9d84bff": "Number of items in the logical nodes in the workspace:", "i18n_570eb1c04f": "Hard disk occupancy:", "i18n_5734b2db4e": "number of rows read", "i18n_576669e450": "Please select the project you want to start", "i18n_5785f004ea": "Please do not manually delete the files below the data directory. If you need to delete it, you need to back it up in advance or it has been determined that the corresponding file is deprecated before deleting it.", "i18n_578adf7a12": "Please confirm the configuration carefully, and it will take effect immediately after the ip configuration. When configuring, you need to ensure that the current ip can access! 127.0.0.1 the IP is not restricted by access. Support configuring IP segments ***********/*************, ***********/24", "i18n_578ca5bcfd": "163 mailboxes", "i18n_57978c11d1": "The log pop-up window will not open in full screen.", "i18n_579a6d0d92": "command value", "i18n_57b7990b45": "When SSH already exists in the target workspace, SSH account, password, private key and other information will be automatically synchronized", "i18n_57c0a41ec6": "The current data is the default state", "i18n_57cadc4cf3": "Will use PING check", "i18n_5805998e42": "restart strategy", "i18n_5854370b86": "trace file", "i18n_585ae8592f": "Rebuild container", "i18n_5866b4bced": "Number of clusters:", "i18n_587a63264b": "overlay restore", "i18n_588e33b660": "If the account is enabled for MFA (two-step verification), logging in with Oauth2 will not verify MFA (two-step verification).", "i18n_589060f38e": "Upgrading, please wait...", "i18n_5893fa2280": "email account", "i18n_58cbd04f02": "SSH refers to the release of products through SSH commands or the execution of multiple commands to achieve release (you need to add new ones in SSH in advance)", "i18n_58e998a751": "Deletion checks for data relevance, and no project or script exists on the node", "i18n_58f9666705": "size", "i18n_590b9ce766": "Currently supported plugins are available (more plugins are expected):", "i18n_590dbb68cf": "End time:", "i18n_590e5b46a0": "Automatic backup", "i18n_592c595891": "start time", "i18n_5936ed11ab": "The script library is used to store and manage general-purpose scripts, and the scripts in the script library cannot be executed directly.", "i18n_593e04dfad": "Menu theme", "i18n_597b1a5130": "update status", "i18n_59a15a0848": "The synchronization mechanism adopts IP + PORT + connection to confirm that it is the same server.", "i18n_59c316e560": "distribution file", "i18n_59c75681b4": "Notification object", "i18n_59cf15fe6b": "Template", "i18n_59d20801e9": "Search on file lines 17 - 20", "i18n_5a0346c4b1": "Edit user", "i18n_5a1367058c": "Back to Home", "i18n_5a1419b7a2": "data name", "i18n_5a42ea648d": "Self-built gitlab access address", "i18n_5a5368cf9b": "Wrong password", "i18n_5a63277941": "The values are: stop, beforeStop, start, beforeRestart, fileChange", "i18n_5a7ea53d18": "Docker information", "i18n_5a8727305e": "Please do not exit the management node first.", "i18n_5a879a657b": "swap memory", "i18n_5aabec5c62": "Parent ID", "i18n_5ab90c17a3": "End of mission", "i18n_5ab9bf3591": "Do you need to use sudo to execute: docker system dial-stdio", "i18n_5ad7f5a8b2": "result", "i18n_5ae4a8f177": "Please enter the silence time", "i18n_5afe5e7ed4": "Edit associated item", "i18n_5b1f0fd370": "Used to create node distribution projects, file center publishing files", "i18n_5b3ffc2910": "Distributing", "i18n_5b47861521": "Name:", "i18n_5baaef6996": "Click to resynchronize the current workspace logic node script template information", "i18n_5badae1d90": "There is no script.", "i18n_5bb162ecbb": "JVM Remaining Memory", "i18n_5bb5b33ae4": "So here we are.", "i18n_5bca8cf7ee": "Custom host, xxx: 192.168.0.x", "i18n_5bcda1b4d7": "Session has been closed [system-log]", "i18n_5bd1d267a9": "Get it in preferences-- > Access Tokens", "i18n_5c3b53e66c": "Modify file", "i18n_5c4d3c836f": "MFA verification is required.", "i18n_5c502af799": "Container name required", "i18n_5c56a88945": "deactivate", "i18n_5c89a5353d": "distribution node", "i18n_5c93055d9c": "Generally, the server cannot be connected and it has been determined that it is no longer in use.", "i18n_5ca6c1b6c7": "Please fill in the cluster name", "i18n_5cb39287a8": "monitoring function", "i18n_5cc7e8e30a": "Modify file permissions", "i18n_5d07edd921": "Please fill in the cluster IP.", "i18n_5d14e91b01": "Primary ID", "i18n_5d368ab0a5": "Execution commands are automatically replaced with sh command files and environment variables are automatically loaded:/etc/profile,/etc/bashrc,~/.bashrc,~/. bash_profile", "i18n_5d414afd86": "Search from the end, the first 2 lines of the file, and the last 3 lines of the file", "i18n_5d459d550a": "Processing", "i18n_5d488af335": "Remote download file", "i18n_5d5fd4170f": "The values are: 1.", "i18n_5d6f47d670": "The project is a static folder", "i18n_5d803afb8d": "Cannot communicate normally with the node", "i18n_5d817c403e": "No data was selected.", "i18n_5d83794cfa": "Node name:", "i18n_5d9c139f38": "content theme", "i18n_5dc09dd5bd": "Reconnect ", "i18n_5dc1f36a27": "certificate description", "i18n_5dc78cb700": "The number of bundle reservations, less than or equal to 0 is to follow the global reserved configuration (if the value is greater than 0, it will be compared with the minimum value of the global configuration for reference). Note that automatic cleaning will only clean the data with the record status of: (build end, release in progress, release failed, release failed) Avoid some abnormal builds affecting the number of reservations. The number of reservations will be checked when creating a new build record", "i18n_5dc7b04caa": "Number of processes viewed", "i18n_5dff0d31d0": "If you need to execute it automatically, fill in the cron expression. The second level is not turned on by default, you need to modify the configuration file: [system.timerMatchSecond])", "i18n_5e32f72bbf": "Refresh file table", "i18n_5e46f842d8": "Monitor users", "i18n_5e9f2dedca": "Was it successful?", "i18n_5ecc709db7": "All environment variables are not loaded by default during execution, and they need to be loaded by themselves in the script.", "i18n_5ed197a129": "Reset initialization pass parameters at startup", "i18n_5ef040a79d": "discard packet", "i18n_5ef72bdfce": "Command content supports workspace environment variables", "i18n_5effe31353": "cull folder", "i18n_5f4c724e61": "Please enter a task name", "i18n_5f5cd1bb1e": "The new associated project refers to associating the project that has been created in the node to distribute the project to the node to achieve unified management", "i18n_5fafcadc2d": "Session has been closed [node-script-consloe]", "i18n_5fbde027e3": "You can refer to the environment variables variable placeholder {'${xxxx}'} xxxx of the workspace as the variable name", "i18n_5fc6c33832": " jump to line", "i18n_5fea80e369": "No assets DOCKER", "i18n_5fffcb255d": "plugin run", "i18n_601426f8f2": "Push to warehouse", "i18n_603dc06c4b": "The page you are visiting does not exist", "i18n_60585cf697": " Welcome", "i18n_607558dbd4": "number of items", "i18n_607e7a4f37": "view", "i18n_609b5f0a08": "time", "i18n_60b4c08f5c": "Are you sure you want to stop the current container?", "i18n_6106de3d87": "JDK version", "i18n_61341628ab": " : representation list", "i18n_6143a714d0": "encoding format", "i18n_616879745d": "0:00 am and 12:00 noon", "i18n_61955b0e4b": "No project status and control functions", "i18n_61a3ec6656": "introduce", "i18n_61bfa4e925": "You need the dockerfile in the repository. If you view multiple folders, you can specify a secondary directory. If springboot-test-jar: springboot-test-jar/Dockerfile", "i18n_61c0f5345d": "SMTP address: [smtp.163.com, smtp.126.com...], the password is the email authorization code, the default port is 25, and the SSL port is 465.", "i18n_61e7fa1227": "<PERSON>", "i18n_61e84eb5bb": "Start time:", "i18n_620489518c": "Parameter {index} value", "i18n_620efec150": "More open source instructions", "i18n_62170d5b0a": "Search Reference", "i18n_6228294517": "Menu configuration only works for non-super administrators", "i18n_622d00a119": "Path to execute the script", "i18n_624f639f16": "Universal mailbox", "i18n_625aa478e2": "Search from the end, 0 lines before the file, 3 lines after the file", "i18n_625fb26b4b": "cancel", "i18n_627c952b5e": "total space", "i18n_6292498392": " Find next", "i18n_629a6ad325": "Safety Management", "i18n_629f3211ca": "Trim type", "i18n_631d5b88ab": "Please enter the project storage path authorization. The carriage return supports entering multiple paths. The system will automatically filter../path and do not allow entering the root path.", "i18n_632a907224": "Reset to regenerate the trigger address. After the reset is successful, the previous trigger address will be invalid. The trigger is bound to generate the trigger to the operator. If the corresponding account is deleted, the trigger will be invalid.", "i18n_6334eec584": "Every five seconds.", "i18n_635391aa5d": "Download product", "i18n_637c9a8819": "Select at least 1 node project", "i18n_638cddf480": "Creator, full match", "i18n_639fd37242": "For the currently used docker swarm cluster, you need to create a swarm cluster before you can choose.", "i18n_63b6b36c71": "Select certificate", "i18n_63c9d63eeb": "Multiple menus can be expanded simultaneously", "i18n_63dd96a28a": "Password support for referencing workspace variables:", "i18n_63e975aa63": "Installation ID:", "i18n_640374b7ae": "mount volume", "i18n_641796b655": "Build complete", "i18n_6428be07e9": "Configuration System Announcement", "i18n_643f39d45f": "non-suspended", "i18n_6446b6c707": "Nickname length is 2-10", "i18n_646a518953": "Please enter the project ID", "i18n_6470685fcd": ": means to match any time at this position (consistent with \"*\")", "i18n_649231bdee": "file suffix", "i18n_64933b1012": "Storage options", "i18n_6496a5a043": "command name", "i18n_649d7fcb73": "The new cluster requires manual configuration of cluster management asset groupings and cluster access addresses", "i18n_649d90ab3c": "Close right", "i18n_649f8046f3": "Please select an SSH node", "i18n_64c083c0a9": "result description", "i18n_64eee9aafa": "boot time", "i18n_652273694e": "host", "i18n_65571516e2": "Build Notes:", "i18n_657969aa0f": "<PERSON>", "i18n_657f3883e3": "Do not execute the release process", "i18n_65894da683": "Publication method:", "i18n_65cf4248a8": "Cannot be initialized", "i18n_65f66dfe97": "Clear the current buffer content", "i18n_66238e0917": "Binding an external system account is not supported when the existing account is inconsistent with the external system account.", "i18n_663393986e": "unbind", "i18n_6636793319": "Do you really want to delete the node? Deletion checks data correlation, and there is no project or script for the node", "i18n_664b37da22": "backups", "i18n_664c205cc3": "Do you really want to clear the warehouse hidden field information? (password, private key)", "i18n_667fa07b52": "Node upgrade to", "i18n_66aafbdb72": "Latest build ID", "i18n_66ab5e9f24": "new", "i18n_66b71b06c6": "Upload compressed files (automatic decompression)", "i18n_66c15f2815": "Match lines containing numbers", "i18n_66e9ea5488": "log name", "i18n_6707667676": "hostname", "i18n_6709f4548f": "random generation", "i18n_67141abed6": "Project authorization path + project folder", "i18n_67425c29a5": "Timeout (s)", "i18n_674a284936": "The second part will only be matched when isMatchSecond is true, and it is turned off by default.", "i18n_674e7808b5": "MFA verification code", "i18n_679de60f71": "Please fill in the log item name", "i18n_67aa1c0169": "Please fill in the build command", "i18n_67aa2d01b9": "Workspace menus, environment variables, and node distribution authorization need to be configured one by one", "i18n_67b667bf98": "partial backup", "i18n_67e3d3e09c": "batch build", "i18n_67e7f9e541": "monitoring cycle", "i18n_6816da19f3": "Close other", "i18n_6835ed12b9": "The key to environment variables", "i18n_685e5de706": "Container Construction", "i18n_6863e2a7b5": "script execution history", "i18n_686a19db6a": "auto delete", "i18n_68a1faf6e2": "Batch builds pass in other parameters and the modifications will be performed synchronously", "i18n_68af00bedb": "Table view to use workspace synchronization", "i18n_68c55772ca": "Please enter the authorized party's web application ID", "i18n_69056f4792": "Some operating status codes may be 0.", "i18n_690a3d1a69": "execution container", "i18n_691b11e443": "Current workspace", "i18n_6928f50eb3": "Support for configuring system parameters:", "i18n_69384c9d71": "Click to view historical trends", "i18n_693a06987c": "Please fill in the user account number.", "i18n_6948363f65": "Cancel timing, no longer timing execution (support! prefix disables timing execution, such as:! 0 0/1 * * * ?）", "i18n_694fc5efa9": "refresh", "i18n_695344279b": "File upload id generation failed:", "i18n_6953a488e3": "Select logical node", "i18n_697d60299e": "The currently logged in account is detected.", "i18n_69c3b873c1": "build locally", "i18n_69c743de70": "IP of the node", "i18n_69de8d7f40": "reduction", "i18n_6a359e2ab3": "scriptId can also be imported into the script library, which needs to be synchronized to the machine node in advance", "i18n_6a49f994b1": "The build process executes the corresponding script, starts building, builds complete, starts publishing, publishes complete, builds exceptions, publishes exceptions", "i18n_6a4a0f2b3b": "The synchronization mechanism uses the node address to determine that it is the same server (node).", "i18n_6a588459d0": "Workspace name", "i18n_6a620e3c07": "synchronization", "i18n_6a658517f3": "task log", "i18n_6a66d4cdf3": "Delay, container rollback interval", "i18n_6a6c857285": "distribution node", "i18n_6a8402afcb": "Parsing the file, ready to upload ", "i18n_6a8c30bd06": "Loading editor", "i18n_6a922e0fb6": "plug-in port", "i18n_6a9231c3ba": "Function args parameter, optional", "i18n_6aa7403b18": "If SSH is used but SSH cannot be selected, it means that the system has not detected the docker service.", "i18n_6aab88d6a3": "Save and restart", "i18n_6ab78fa2c4": "email address", "i18n_6ac61b0e74": "It is recommended to restore files that are consistent with the current version or files from a nearby version", "i18n_6ad02e7a1b": "Page resources loading....", "i18n_6adcbc6663": "Configuration method: SSH list - > operation bar - > associated button - > corresponding workspace - > operation bar - > configuration button", "i18n_6af7686e31": "Refresh every minute", "i18n_6b0bc6432d": "operator", "i18n_6b189bf02d": "Number of containers:", "i18n_6b29a6e523": "Launch project", "i18n_6b2e348a2b": "Timed execution", "i18n_6b46e2bfae": "Is it really the current workspace?", "i18n_6b4fd0ca47": "Support for configuring the sender: Following the RFC-822 standard, the sender can be in the following form:", "i18n_6b6d6937d7": "163 Mailbox SSL", "i18n_6bb5ba7438": "Limit commands that are prohibited at online end points", "i18n_6be30eaad7": "Please enter a timeout", "i18n_6bf1f392c0": "current state", "i18n_6c08692a3a": "If the password is not changed, you don't need to fill it in.", "i18n_6c14188ba0": "Cannot download directory", "i18n_6c24533675": "Please select an alarm contact or fill in the webhook.", "i18n_6c72e9d9de": "Edit distribution project", "i18n_6c776e9d91": "Project start, stop, restart, file change will request the corresponding address, optional, GET request", "i18n_6d110422ce": "(Need to choose how to re store the publishing template. By default, each method will only store and retain the latest publishing template, with alias templates having the highest priority.)", "i18n_6d5f0fb74b": "Do you need to push the image to a remote warehouse after it is successfully built?", "i18n_6d68bd5458": "full backup", "i18n_6d7f0f06be": "Please select a publish action", "i18n_6d802636ab": "privacy", "i18n_6da242ea50": "Task Id", "i18n_6dcf6175d8": "Go now.", "i18n_6de1ecc549": "View server level scripts", "i18n_6e02ee7aad": "The length of the period, expressed in microseconds.", "i18n_6e2d78a20e": "Search from the end, the first 20 lines of the file, and the last 3 lines of the file", "i18n_6e60d2fc75": "Page Enables Compact Mode", "i18n_6e69656ffb": "File cannot be empty", "i18n_6e70d2fb91": "Build parameters, such as: key1 = value1 & key2 = value2", "i18n_6ea1fe6baa": "basic information", "i18n_6eb39e706c": "editing machine", "i18n_6ef90ec712": "Please fill in the name of the image to be pulled", "i18n_6f15f0beea": "The two passwords do not match...", "i18n_6f32b1077d": "Please enter a workspace note, leave blank and use the default name", "i18n_6f5b238dd2": " SSH and local command issuance perform variable replacement. System reserved variables are: {'${BUILD_ID }'}、{'${ BUILD_NAME }'}、{'${ BUILD_RESULT_FILE }'}、{'${ BUILD_NUMBER_ID}'}", "i18n_6f6ee88ec4": "Support open source", "i18n_6f73c7cf47": "Retention days:", "i18n_6f7ee71e77": "static directory", "i18n_6f854129e9": "Group/label", "i18n_6f8907351b": "synchronization node configuration", "i18n_6f8da7dcca": "The node address format is: IP: PORT (example: *************:2123)", "i18n_6f9193ac80": "Enable two-step verification", "i18n_6fa1229ea9": "One-click distribution synchronizes the authorization configuration of multiple nodes", "i18n_6ffa21d235": "Distributing nodes refers to synchronizing variables to corresponding nodes, and current variables can also be used in node scripts", "i18n_7006410585": "No matter what exit code is returned, always restart the container.", "i18n_7010264d22": "No authentication is enabled.", "i18n_702430b89d": "Page Enables Loose Mode", "i18n_702afc34a0": "Difference release:", "i18n_7030ff6470": "error", "i18n_7035c62fb0": "account number", "i18n_704f33fc74": "Search from scratch, 0 lines before the file, 3 lines after the file", "i18n_706333387b": "This feature does not guarantee that the newly added container and the previous container parameters are exactly the same. Please use it with caution.", "i18n_7088e18ac9": "volume", "i18n_708c9d6d2a": "Please choose", "i18n_70a6bc1e94": "The current system has been initialized and cannot be initialized repeatedly.", "i18n_70b3635aa3": "execution time", "i18n_70b5b45591": "Quick installation", "i18n_70b9a2c450": "Do you really want to exit the system?", "i18n_710ad08b11": "disable", "i18n_7114d41b1d": "Super admin has no restrictions.", "i18n_712cdd7984": "Cooperation consultation", "i18n_713c986135": "The container build will generate the relevant mount directory in the docker, which generally does not require human operation", "i18n_7156088c6e": "Encoding method", "i18n_71584de972": "Non-server boot self-start, if you need to boot self-start, it is recommended to configure", "i18n_715ec3b393": "Used to quickly synchronize the configuration of other machine nodes", "i18n_7173f80900": "refuse", "i18n_71a2c432b0": "edit variable", "i18n_71bbc726ac": "follower system", "i18n_71c6871780": "timed task expression", "i18n_71dc8feb59": "Not configured", "i18n_71ee088528": "Bridge mode:", "i18n_7220e4d5f9": "way", "i18n_7229ecc631": "time", "i18n_7293bbb0ff": "Total number of inodes", "i18n_729eebb5ff": "There is no corresponding SSH.", "i18n_72d14a3890": "Please select the user's permission group", "i18n_72d46ec2cf": "Login information expired", "i18n_72d4ade571": ", is only used for the meaning of the prompt parameter", "i18n_72e7a5d105": "Mirror ID", "i18n_72eae3107e": "Grey green abbott", "i18n_72ebfe28b0": "timing", "i18n_7307ca1021": "auto start", "i18n_7327966572": "delete completely", "i18n_7329a2637c": "cluster ID", "i18n_73485331c2": "file information", "i18n_73578c680e": "The data directory refers to the files and data storage directories generated during the execution of the program", "i18n_73651ba2db": "batch restart", "i18n_7370bdf0d2": "script log", "i18n_738a41f965": "Project name", "i18n_73a87230e0": "File system type", "i18n_73b7b05e6e": " Distribute the script to the corresponding machine node, and the corresponding machine node can refer to the corresponding script ", "i18n_73b7e8e09e": "If you encounter any problems during the use of the beta version, you can give us feedback at any time, and we will answer you as soon as possible.", "i18n_73c980987a": "The loading container is available in the label....", "i18n_73d8160821": "Configure in yaml/yml format, scriptId is the relative path or script template ID of the script file under the project path, you can view scriptId in the script template editing pop-up window", "i18n_73ed447971": "We highly recommend that you use a TLS certificate", "i18n_73f798a129": "free community", "i18n_7457228a61": "Remote download address", "i18n_74bdccbb5d": "My Workspace", "i18n_74c5c188ae": "The successful operation interface HTTP status code is 200.", "i18n_74d5f61b9f": "build trigger", "i18n_74d980d4f4": "In the single page list, file type items will be automatically sorted to the end", "i18n_74dc77d4f7": "Container ID", "i18n_74dd7594fc": "Request when an alarm occurs", "i18n_74ea72bbd6": "cluster management", "i18n_751a79afde": "30 Minutes", "i18n_7527da8954": "normal user", "i18n_7548ea6316": "Click to collapse the left menu bar", "i18n_75528c19c7": "Automatic restart", "i18n_7561bc005e": "Build process request, optional, GET request", "i18n_75769d1ac8": "read", "i18n_757a730c9e": "Unable to connect", "i18n_758edf4666": "Search from scratch, 2 lines before the file, 3 lines after the file", "i18n_75c63f427a": "This option is an experimental property, and the actual effect is basically the same.", "i18n_75fc7de737": "route", "i18n_7617455241": "If there are two fields in the file: MemAvailable and MemTotal, then o<PERSON> will use it directly, so in this system, the memory occupation calculation method: memory occupation = (total-available)/total", "i18n_762e05a901": "Difference publishing refers to whether there are differences in the files in the corresponding bundle and project folders, and if there are incremental differences, then upload or overwrite the files.", "i18n_7650487a87": "Address", "i18n_76530bff27": "Please enter a private token", "i18n_7653297de3": "jump", "i18n_765592aa05": "If the container data directory is not mounted, please back up the data in advance before using this feature.", "i18n_765d09eea5": "The current file is not readable, you need to configure readable file authorization", "i18n_767fa455bb": "directory", "i18n_768e843a3e": "Class 192", "i18n_769d88e425": "complete", "i18n_76aebf3cc6": "log size", "i18n_76ebb2be96": "1 minute", "i18n_77017a3140": "associative container tag", "i18n_770a07d78f": "When there is no corresponding script in the target workspace, a new script will be automatically created", "i18n_771d897d9a": "status code", "i18n_77373db7d8": "Receive alarm message, optional, GET request", "i18n_7737f088de": "batch restart", "i18n_773b1a5ef6": "Please select a language mode", "i18n_775fde44cf": "Process port cache:", "i18n_7760785daf": "free script", "i18n_7764df7ccc": "Enabling differential releases but not empty releases is equivalent to only incremental and change updates", "i18n_77688e95af": "Container rebuilding refers to recreating an identical container using the container parameters that have already been created.", "i18n_776bf504a4": "Upload prompt", "i18n_7777a83497": "Please enter build notes, length less than 240", "i18n_77834eb6f5": "You use this system.", "i18n_7785d9e038": "The node ID is not stored in the lower version of the project data, and the corresponding project data will also come out in the lonely data (such data does not affect the use).", "i18n_77b9ecc8b1": "backup name", "i18n_77c1e73c08": "Script storage path: {'${user.home}'}/.jpom/xxxx.sh, script execution path: {'${user.home}'}, script execution method: bash {'${user.home}'}/.jpom/xxxx.sh par1 par2", "i18n_77c262950c": "Import multiple projects at once using Access Tokens", "i18n_77e100e462": "No status message yet", "i18n_77e501b44b": "Log file:", "i18n_780afeac65": "Whether to turn it on", "i18n_780fb9f3d0": "Update time:", "i18n_7824ed010c": "Do you really want to cancel the current posting task?", "i18n_7854b52a88": "enable", "i18n_787fdcca55": "System Configuration", "i18n_788a3afc90": "Lost contact", "i18n_78a4b837e3": "IP that can communicate", "i18n_78b2da536d": "The build process requests the corresponding address, starts building, builds complete, starts publishing, publishes complete, builds exceptions, publishes exceptions", "i18n_78ba02f56b": "Do you really want to completely delete the distribution information? After deleting, the items under the node will also be completely deleted, and the project-related files will be automatically deleted (including project logs, log backups, and project files).", "i18n_78caf7115c": "task name", "i18n_78dccb6e97": "All nodes (plug-in side)", "i18n_79076b6882": "Do you really want to delete these build information in bulk? Deletion will also delete all build history information synchronously. If the deletion fails halfway through, the deletion operation will be terminated.", "i18n_7912615699": "connection status", "i18n_791870de48": "Warehouse password", "i18n_791b6d0e62": "Ranking is sorted alphabetically a-z", "i18n_79698c57a2": "The current workspace has no nodes yet", "i18n_798f660048": "template node", "i18n_799ac8bf40": "Support variable references: {'${TASK_ID }'}、{'${ FILE_ID }'}、{'${ FILE_NAME }'}、{'${ FILE_EXT_NAME}'}、{'${RELEASE_PATH}'}", "i18n_79a7072ee1": "Token URL", "i18n_79c6b6cff7": "association grouping", "i18n_79d3abe929": "copy", "i18n_7a28e9cd4a": "When continuous anomalies are detected, the monitoring notification will be sent successfully and will not be repeated for a period of time", "i18n_7a30792e2a": "Edit SSH", "i18n_7a3c815b1e": "file directory", "i18n_7a4ecc606c": "Mirror tags, such as: key1 = values1 & keyvalue2 use URL encoding", "i18n_7a5dd04619": "Note that the execution of relevant commands requires a corresponding environment in the server where it is located", "i18n_7a7e25e9eb": "Are you sure you want to move this data down? The move down operation may be invalid because the subsequent data of the list does not have a sorted value operation!", "i18n_7a811cc1e5": "copy ", "i18n_7a93e0a6ae": "Select the enterprise version or purchase a license.", "i18n_7aa81d1573": "Please enter a file name", "i18n_7aaee3201a": "If you need to delete it, you need to back it up in advance or it has been determined that the corresponding file is deprecated before deleting it!!!!", "i18n_7afb02ed93": "There are currently no environment variables to reference", "i18n_7b2cbfada9": "Stop before publishing:", "i18n_7b36b18865": "partition ID", "i18n_7b61408779": "#Project file backup path", "i18n_7b8e7d4abc": "Do you really want to delete the execution record?", "i18n_7b961e05d0": "Indicates the last day of the month", "i18n_7bcbf81120": "receive packet", "i18n_7bcc3f169c": "#Built-in variable {'${JPOM_WORKING_DIR}'} {'${JPOM_BUILD_ID}'}", "i18n_7bf62f7284": "Manually cancel distribution", "i18n_7c0ee78130": "build log", "i18n_7c223eb6e9": "Release system announcement", "i18n_7c9bb61536": "log item name", "i18n_7cb8d163bb": "variable name", "i18n_7cc3bb7068": "Nodes are not actually requested to delete project information", "i18n_7ce511154f": "Cannot be modified after creation", "i18n_7d23ca925c": "Server level time", "i18n_7d3f2fd640": "Search in file lines 3 - 2147483647", "i18n_7ddbe15c84": "network", "i18n_7dde69267a": "Grouping of unbound clusters:", "i18n_7de5541032": "If ssh does not configure the authorization directory, it cannot be selected.", "i18n_7dfc7448ec": "Do you really want to delete the warehouse information?", "i18n_7dfcab648d": "product", "i18n_7e000409bb": "It is prone to mining", "i18n_7e1b283c57": " add", "i18n_7e2b40fc86": "Select Node", "i18n_7e300e89b1": "Distributed successfully", "i18n_7e33f94952": "If you want to execute the command after switching the path, you need to", "i18n_7e359f4b71": "Total number of hard disks:", "i18n_7e58312632": "Edit Log Search", "i18n_7e866fece6": "Please enter 2-step verification code", "i18n_7e930b95ef": "publish file", "i18n_7e951d56d9": "operating time", "i18n_7e9f0d2606": "Project refers to a certain project in the node, and the project needs to be created in the node in advance", "i18n_7ef30cfd31": "Additional environment variables refer to reading the environment variables file specified by the warehouse to add to the execution build runtime", "i18n_7f0abcf48d": "You need to go to the editor to bind an ssh information for a node to enable this function.", "i18n_7f3809d36b": "end of build", "i18n_7f5bcd975b": "CPU usage", "i18n_7f7c624a84": "batch operation", "i18n_7f7ee903da": "Publish hidden files", "i18n_7fb5bdb690": "Software Acknowledgments", "i18n_7fb62b3011": "batch delete", "i18n_7fbc0f9aae": "Execution time starts", "i18n_7fc88aeeda": "Change Password", "i18n_800dfdd902": "Today.", "i18n_80198317ff": "And recommend or share with your friends:", "i18n_8023baf064": "notification status", "i18n_80669da961": "CPU usage", "i18n_807ed6f5a6": "No data yet", "i18n_8086beecb3": "Tag name:", "i18n_808c18d2bb": "A value of true indicates that the project is currently running", "i18n_809b12d6a0": "Please be patient, there is no need to refresh the page for the time being.", "i18n_80cfc33cbe": "Confirm reset", "i18n_81301b6813": "Open end point", "i18n_81485b76d8": "Please enter the host address", "i18n_814dd5fb7d": "Do you really want to delete the backup information?", "i18n_815492fd8d": "Legacy packages take up space", "i18n_8160b4be4e": "Abnormal shutdown", "i18n_819767ada1": "username", "i18n_8198e4461a": "Project:", "i18n_81afd9e713": "queue waiting", "i18n_81c1dff69c": "solution", "i18n_81d7d5cd8a": "Detailed description of the command", "i18n_81e4018e9d": "dangling type", "i18n_82416714a8": "Ports to be tested", "i18n_824607be6b": "retention days", "i18n_824914133f": "No script library", "i18n_8283f063d7": "Project complete catalog", "i18n_828efdf4e5": "number of MFA enabled", "i18n_82915930eb": "concurrent execution", "i18n_829706defc": "Online build (build associated repositories, build history)", "i18n_829abe5a8d": "group", "i18n_82b89bd049": "The log pop-up window will open in full screen.", "i18n_82d2c66f47": "batch distribution", "i18n_8306971039": "user", "i18n_8309cec640": "Please select the node project. It may be that there is no project in the node, and you need to go to the node to create a project.", "i18n_833249fb92": "Current file time", "i18n_8347a927c0": "modify", "i18n_835050418f": "Are you sure you want to upload the latest plugin package?", "i18n_8351876236": "<PERSON><PERSON>", "i18n_83611abd5f": "publish", "i18n_8363193305": "Please enter the callback redirect url [redirectUri]", "i18n_8388c637f6": "self-starting", "i18n_83aa7f3123": "distribution id", "i18n_83c61f7f9e": "Please select a monitoring user", "i18n_83ccef50cd": "When the target workspace already exists, the script will be automatically synchronized, the script content, default parameters, scheduled execution, description", "i18n_83f25dbaa0": "Bind node", "i18n_8400529cfb": "Reset custom process name information", "i18n_8432a98819": "operation function", "i18n_843f05194a": "Show all", "i18n_84415a6bb1": "Reset the download token information, the previous download token will be invalid after reset", "i18n_844296754e": "virtual memory", "i18n_84592cd99c": "It can be understood as a directory packaged for the project. Such as Jpom project execution (build command)", "i18n_84597bf5bc": "Alibaba Cloud Enterprise Email Configuration", "i18n_84632d372f": "Click to view details", "i18n_84777ebf8b": "Safety reminder", "i18n_847afa1ff2": "Please enter IP authorization, multiple use newline, 0.0.0.0 open all IP, support configuration IP segment ***********/*************, ***********/24", "i18n_848c07af9b": "admin panel", "i18n_848e4e21da": "Such as: --server", "i18n_8493205602": "open", "i18n_84aa0038cf": "system log", "i18n_84b28944b7": "Timeout (S)", "i18n_84d331a137": "Seconds (if the value is too small, the node state may not be obtained)", "i18n_84e12f7434": "Session has been closed [ssh-terminal]", "i18n_853d8ab485": "Building now.", "i18n_85451d2eb5": "Please enter a variable value", "i18n_8580ad66b0": "Do you really want to delete the project completely? A thorough project will automatically delete project-related files (including project logs, log backups, and project files)", "i18n_85be08c99a": "No data was found", "i18n_85cfcdd88b": "Local build refers to the execution of build commands directly on the server at the server level", "i18n_85da2e5bb1": "<PERSON><PERSON>, please wait...", "i18n_85ec12ccd3": "Delay, container upgrade interval", "i18n_85f347f9d0": "User restrictions Users can only operate the corresponding functions in the corresponding workspace", "i18n_85fe5099f6": "cluster", "i18n_86048b4fea": "remove", "i18n_860c00f4f7": "per hour", "i18n_863a95c914": "Do you really want to save the current configuration? If the configuration is wrong, the service may not be started. You need to manually restore Austria!!!", "i18n_867cc1aac4": " : Range: 0~ 23", "i18n_869b506d66": "For independent project distribution, please go to the distribution management to modify it.", "i18n_869ec83e33": "unused", "i18n_86b7eb5e83": "Do you need to delete all associated data before deleting the current workspace?", "i18n_86c1eb397d": "Switch account", "i18n_86cd8dcead": "startup time", "i18n_86e9e4dd58": "Warehouse lastcommit", "i18n_86f3ec932c": "read size", "i18n_86fb7b5421": "Node account", "i18n_8704e7bdb7": "Please enter the token url [accessTokenUri]", "i18n_871cc8602a": "secondary directory", "i18n_8724641ba8": "Interval (/) > Interval (-) > List (,)", "i18n_872ad6c96e": "Support referencing global script libraries (G{'@'}\"xx\") xx is the script tag)", "i18n_8756efb8f4": "Do you really want to delete the current folder?", "i18n_87659a4953": "Are you sure you want to close the beta program?", "i18n_8780e6b3d1": "file management", "i18n_878aebf9b2": "login name", "i18n_87d50f8e03": "Container ID", "i18n_87db69bd44": "Restrict resources", "i18n_87dec8f11e": "Incorrect workspace data", "i18n_87e2f5bf75": "append script template", "i18n_87eb55155a": "Number of lines:", "i18n_8813ff5cf8": "If the environment variables are installed and configured after starting the server level, you need to restart the server level through the end point command to take effect", "i18n_883848dd37": "Actual memory usage", "i18n_8844085e15": "0 a.m.", "i18n_884ea031d3": "Please enter a variable description", "i18n_8887e94cb7": "Sequential execution (if the execution fails, it will continue)", "i18n_888df7a89e": "Not recommended.", "i18n_88ab27cfd0": "Group/Tag:", "i18n_88b4b85562": "Packaging pre-release environment npm i & & npm run build: stage", "i18n_88b79928e7": "Certificate lost", "i18n_88c5680d0d": "Management status:", "i18n_88c85a2506": "The newly added node (plug-in side) will automatically", "i18n_88e6615734": "Unbinding will check the data correlation, and will automatically delete the node item and script cache information. Generally, the server cannot be connected and has been determined to be no longer used.", "i18n_88f5c7ac4a": "Please select a sort field", "i18n_8900539e06": "write size", "i18n_89050136f8": "post-publication action", "i18n_891db2373b": "auto refresh", "i18n_897d865225": "Number of mirrors:", "i18n_89944d6ccb": "Labels are limited to alphanumeric and 1-10 in length.", "i18n_899dbd7b9a": "CPU model", "i18n_899fe0c5dd": "The node address is the IP: PORT of the plug-in side. The default port of the plug-in side is: 212.", "i18n_89a40a1a8b": "Distribution process request, optional, GET request", "i18n_89cfb655e0": "container name tag", "i18n_89d18c88a3": "Please enter a file task name", "i18n_89f5ca6928": "Support wild-card", "i18n_8a1767a0d2": "After turning on this option, hidden files can be published normally.", "i18n_8a3e316cd7": "Do not code", "i18n_8a414f832f": "cluster address", "i18n_8a49e2de39": "QQ mailbox configuration", "i18n_8a4dbe88b8": "Click to enter node management", "i18n_8a745296f4": "Boot time:", "i18n_8aa25f5fbe": "release type", "i18n_8ae2b9915c": "Please fill in the", "i18n_8aebf966b2": "cluster access address", "i18n_8b1512bf3a": "If the port", "i18n_8b2e274414": "Last Reloaded Results", "i18n_8b3db55fa4": "Cluster ID:", "i18n_8b63640eee": "Account is disabled", "i18n_8b6e758e4c": "Hover over the dashboard to reveal the specific meaning", "i18n_8b73b025c0": "Simple to use, but does not support key export backups", "i18n_8b83cd1f29": "Name of mirror to pull", "i18n_8ba971a184": "private token", "i18n_8ba977b4b7": "#Restrict backup to specified file suffixes (regular support)", "i18n_8bd3f73502": "node password", "i18n_8be76af198": "163 mailbox configuration", "i18n_8be868ba1b": "Class 10", "i18n_8c0283435b": ": represents a continuous interval, e.g. in points, indicating 2, 3, 4, 5, 6, 7, 8 points", "i18n_8c24b5e19c": "Please use the application scan code to bind the token, and then enter the verification code to confirm the binding before it takes effect.", "i18n_8c2da7cce9": "No certificate", "i18n_8c4db236e1": "Please enter a script tag. The tag can only be letters or numbers. The length needs to be less than 20 and it is globally unique.", "i18n_8c61c92b4b": "backup type", "i18n_8c66392870": "Requires ssh-keygen -m PEM -t rsa -b 4096 -C", "i18n_8c67370ee5": "If the product is synced to the file center, the current value is shared", "i18n_8c7c7f3cfa": "Server level scripting", "i18n_8c7ce1da57": "After enabling dockerTag version increment, the last digit of the version number will be automatically synchronized to the build sequence ID during each build, such as: the current build is the 100th build testtag: 1.0 - > testtag: 1.100, testtag: 1.0.release - > testtag: 1.100.release. If there is no matching number, the increment operation will be ignored.", "i18n_8c7d19b32a": "Memory Node (MEM) allowed for execution (0-3, 0, 1). Valid only on NUMA systems.", "i18n_8cae9cb626": "Light color idea", "i18n_8ccbbb95a4": "Please fill in the remote URL.", "i18n_8cd628f495": "All IP addresses:", "i18n_8d0fa2ee2d": "Please enter the port number", "i18n_8d1286cd2e": "No distribution logs", "i18n_8d13037eb7": "Status message:", "i18n_8d202b890c": "Bulk trigger address", "i18n_8d3d771ab6": "edit cluster", "i18n_8d5956ca2a": "Configure in yaml/yml format, scriptId is the relative path of the script file under the project path or the server level script template ID, you can view the scriptId in the server level script template editing pop-up window", "i18n_8d5c1335b6": "Container name numeric letters, and the length is greater than 1", "i18n_8d62b202d9": "Please select the file to use", "i18n_8d63ef388e": "pause", "i18n_8d6d47fbed": "#Execute in the specified directory:./project directory /root/specific directory, the default is {'${jpom_agent_data_path}'}/script_run_cache ", "i18n_8d6f38b4b1": "file description", "i18n_8d90b15eaf": "#host file upload to container /host:/container: true", "i18n_8d92fb62a7": "Please select the template node", "i18n_8d9a071ee2": "import", "i18n_8da42dd738": "Second level (second level is not enabled by default, you need to modify the configuration file: [system.timerMatchSecond])", "i18n_8dbe0c2ffa": "Occupy space:", "i18n_8dc09ebe97": "obtain", "i18n_8dc8bbbc20": "file system", "i18n_8de2137776": "cluster task", "i18n_8e2ed8ae0d": "[Independent distribution]", "i18n_8e331a52de": "Only allowed IP addresses", "i18n_8e34aa1a59": "Configure this machine node as a template", "i18n_8e389298e4": "export image", "i18n_8e38d55231": "Do you really want to log out of the system completely? Logging out completely will log out and clear the browser cache", "i18n_8e54ddfe24": "start", "i18n_8e6184c0d3": "Projects may support linking the following data:", "i18n_8e6a77838a": "Please select the machine node to distribute to", "i18n_8e872df7da": "Be careful that the entire line cannot contain spaces", "i18n_8e89763d95": "Host IP", "i18n_8e8bcfbb4f": "Are you sure you want to trim the corresponding information? Trimming will automatically clean the corresponding data.", "i18n_8e9bd127fb": "The authorization directory needs to be configured for the workspace in advance", "i18n_8ea4c3f537": "Search from the end, the first 100 lines of the file, and the last 100 lines of the file", "i18n_8ea93ff060": "Node script templates are command scripts stored in nodes for online management of some script commands, such as initializing software environments, managing applications, etc", "i18n_8ef0f6c275": "Close the beta program", "i18n_8f0bab9a5a": "Number of log files being read", "i18n_8f0c429b46": "Migration operations are not transactional in nature and may generate redundant data if the process is interrupted or constraints are not met!!!!", "i18n_8f36f2ede7": "Workspace name:", "i18n_8f3747c057": "service name", "i18n_8f40b41e89": "Expiration time:", "i18n_8f7a163ee9": "Quick installation of plug-ins", "i18n_8f8f88654f": "No node information yet", "i18n_8fb7785809": "If there is encryption in the process of generating the private key, then you need to fill the encrypted password into the password box above", "i18n_8fbcdbc785": "Please enter an alias code", "i18n_8fd9daf8e9": "Guaranteed to use ignoble TLS certificates on the intranet", "i18n_8fda053c83": "write count", "i18n_8ffded102f": "If you need to automatically build regularly, fill in the cron expression. The second level is not turned on by default, you need to modify the configuration file: [system.timerMatchSecond])", "i18n_900c70fa5f": "warning", "i18n_9014d6d289": "backup list", "i18n_90154854b6": "Please enter host", "i18n_901de97cdb": "Mode Request interface parameters are passed into the request body ContentType Please use: text/plain", "i18n_903b25f64e": "unknown state", "i18n_904615588b": "File type does not have console functionality", "i18n_9057ac9664": "Please select the trigger type", "i18n_9065a208e8": "To download the remote file to the project folder through the URL, you need to configure the allowed HOST authorization in the authorization directory configuration under the corresponding workspace.", "i18n_906f6102a7": "Restart successful", "i18n_9086111cff": "Associated workspace docker", "i18n_90b5a467c1": "refresh directory", "i18n_90c0458a4c": "Import backup", "i18n_90eac06e61": "host directory", "i18n_912302cb02": "Browser", "i18n_9136e1859a": "Docker image", "i18n_913ef5d129": "Perform a restart", "i18n_916cde39c4": "All parameters will be concatenated into strings to execute the script separated by spaces. Parameters that need to pay attention to the order of parameters and unfilled values will be automatically ignored", "i18n_916ff9eddd": "Please enter a nickname", "i18n_917381e4a5": "Current download source:", "i18n_91985e3574": "automatic detection", "i18n_91a10b8776": " script library ", "i18n_920f05031b": "state description", "i18n_922b76febd": "Run mode Required", "i18n_923f8d2688": "Post-issue command", "i18n_9255f9c68f": "Session has been closed [tail-file]", "i18n_92636e8c8f": "skip", "i18n_9282b1e5da": "WeCom scan code", "i18n_929e857766": "Certificate type", "i18n_92c6aa6db9": "If docker exists in your SSH machine but the system does not detect it, you need to go to [Configuration Management] -", "i18n_92dde4c02b": "ad serving", "i18n_92e3a830ae": "help", "i18n_92f0744426": "Container building refers to the use of docker containers to execute builds, which can be isolated from the host environment without installing a dependent environment", "i18n_92f3fdb65f": "Warehouse:", "i18n_92f9a3c474": "The page will automatically refresh after switching languages", "i18n_9300692fac": "Markup Reference", "i18n_9302bc7838": "Please enter the port to check", "i18n_930882bb0a": "a", "i18n_9308f22bf6": "In a single trigger address: the first random string is the script ID, and the second random string is the token.", "i18n_930fdcdf90": "Configuration name (e.g. size)", "i18n_9324290bfe": "Such as: key1", "i18n_932b4b7f79": "Attention: Priority in each subexpression:", "i18n_934156d92c": "Create a distribution project", "i18n_9341881037": "Are you sure you want to take a batch build? Note: Running multiple builds at the same time will consume a large amount of resources. Please use batch builds with caution. If the number of batch builds exceeds the number of build task queues waiting, the build task will be automatically cancelled.", "i18n_935b06789f": "You have not performed the operation yet.", "i18n_9362e6ddf8": "Dangerous operation!!!", "i18n_938246ce8b": "Task Template", "i18n_938dd62952": "execution path", "i18n_939d5345ad": "submit", "i18n_93e1df604a": "machine grouping", "i18n_93e894325d": "batch boot", "i18n_9402665a2c": " Continuous search (dialog box does not close automatically, press Enter to find the next one, press Shift-Enter to find the previous one)", "i18n_9412eb8f99": "Please fill in the platform address.", "i18n_9443399e7d": " , the range is 1970~ 2099, but the 7th bit is not parsed or matched", "i18n_94763baf5f": "You can go to [Plug-in Side Configuration] = > [Authorization Configuration] in the node management to modify it.", "i18n_947d983961": "Warm reminder", "i18n_948171025e": "Session has been closed [docker-log]", "i18n_949934d97c": "oversized", "i18n_949a8b7bd2": "column settings", "i18n_94aa195397": "certificate file", "i18n_94ca71ae7b": "Please select the certificate to use", "i18n_94d4fcca1b": "Create account", "i18n_952232ca52": "The build history may occupy a lot of hard disk space. It is recommended to configure the number of reservations according to the actual situation", "i18n_953357d914": "Ignore check state", "i18n_953ec2172b": "Unrestarted successfully:", "i18n_954fb7fa21": "Do you really want to delete the project? Deleting the project will not delete the project-related files. It is recommended to clean the project-related files first and then delete the project.", "i18n_956ab8a9f7": "Profile? Once a profile is created, it cannot be deleted through the admin page.", "i18n_957c1b1c50": "cluster node", "i18n_95a43eaa59": "founder", "i18n_95b351c862": "edit", "i18n_95c5c939e4": "The selectable list and the project authorization directory are the same, i.e. the same configuration", "i18n_95dbee0207": "Remote Download Secure HOST", "i18n_96283fc523": "Backup file does not exist", "i18n_964d939a96": " Long name:", "i18n_969098605e": "Environment variables refer to some fixed parameter values that are configured in the system and are used for quick reference during script execution.", "i18n_96972aa0df": "#Do you want to enable differential backup (only backup changed files)", "i18n_96b78bfb6a": "Do not manually delete the files below the data directory!!!!", "i18n_96c1c8f4ee": "Grey green abcdef", "i18n_96c28c4f17": "Which cluster to join", "i18n_96d46bd22e": "manual refresh statistics", "i18n_96e6f43118": "Container runtime", "i18n_974be6600d": "Password must contain numbers, letters, characters, and be greater than 6 digits", "i18n_977bfe8508": "Label (TAG)", "i18n_979b7d10b0": "build break", "i18n_97a19328a8": "Open now", "i18n_97cb3c4b2e": "Workspace environment variables for building command correlation", "i18n_97d08b02e7": "Network port test", "i18n_97ecc1bbe9": "output flow", "i18n_981cbe312b": "to", "i18n_9829e60a29": "live version number", "i18n_98357846a2": "Table view to use batch operation function", "i18n_983f59c9d4": "CAPTCHA", "i18n_9878af9db5": "Please go to [System Administration] - > [Asset Management] - > [Docker Management] to add <PERSON><PERSON> and create a cluster, or associate and assign existing Docker cluster authorizations to this workspace", "i18n_9880bd3ba1": "This tool is used to check whether the cron expression is correct and to schedule the running time", "i18n_989f1f2b61": "Do you really want to restart the project?", "i18n_98a315c0fc": "authorization", "i18n_98cd2bdc03": "Table view to use the synchronization configuration feature", "i18n_98d69f8b62": "Workspace", "i18n_98e115d868": "Running timed tasks", "i18n_9914219dd1": "Search from scratch", "i18n_9932551cd5": "memory", "i18n_993a5c7eee": "#Configuration instructions: https://docs.docker.com/engine/api/v1.43/#tag/Container/operation/ContainerCreate", "i18n_99593f7623": "Client side ID", "i18n_9964d6ed3f": "mount", "i18n_996dc32a98": "System type", "i18n_9970ad0746": "theme", "i18n_9971192b6a": "Such as: http", "i18n_9973159a4d": "Match a character", "i18n_998b7c48a8": "View container", "i18n_99b3c97515": "hour level", "i18n_99cba05f94": "Do you really want to delete SSH? Scripts associated with the current ssh will be invalid after deletion", "i18n_99d3e5c718": " Start searching", "i18n_99f0996c0a": "Please select a log directory", "i18n_9a00e13160": "In a single trigger address: the first random string is the project ID (server level), the second random string is the token", "i18n_9a0c5b150c": "Edit, command", "i18n_9a2ee7044f": "variable value", "i18n_9a436e2a53": "Trim objects with specified labels, multiple separated by commas", "i18n_9a4b872895": "cluster operation", "i18n_9a56bb830e": "user nickname", "i18n_9a77f3523e": "Mirror tag", "i18n_9a7b52fc86": "all", "i18n_9a8eb63daf": "Configure Workspace Permissions", "i18n_9ab433e930": "Other configurations", "i18n_9ac4765895": "An integer value that represents the relative CPU weight of this container relative to other containers.", "i18n_9adf43e181": "Number under construction", "i18n_9ae40638d2": "Deployment certificate", "i18n_9af372557e": "server port", "i18n_9aff624153": "monitor", "i18n_9b0bc05511": "Search on file lines 1 - 100", "i18n_9b1c5264a0": "After uploading", "i18n_9b280a6d2d": "Node:", "i18n_9b3e947cc9": "Node status:", "i18n_9b5f172ebe": "bind cluster", "i18n_9b7419bc10": "QQ mailbox", "i18n_9b74c734e5": "The node account password is the account password of the plug-in side, not the user account (administrator) password.", "i18n_9b78491b25": "Please enter the authorization path. Carriage return supports entering multiple paths. The system will automatically filter../path and do not allow entering the root path.", "i18n_9b7ada2613": " : Range: 1 to 31,", "i18n_9b9e426d16": "Number of log files read:", "i18n_9ba71275d3": "Please be patient. There is no need to refresh the page for the time being. It will be automatically refreshed after the upgrade is successful.", "i18n_9baca0054e": "Modifier", "i18n_9bbb6b5b75": "Do you really want to delete the log search?", "i18n_9bd451c4e9": "Node already exists", "i18n_9be8ff8367": "Support variable substitution: {'${BUILD_ID }'}、{'${ BUILD_NAME }'}、{'${ BUILD_RESULT_FILE }'}、{'${ BUILD_NUMBER_ID}'}", "i18n_9bf4e3c9de": "Creating a distribution project means creating a new node that belongs to the project and distributing it to the project. After the creation is successful, the project information will be automatically synchronized to the corresponding node, and the modified node distribution information will also be automatically synchronized to the corresponding node", "i18n_9bf5aa6672": "Web socket error, please check if ws proxy is enabled", "i18n_9c19a424dc": "Please enter the original password", "i18n_9c2a917905": "search command", "i18n_9c2f1d3f39": "The root inside has true root privileges.", "i18n_9c3a3e1b03": "There is no automatic deletion of the parent", "i18n_9c3a5e1dad": "Please go to [System Management] - > [Asset Management] - > [Machine Management] to add a node, or associate and assign the newly added machine authorization to this workspace.", "i18n_9c3c05d91b": "Node address It is recommended to use the intranet address", "i18n_9c55e8e0f3": "The CPU allowed to execute (e.g., 0-3, 0, 1).", "i18n_9c66f7b345": "Do you really want to delete the machine? Delete will check data correlation", "i18n_9c84cd926b": "The new machine also needs to bind the workspace, because we recommend that different clusters be allocated to different workspaces for management", "i18n_9c942ea972": "The certificate generation method can refer to the documentation) to connect to docker to improve security", "i18n_9c99e8bec9": "If not, publish to the root directory of the project", "i18n_9cac799f2f": "Select group name", "i18n_9caecd931b": "field", "i18n_9cd0554305": "If you don't need to keep more build history information, you can go to the server level to modify the build-related configuration parameters", "i18n_9ce5d5202a": "Running Jar packages:", "i18n_9d577fe51b": "File source", "i18n_9d5b1303e0": "When creating a cluster, you will try to get the cluster information in docker. If there is cluster information, the cluster information will be automatically synchronized to the system. Otherwise, if there is no cluster information, a swarm cluster will be automatically created.", "i18n_9d7d471b77": "Please select a node role", "i18n_9d89cbf245": "distribution name", "i18n_9dd62c9fa8": "End point command unlimited", "i18n_9ddaa182bd": "Plugin end time:", "i18n_9de72a79fe": "View file", "i18n_9e09315960": "rebuild", "i18n_9e0c797c04": "Select the language used for monitoring", "i18n_9e2e02ef08": "distribution type", "i18n_9e4ae8a24f": "DingTalk scan code", "i18n_9e560a4162": "Way to generate SSH key", "i18n_9e5ffa068e": "Basic information", "i18n_9e6b699597": "nanoCPUs Minimum 1000000", "i18n_9e78f02aad": "Parameter description, parameter description has no practical effect, it is only used to prompt the meaning of parameters", "i18n_9e96d9c8d3": "System load:", "i18n_9e98fa5c0d": "File name bar supports right-click menu", "i18n_9ec961d8cb": "Select bundle", "i18n_9ee0deb3c8": "The network is deserted! Please try again...:", "i18n_9ee9d48699": "Modification is not supported after creation", "i18n_9f01272a10": " legal risk", "i18n_9f0de3800b": "Please fill in the warehouse name.", "i18n_9f4a0d67c6": "It is recommended not to have blank spaces, tables, other blanks, \\,/,:, *,?, _ # # _,<,>, \\, in the file name{'|'} Waiting for special characters", "i18n_9f52492fbc": "For configuration details, please refer to the configuration example.", "i18n_9f6090c819": "The incoming parameters are: buildId, buildName, type, statusMsg, triggerTime", "i18n_9f6fa346d8": "Please enter an SSH name", "i18n_9f70e40e04": "running time", "i18n_9fb12a2d14": "The command to execute after publishing (non-blocking command), usually the start project command, such as: ps -aux {'|'} grep java, supports variable substitution: {'${BUILD_ID }'}、{'${ BUILD_NAME }'}、{'${ BUILD_RESULT_FILE }'}、{'${ BUILD_NUMBER_ID}'} ", "i18n_9fb61a9936": "version increment", "i18n_9fc2e26bfa": "Please select an item", "i18n_9fca7c455f": "login time", "i18n_9febf31146": "Please select a file", "i18n_9ff5504901": "Incoming environment variables are: buildId, buildName, type, statusMsg, triggerTime, buildNumberId, buildSourceFile", "i18n_a001a226fd": "update time", "i18n_a03c00714f": "batch shutdown", "i18n_a03ea1e864": "Please select the node to distribute to", "i18n_a04b7a8f5d": "A single trigger request supports parsing parameters into environment variables and passing them into the script for execution. For example, the passed parameter name abc = efg is introduced in the script as: {'${trigger_abc}'}", "i18n_a050cbc36d": "Your browser version is too low to support this feature", "i18n_a056d9c4b3": "Select script", "i18n_a05c1667ca": "Building history", "i18n_a08cbeb238": "And configure the correct environment variables", "i18n_a09375d96c": "dangling", "i18n_a093ae6a6e": "Automatic renewal", "i18n_a0a111cbbd": "Distributing items -", "i18n_a0a3d583b9": "Total memory:", "i18n_a0b9b4e048": "Please enter client side ID [clientId]", "i18n_a0d0ebc519": "global proxy", "i18n_a0e31d89ff": "It is generally recommended to take more than 10 seconds.", "i18n_a0f1bfad78": "Data directory size includes: temporary files, online build files, database files, etc", "i18n_a11cc7a65b": "Please enter content", "i18n_a13d8ade6a": "The associated node data is acquired asynchronously with a certain time lag", "i18n_a14da34559": "resource monitoring exception", "i18n_a156349591": " view", "i18n_a1638e78e8": "Match lines containing a or b", "i18n_a17450a5ff": "Select compressed file", "i18n_a17b5ab021": "The current file already exists", "i18n_a17b905126": "Automatically recognize keywords as privacy variables", "i18n_a17bc8d947": "The console log is only the log information output by the startup project, not the project log. You can turn off the console log backup function:", "i18n_a189314b9e": "Do not publish", "i18n_a1a3a7d853": "ready-made generation", "i18n_a1b745fba0": "Please enter a backup name", "i18n_a1bd9760fc": "timed task", "i18n_a1c4a75c2d": "It is an open-source software. If you feel good about using this project, or want to support us as we continue to develop, you can support us in the following ways:", "i18n_a1da57ab69": "Alipay transfer", "i18n_a1e24fe1f6": "user time", "i18n_a1f58b7189": "Parameter {count} value", "i18n_a1fb7f1606": "script management", "i18n_a20341341b": "Show the first N lines", "i18n_a24d80c8fa": "The corresponding address will be requested for project start, stop, restart, and file changes.", "i18n_a25657422b": "variable name", "i18n_a2741f6eb3": "The variable names in the system environment variables include:", "i18n_a2a0f52afe": "If left blank, the default configuration in the $HOME/.ssh directory will be used. The priority is: id_dsa > id_rsa > identity.", "i18n_a2ae15f8a7": "build process", "i18n_a2e62165dc": "Do you really want to save the current configuration? IP authorization, please configure Austria carefully (authorization refers to IP that only allows access), and it will take effect immediately after configuration. If the configuration is wrong, it will be inaccessible, and you need to manually restore Austria!!!", "i18n_a2ebd000e4": "Do nothing.", "i18n_a3296ef4f6": "Full screen end point", "i18n_a33a2a4a90": "Scripts for server level synchronization cannot be modified here", "i18n_a34545bd16": "Build parameters, such as: key1 = values1 & keyvalue2 use URL encoding", "i18n_a34b91cdd7": "Environment variables can also be used for warehouse account passwords, ssh password references", "i18n_a34c24719b": "Start executing the task", "i18n_a35740ae41": "operation prompt", "i18n_a3751dc408": " Execution every minute at 12:00", "i18n_a37c573d7b": "This can be a Unix timestamp, a timestamp in date format, or a Go duration string (e.g. 10m, 1h30m), calculated relative to the time of the daemon machine.", "i18n_a38ed189a2": "Please read the instructions and precautions in the update log before uploading the update and", "i18n_a39340ec59": "prohibition order", "i18n_a396da3e22": "The current workspace has no projects and no nodes", "i18n_a3d0154996": "file status", "i18n_a3f1390bf1": "After modification, if there is original associated data, it will be invalid and the association needs to be reconfigured.", "i18n_a4006e5c1e": "Create backup", "i18n_a421ec6187": "Edit environment variables", "i18n_a4266aea79": "Do you really want to delete this service?", "i18n_a436c94494": "Feishu scan code", "i18n_a472019766": "Node Id", "i18n_a497562c8e": "executor", "i18n_a4f5cae8d2": "on state", "i18n_a4f629041c": "The path needs to be configured with an absolute path.", "i18n_a50fbc5a52": "Support specifying the network interface card name to bind:", "i18n_a51cd0898f": "container name", "i18n_a51d8375b7": "Select static file", "i18n_a52a10123f": "If the upgrade fails, you need to manually restore the Austrian", "i18n_a52aa984cd": "Do you really want to delete the permission group?", "i18n_a53d137403": "Session has been closed [free-script]", "i18n_a55ae13421": "Please configure the download authorization code", "i18n_a5617f0369": "SSH connection information", "i18n_a577822cdd": "Save and build", "i18n_a59d075d85": "Customize the clone depth to avoid all clones in large repositories", "i18n_a5d1c511d7": "Template Name", "i18n_a5d550f258": "interval time", "i18n_a5daa9be44": "Please check whether the package is complete before uploading, otherwise it may not start normally after the update!!", "i18n_a5e9874a96": "Please choose which docker cluster to publish to", "i18n_a5f84fd99c": "non-private", "i18n_a6269ede6c": "management node", "i18n_a62fa322b4": "The certificate will be packaged into a zip file and uploaded to the corresponding folder.", "i18n_a637a42173": "The chosen build history no longer exists", "i18n_a63fe7b615": "After being assigned to the workspace, you also need to configure the corresponding workspace in the association to use the Olympic Games perfectly.", "i18n_a657f46f5b": "week", "i18n_a66644ff47": "Class 172", "i18n_a66fff7541": "Remote download URL", "i18n_a6bf763ede": "machine node", "i18n_a6fc9e3ae6": "Upload file", "i18n_a74b62f4bb": "Hard disk information", "i18n_a75a5a9525": "Publish directory, bundle and upload to the corresponding directory", "i18n_a75b96584d": "Service Id", "i18n_a75f781415": "Server level menu", "i18n_a7699ba731": "Upload successful", "i18n_a76b4f5000": "number of administrators", "i18n_a77cc03013": "If you refer to the script library, you need to distribute the corresponding script to the machine node in advance to use it normally.", "i18n_a795fa52cd": "quit completely", "i18n_a7a9a2156a": "Please enter the confirmation password", "i18n_a7c8eea801": "Attempt to auto-renew successfully", "i18n_a7ddb00197": "Alibaba Cloud Enterprise Email SSL", "i18n_a805615d15": "The values of type are: startReady, pull, executeCommand, release, done, stop, success, error", "i18n_a810520460": "password", "i18n_a823cfa70c": "container label", "i18n_a84a45b352": "upgrade strategy", "i18n_a8754e3e90": "Fill in the correct Internet Protocol Address", "i18n_a87818b04f": "Wait to start", "i18n_a8920fbfad": "Please change the command path to the actual path in your server", "i18n_a89646d060": "After creating the workspace, the corresponding data needs to be managed separately in the corresponding workspace", "i18n_a8f44c3188": "The account number is the account used for the system-specific demonstration.", "i18n_a90cf0796b": "Information:", "i18n_a912a83e6f": "plugin version", "i18n_a918bde61d": "You have not yet built", "i18n_a91ce167c1": "File ID", "i18n_a9463d0f1a": "Search mode, by default, view the last number of lines in the file, search from the beginning refers to the search from the specified line down, and search from the end refers to the number of lines searched from the end of the file up", "i18n_a94feac256": "The loading speed is determined according to the network speed. If the network is not good, the download will be slower.", "i18n_a952ba273f": "Please note your intention when contacting.", "i18n_a9795c06c8": "No SSH", "i18n_a98233b321": "Recommendations for using the Microsoft Family Bucket", "i18n_a9886f95b6": "Are you sure you want to delete this script library?", "i18n_a9add9b059": "Data storage directory:", "i18n_a9b50d245b": "Do not bind", "i18n_a9c52ffd40": "Strictly implement:", "i18n_a9c999e0bd": "It is recommended to customize the file name in the uploaded script. SSH upload defaults to: {'${FILE_ID }'}.{'${ FILE_EXT_NAME}'}", "i18n_a9de52acb0": "How to operate", "i18n_a9eed33cfb": "If the version difference is large, you need to reinitialize the data to ensure that it is consistent with the fields in the current program.", "i18n_a9f94dcd57": "deploy", "i18n_aa53a4b93a": "No network interface information", "i18n_aa9236568f": "Statistical Trend", "i18n_aabdc3b7c0": "project path", "i18n_aac62bc255": "Click to view the log.", "i18n_aacd9caa4a": "View environment variables", "i18n_aad7450231": "Please enter the cluster you want to bind to", "i18n_aadf9d7028": "Used to download remote files for node distribution and file uploads", "i18n_aaeb54633e": "overload", "i18n_ab006f89e7": "manual delete", "i18n_ab13dd3381": "Login with your own Gitlab account", "i18n_ab3615a5ad": "Download the installation package", "i18n_ab3725d06b": "Session has been closed", "i18n_ab7f78ba4c": "Space ID (full match)", "i18n_ab968d842f": "Build image Try to update the new version of the base image", "i18n_ab9a0ee5bd": "Folder path, you need to dockerfile in the warehouse.", "i18n_ab9c827798": "No docker cluster", "i18n_abb6b7260b": "If you multiple select ssh, the directory below only displays the first item in the option, but the authorization directory needs to ensure that each item is configured with the corresponding directory", "i18n_abba4043d8": "Search from scratch, 20 lines before the file, 3 lines after the file", "i18n_abba4775e1": "command parameters", "i18n_abd9ee868a": "Network mode: bridge, container: < name {'|'} id >, host, container, none", "i18n_abdd7ea830": "Please enter a new password", "i18n_abee751418": "Container Id: ", "i18n_ac00774608": "first", "i18n_ac0158db83": "Task ID", "i18n_ac2f4259f1": "New Version:", "i18n_ac408e4b03": "Please select a certificate type", "i18n_ac5f3bfa5b": "Select items to monitor, file type items cannot be monitored", "i18n_ac762710a5": "Support custom sorting fields: sort", "i18n_ac783bca36": "Do you really want to log out and switch accounts to log in?", "i18n_acb4ce3592": "Please select a file in a static file", "i18n_acd5cb847a": "fail", "i18n_ace71047a0": "Please go to [System Management] - > [Asset Management] - > [SSH Management] to add SSH, or associate and assign the newly added SSH authorization to this workspace", "i18n_acf14aad3c": "There is no need to configure one by one. After configuration, the previous configuration will be overwritten.", "i18n_ad209825b5": "Please select a trim type", "i18n_ad311f3211": "Please select a warehouse.", "i18n_ad35f58fb3": "occupied space", "i18n_ad4b4a5b3b": "host", "i18n_ad780debbc": "Rollback strategy", "i18n_ad8b626496": "Do you really want to delete the build history?", "i18n_ad9788b17d": "abnormal recovery", "i18n_ad9a677940": "Specify settings file package mvn -s xxx/settings.xml clean package", "i18n_adaf94c06b": "execution result", "i18n_adbec9b14d": "Create backup information", "i18n_adcd1dd701": "Back to list", "i18n_add91bb395": "logical node", "i18n_ade63665b2": "File merging in progress", "i18n_ae0d608495": "Whether to use MFA", "i18n_ae0fd9b9d2": "backup time", "i18n_ae12edc5bf": "Click Copy File Path", "i18n_ae17005c0c": "not joined", "i18n_ae35be7986": "<PERSON><PERSON>, full match", "i18n_ae653ec180": "detailed description", "i18n_ae6838c0e6": "Node distribution", "i18n_ae809e0295": "Suffix, precision search", "i18n_aeade8e979": "uninitialized", "i18n_aeb44d34e6": "One-time donation sponsorship", "i18n_aec7b550e2": "Delete Workspace Confirmation", "i18n_aed1dfbc31": "middle", "i18n_aef1a0752a": "Note: After configuring the project log encoding format, the \"log search\" function of the project will also follow this encoding format when reading log files", "i18n_aefd8f9f27": "Please select a restore method", "i18n_af013dd9dc": "It will be automatically refreshed after successful restart.", "i18n_af0df2e295": "You need to configure the file suffix that allows editing in the ssh information.", "i18n_af14cd6893": "Please fill in the build DSL configuration content, you can click the switch tab above to view the configuration example", "i18n_af3a9b6303": "WeCom scan code account login", "i18n_af427d2541": "data update time", "i18n_af4d18402a": "It has been disconnected.", "i18n_af51211a73": "A scroll bar will appear on the page content", "i18n_af708b659f": "Memory:", "i18n_af7c96d2b9": "The synchronization mechanism uses the container host to determine that it is the same server (docker).", "i18n_af83388834": "Trigger backup", "i18n_af924a1a14": "Abnormal download", "i18n_af98c31607": "Number of physical node projects:", "i18n_afa8980495": "Please enter the suffix and file encoding that allow editing of the file. If you do not set the encoding, the system encoding will be taken by default. Example: Set the encoding: txt {'@'} utf-8, do not set the encoding: txt", "i18n_afb9fe400b": "Usage rate:", "i18n_b04070fe42": "Select proxy type", "i18n_b04209e785": "Linked Data:", "i18n_b05345caad": "owner", "i18n_b07a33c3a8": "Please select a distribution node", "i18n_b095ceda99": "The selected script needs to configure the monitored event name in the script description to take effect", "i18n_b0b9df58fd": "SSH Node", "i18n_b0fa44acbb": "Occupancy:", "i18n_b10b082c25": "The values are: stop, beforeStop, start, beforeRestart", "i18n_b1192f8f8e": "Is it really necessary to cancel the current distribution?", "i18n_b11b0c93fa": "If the actual random access memory in the Linux may be too different from the values calculated by the free and total fields directly using the free -h command, then this is caused by the swap memory in your current server", "i18n_b12d003367": "privacy field", "i18n_b153126fc2": "Please enter a workspace name", "i18n_b15689296a": "risk notice", "i18n_b15d91274e": "close", "i18n_b166a66d67": "Are you sure you want to move this number up?", "i18n_b17299f3fb": "Plugin-side process ID:", "i18n_b1785ef01e": "Node name", "i18n_b186c667dc": "The distribution process requests the corresponding address, starts distribution, distribution completes, distribution fails, and cancels distribution.", "i18n_b188393ea7": "Published SSH", "i18n_b1a09cee8e": "empty restore", "i18n_b1dae9bc5c": "administrator", "i18n_b28836fe97": "The distribution ID is equivalent to the project ID.", "i18n_b28c17d2a6": " (MEM) (0-3, 0, 1). Valid only on NUMA systems.", "i18n_b29fd18c93": "Please select a project for publication", "i18n_b2f296d76a": "5 minutes", "i18n_b30d07c036": "Batch shutdown startup", "i18n_b328609814": "Administrators have: Partial permissions to manage the server", "i18n_b339aa8710": "form", "i18n_b33c7279b3": "authentication method", "i18n_b3401c3657": "container directory", "i18n_b341f9a861": "task time", "i18n_b343663a14": "Empty publishing means that before uploading a new file, all the files in the project folder directory will be deleted before saving the new file", "i18n_b36e87fe5b": "Do not execute, but compile test cases mvn clean package -DskipTests", "i18n_b37b786351": "group name", "i18n_b384470769": "synchronous cache", "i18n_b38d6077d6": "login IP", "i18n_b38d7db9b0": "Download the build log. If the button is not available, it means that the log file does not exist. Generally, the build history related files are deleted.", "i18n_b3913b9bb7": "Please enter the build environment variables: xx = abc multiple variables carriage return and line feed", "i18n_b399058f25": "Powerful and secure password management paid app", "i18n_b39909964f": "Please enter your email account", "i18n_b3b1f709d4": "cull", "i18n_b3bda9bf9e": "Please select a workspace", "i18n_b3ef35a359": "source warehouse", "i18n_b3f9beb536": ": 3 to 18 minutes, executed every 5 minutes, i.e. 0:03, 0:08, 0:13, 0:18, 1:03, 1:08...", "i18n_b3fe677b5f": "failure rate", "i18n_b408105d69": "The password field and key field will not be returned during editing. Please click me if you need to reset or clear it.", "i18n_b437a4d41d": "Also supports URL parameter format: test_par = 123abc & test_par2 = abc21", "i18n_b44479d4b8": "Available tags", "i18n_b4750210ef": "Cluster modification time:", "i18n_b499798ec5": "Disable distribution node", "i18n_b4a8c78284": "Select a workspace", "i18n_b4c83b0b56": "Warehouse account number", "i18n_b4dd6aefde": "Session has been closed [script-console]", "i18n_b4e2b132cf": "The plug-in side running port is used by default:", "i18n_b4fc1ac02c": "Cancel build", "i18n_b4fd7afd31": "Personality configuration", "i18n_b513f53eb4": "Timeout, in seconds", "i18n_b515d55aab": "You can upload it in advance through Certificate Management or click Select Certificate at the back to select/import the certificate.", "i18n_b53dedd3e0": "Commands executed before publishing (non-blocking commands), typically the close project command", "i18n_b55f286cba": "Please read the instructions and precautions in the update log before loading, and", "i18n_b56585aa18": "After configuration, you can control whether you want to prohibit users from operating certain functions for a certain period of time, and give priority to judging the disabled period", "i18n_b57647c5aa": "Do you really want to unbind the node associated with the script?", "i18n_b57ecea951": "Already running time:", "i18n_b5a1e1f2d1": "Trigger type:", "i18n_b5a6a07e48": "Tuesday", "i18n_b5b51ff786": "Upload SQL file", "i18n_b5c291805e": "initialization system", "i18n_b5c3770699": "console", "i18n_b5c5078a5d": "List of all IPv4 devices", "i18n_b5ce5efa6e": "cluster service", "i18n_b5d0091ae3": "Build ID", "i18n_b5d2cf4a76": "When the target workspace already exists, the script will be automatically synchronized, the script content, default parameters, automatic execution, description", "i18n_b5fdd886b6": "Full screen view log", "i18n_b60352bc4f": "virtual", "i18n_b6076a055f": "<PERSON><PERSON> failed", "i18n_b61a7e3ace": "Script name:", "i18n_b63c057330": "Do you really want to delete the operation monitoring?", "i18n_b650acd50b": "Restore default name", "i18n_b6728e74a4": "Run directory:", "i18n_b6a828205d": "cache build", "i18n_b6afcf9851": "Forbidden commands are commands that are not allowed to be executed at the end point, separated by multiple commas. (Superadmin has no restrictions)", "i18n_b6c9619081": "Port:", "i18n_b6e8fb4106": "platform login", "i18n_b6ee682dac": "Number of plugins:", "i18n_b714160f52": "distribution project ID", "i18n_b71a7e6aab": "local command", "i18n_b7579706a3": "check", "i18n_b7c139ed75": "If the project directory is large or involves deep directories, it is recommended to turn off the scan to avoid getting the project directory scan for too long and affecting performance", "i18n_b7cfa07d78": "Confirm binding", "i18n_b7df1586a9": "When the target workspace already exists, the docker repository configuration information will be automatically synchronized", "i18n_b7ea5e506c": "System information", "i18n_b7ec1d09c4": "Service ID", "i18n_b7f770d80b": "You need to install the dependent npm i & & npm run build first.", "i18n_b8545de30e": "Please select at least one node", "i18n_b85b213579": "Sender name", "i18n_b86224e030": "Node state", "i18n_b87c9acca3": "Is it really necessary to force out of the cluster?", "i18n_b8915a4933": "Is it really possible to turn off two-factor authentication for the current user?", "i18n_b8ac664d98": "Check data table", "i18n_b90a30dd20": "If you don't fill in here, the password will not be changed.", "i18n_b91961bf0b": "The passed parameters are: projectId, projectName, type, result", "i18n_b922323119": "Mirror tags, such as: key1 = value1 & key2 = value2", "i18n_b939d47e23": "public key", "i18n_b953d1a8f1": "Can't close", "i18n_b96b07e2bb": "Trim only unused and unmarked images", "i18n_b9a4098131": "Trigger address", "i18n_b9af769752": "Mirror name required", "i18n_b9b176e37a": "Please select a script", "i18n_b9bcb4d623": "Plugin:", "i18n_b9c1616fd5": "Docker connected by SSH method is not recommended for container building (SSH method is very unstable for building)", "i18n_b9c4cf7483": " Replace all", "i18n_b9c52d9a85": "File name:", "i18n_ba17b17ba2": "There are no SSH script commands", "i18n_ba1f68b5dd": "This makes", "i18n_ba20f0444c": "forced deletion", "i18n_ba311d8a6a": "script", "i18n_ba3a679655": "Configure in yaml/yml format", "i18n_ba452d57f2": "The most used partitions:", "i18n_ba52103711": "Residual inode number", "i18n_ba619a0942": "Default build errors will automatically ignore hidden files", "i18n_ba6e91fa9e": "permission", "i18n_ba6ea3d480": "The page is full screen and the height is 100%. Partial areas can be scrolled.", "i18n_ba8d1dca4a": "Attention:", "i18n_baafe06808": "security group rules", "i18n_bab17dc6b1": "Select process name", "i18n_baef58c283": "Please enter label, alphanumeric, length 1-10", "i18n_baefd3db91": "Authorize the directory that can be accessed directly, and multiple carriage returns can be used to wrap lines.", "i18n_bb316d9acd": "The download speed is determined according to the network speed. If the network is not good, the download will be slower.", "i18n_bb4409015b": "machine ssh name", "i18n_bb4740c7a7": "Execute, order", "i18n_bb5aac6004": "Bundle sync to file center retention days", "i18n_bb667fdb2a": "No alarm", "i18n_bb7eeae618": "Statistics only:", "i18n_bb8d265c7e": "Version must be greater than 18.", "i18n_bb9a581f48": "Login is successful, MFA verification is required.", "i18n_bb9ef827bf": "ban access", "i18n_bba360b084": "Do you really want to delete the corresponding trigger?", "i18n_bbbaeb32fc": "machine delay", "i18n_bbcaac136c": "Is there incorrect data in the table?", "i18n_bbd63a893c": "Automatically detect whether there is a docker in the server where the server level is located. If there is, it will be automatically added to the list", "i18n_bbf2775521": "mirror name", "i18n_bc2c23b5d2": "The trimming operation will delete the relevant data, please operate with caution. Please confirm the consequences of this operation before using it.", "i18n_bc2f1beb44": "Do you really want to unlock the user?", "i18n_bc4b0fd88a": "Network Reachable Testing", "i18n_bc8752e529": "distribution project", "i18n_bcaf69a038": "Please select a node", "i18n_bcc4f9e5ca": "such as", "i18n_bcf48bf7a8": "authorization URL", "i18n_bcf83722c4": "Variable description", "i18n_bd0362bed3": "new group", "i18n_bd49bc196c": "Edit project", "i18n_bd4e9d0ee2": "Original Name:", "i18n_bd5d9b3e93": "Which docker to use to build, fill in the docker tag (the tag is configured on the docker editing page) The default query is the first one available, if the tag queries out multiple, it will be built in sequence", "i18n_bd6c436195": "Please enter a script description", "i18n_bd7043cae3": "remote download", "i18n_bd7c7abc8c": "Grouping is a virtual logical grouping and does not independently manage grouping data (if the relevant data is not saved after being added here, the corresponding grouping will not be saved)", "i18n_bd7c8c96bc": "manual upload", "i18n_bda44edeb5": "Can't operate", "i18n_bdc1fdde6c": "Beta plan:", "i18n_bdd4cddd22": "Will restore [", "i18n_bdd87b63a6": "WeChat QR code", "i18n_bdd9d38d7e": "column width", "i18n_be166de983": "Soft chain project", "i18n_be1956b246": "Dark 2 blackboard", "i18n_be2109e5b1": "Are you sure you want to reset the user password?", "i18n_be24e5ffbe": "Java project (java -jar xxx)", "i18n_be28f10eb6": "Please select the published primary catalog and fill in the secondary catalog", "i18n_be381ac957": "Please select the warehouse to use", "i18n_be3a4d368e": "Distribution in progress, 2: distribution ended, 3: cancelled, 4: distribution failed", "i18n_be4b9241ec": "The default status code of 200 indicates successful execution.", "i18n_be5b6463cf": "syntax reference", "i18n_be5fbbe34c": "save", "i18n_beafc90157": "The script library distributed to the machine node is referenced using G {'@'}(\" xxxx \") format in the node script support, and the system will automatically replace the script content in the referenced script library when there is a reference", "i18n_bebcd7388f": "Loading build data", "i18n_bec98b4d6a": "Status:", "i18n_becc848a54": "Absolute path of private key file (new file before absolute path)", "i18n_bef1065085": "Chrome extension", "i18n_bf0e1e0c16": "Enter the warehouse name or warehouse path to {slot1}", "i18n_bf77165638": "Are you sure you want to restart the current container?", "i18n_bf7da0bf02": "new password", "i18n_bf91239ad7": "command description", "i18n_bf93517805": "The following configuration information is only valid for the current browser", "i18n_bf94b97d1a": "Modification time:", "i18n_bfacfcd978": "Automatically load environment variables", "i18n_bfc04cfda7": "branch", "i18n_bfda12336c": "Search view", "i18n_bfe68d5844": "link", "i18n_bfe8fab5cd": "You need to configure the authorization directory (authorization can be used to publish normally). The authorization directory is mainly used to determine which directories can be published to", "i18n_bfed4943c5": "parameter value", "i18n_c00fb0217d": "Please fill in the user's nickname", "i18n_c03465ca03": "banned quantity", "i18n_c0996d0a94": " : Executed every Monday and Tuesday at 11:59", "i18n_c0a9e33e29": "Please select Build the corresponding branch, required", "i18n_c0ad27a701": "Actual available environment variables", "i18n_c0d19bbfb3": "Please enter the value of the key", "i18n_c0d38f475f": "soft memory", "i18n_c0d5d68f5f": "ignore", "i18n_c0e498a259": "Click the icon to view all associated tasks", "i18n_c0f4a31865": "logical deletion", "i18n_c11eb9deff": "File MD5", "i18n_c12ba6ff43": "We have the right to pursue all illegal gains of individuals who destroy open source and profit from it, and we are welcome to provide us with clues of infringement.", "i18n_c163613a0d": "If the current cluster still exists, there may be data inconsistencies.", "i18n_c1690fcca5": "Import certificate", "i18n_c16ab7c424": "]? Note: Canceling/stopping the build may not necessarily shut down all associated processes normally", "i18n_c1786d9e11": "Node address", "i18n_c17aefeebf": "System name:", "i18n_c18455fbe3": "Authorization information error", "i18n_c195df6308": "abnormal", "i18n_c1af35d001": "bundle", "i18n_c1b72e7ded": "For variable name", "i18n_c23fbf156b": "ssh not selected", "i18n_c26e6aaabb": "There are legal risks in modifying or deleting copyright information without authorization", "i18n_c2add44a1d": "Some examples:", "i18n_c2b2f87aca": "Script lonely data", "i18n_c2ee58c247": "build command", "i18n_c2f11fde3a": "Initialize the system account", "i18n_c31ea1e3c4": "No operation logs", "i18n_c325ddecb1": "The length of the CPU cycle, expressed in microseconds.", "i18n_c32e7adb20": "Please enter remote download safe HOST, carriage return support input multiple paths, sample https://www.test.com, etc", "i18n_c34175dbef": "Console log backup path: ", "i18n_c3490e81bf": "The previous container will be automatically deleted before restarting the creation.", "i18n_c34f1dc2b9": "The id, token, and trigger builds in the parameters are consistent", "i18n_c3512a3d09": "Please select Select Type", "i18n_c35c1a1330": "sorted value", "i18n_c360e994db": "sort", "i18n_c36ab9a223": "Create a new network stack for containers on the docker bridge", "i18n_c37ac7f024": "Clear Code", "i18n_c3aeddb10d": "There are no logical nodes in the current workspace, so the node distribution cannot be created.", "i18n_c3f28b34bb": "cluster name", "i18n_c43743d213": "Delete after push", "i18n_c446efd80d": "<PERSON>", "i18n_c4535759ee": "System prompt", "i18n_c46938460b": "The system uses the docker http interface to communicate and manage with docker, but the default is", "i18n_c469afafe0": "Are you sure you want to delete the current container?", "i18n_c494fbec77": "add manually", "i18n_c4a61acace": "Order?", "i18n_c4b5d36ff0": "Node status will automatically identify whether there is a java environment in the server, if there is no Java environment can not quickly install the node", "i18n_c4cfe11e54": "If the uploaded compressed file is automatically decompressed, the supported compressed package types are tar.", "i18n_c4e0c6b6fe": "Filter items", "i18n_c4e2cd2266": "You also need to operate on the relevant data to achieve the expected sorting.", "i18n_c5099cadcd": "number of plugins", "i18n_c53021f06d": "Fill in [xxx", "i18n_c530a094f9": "Build method:", "i18n_c538b1db4a": "Soft chain project (similar to a file whose copy of the project uses the relevant path)", "i18n_c583b707ba": "Description of parameters", "i18n_c5a2c23d89": "non-full screen", "i18n_c5aae76124": "Bind SSH", "i18n_c5bbaed670": "status code error", "i18n_c5c3583bfc": "Send verification code", "i18n_c5c69827c5": "Network port restrictions", "i18n_c5de93f9c7": "Manual confirmation is required.", "i18n_c5e7257212": "Current node ID:", "i18n_c5f9a96133": "The system will restrict a lot of permissions to the demo account by default. It is not recommended to use the demo account for non-demo scenarios.", "i18n_c600eda869": "The command file will be executed in {'${data directory}'}/script/xxxx.sh, bat", "i18n_c618659cea": "custom branch wildcard expression", "i18n_c6209653e4": "SMTP server domain name", "i18n_c68dc88c51": "Please enter a monitor name", "i18n_c6a3ebf3c4": "receive size", "i18n_c6c2497dbe": "Please enter the encoding format of the project log file, which defaults to following the global configuration", "i18n_c6e4cddba0": "Please select the function to monitor.", "i18n_c6f1c6e062": "Directory cannot be edited", "i18n_c6f6a9b234": "Move to another workspace", "i18n_c704d971d6": "Please fill in the build and product", "i18n_c7099dabf6": "Uploading file", "i18n_c71a67ab03": "After distribution", "i18n_c75b14a04e": "The monitoring frequency can be modified in the server level configuration file", "i18n_c75d0beca8": "Open the page operation guidance and navigation", "i18n_c7689f4c9a": "Here the build command will eventually be executed on the server. If there are multiple lines of commands then", "i18n_c76cfefe72": "Port", "i18n_c7c4e4632f": "The allowable failure rate during the update period", "i18n_c7e0803a17": "The password will only appear once, and the password cannot be viewed again after closing the window.", "i18n_c806d0fa38": "compressed package", "i18n_c83752739f": "Supported fields can be viewed through the interface return", "i18n_c840c88b7c": "Really want to empty [", "i18n_c84ddfe8a6": "execution log", "i18n_c8633b4b77": "Import repository via private token", "i18n_c87bd94cd7": "Mirror Id: ", "i18n_c889b9f67d": "Add related items", "i18n_c89e9681c7": "Temporary file footprint", "i18n_c8a2447aa9": "join", "i18n_c8b2aabc07": "If the file does not exist: MemAvailable, then MemAvailable = MemFree + Active (file) + Inactive (file) + SReclaimable, so in this system, the memory occupation calculation method: memory occupation = (total- (MemFree + Active (file) + Inactive (file) + SReclaimable))/total", "i18n_c8c452749e": "Select SQL file", "i18n_c8c45e8467": "Please configure according to your own project start time", "i18n_c8c6e37071": "Warm reminder", "i18n_c8ce4b36cb": "rename", "i18n_c90a1f37ce": "Node ID", "i18n_c96b442dfb": "Universal Mailbox SSL", "i18n_c96f47ec1b": "Asynchronous requests do not guarantee orderliness", "i18n_c972010694": "Product catalog", "i18n_c9744f45e7": "No", "i18n_c97e6e823a": "Restart the container unless it has been stopped", "i18n_c983743f56": "total memory", "i18n_c996a472f7": "Refresh every day at 0/12 o'clock", "i18n_c99a2f7ed8": "start command", "i18n_c9b0f8e8c8": "Really want to delete", "i18n_c9b79a2b4f": "After the global proxy configuration, it will take effect on the network of the server. The proxy implementation method: ProxySelector", "i18n_c9daf4ad6b": "multithreading", "i18n_ca32cdfd59": "Memory usage:", "i18n_ca527c48cf": "Should we store the information released this time as a template for publishing tasks, so that we can quickly use the same configuration information to publish tasks next time.", "i18n_ca69dad8fc": "After the process executes the script, the last line of the output must be: running", "i18n_ca774ec5b4": "The last build was based on commitId:", "i18n_caa9b5cd94": "Label values need to be configured into the build DSL", "i18n_cab7517cb4": "Node address:", "i18n_cabdf0cd45": "Select product", "i18n_cac26240b5": "container log", "i18n_cac6ff1d82": "Get build status addresses in bulk", "i18n_cad01fe13c": "Black and white ambiance-mobile", "i18n_caed797183": "ReserveCount represents the number of backup reserves generated by the trigger", "i18n_caf335a345": "Java information", "i18n_cb09b98416": "Personalized configuration area", "i18n_cb156269db": "In seconds, the minimum is 1 second", "i18n_cb25f04b46": "Search in the last 3 lines of the file", "i18n_cb28aee4f0": "Node distribution [Migration is not supported for the time being]", "i18n_cb46672712": "Log reading [Migration is not supported for the time being]", "i18n_cb93a1f4a5": ", root, manager", "i18n_cb951984f2": "Exists", "i18n_cb9b3ec760": "Batch trigger parameter BODY json ： [ { \" id \":\" 1 \",\" token \":\" a \",\" action \":\" status \"}]", "i18n_cbc44b5663": "End of execution time", "i18n_cbcc87b3d4": "Name, copyright, etc", "i18n_cbce8e96cf": "Certificate Information", "i18n_cbd9ffe1b8": "<PERSON><PERSON> execution", "i18n_cbdc4f58f6": "Please enter the name of the machine", "i18n_cbdcabad50": "release", "i18n_cbee7333e4": "Local command refers to the execution of multiple commands locally at the server level to achieve publication", "i18n_cc3a8457ea": "Node state is acquired asynchronously with a certain time lag", "i18n_cc42dd3170": "open", "i18n_cc51f34aa4": "editing service", "i18n_cc5dccd757": "Number of script templates in logical nodes in the workspace:", "i18n_cc617428f7": "Privacy variables refer to some important information such as password fields or key keys. Privacy fields can only be modified and cannot be viewed (the corresponding value cannot be seen in the edit pop-up window). Once the privacy field is created, it cannot be switched to a non-privacy field.", "i18n_cc637e17a0": "Product:", "i18n_cc92cf1e25": "Please fill in the product catalog", "i18n_cc9a708364": "status code error ", "i18n_cca4454cf8": "Please enter the content of the announcement", "i18n_ccb2fdd838": "Switch workspaces", "i18n_ccb91317c5": "command content", "i18n_ccea973fc7": "Current node address:", "i18n_cd1aedc667": "In Settings -- > Apps -- > Generate Token", "i18n_cd649f76d4": "time frame", "i18n_cd998f12fa": "When a node already exists in the target workspace, the node authorization information and agent configuration information will be automatically synchronized", "i18n_cda84be2f6": "operation log", "i18n_cdc478d90c": "system name", "i18n_ce043fac7d": "The current workspace does not have SSH yet.", "i18n_ce07501354": "Click on a number to see a running task", "i18n_ce1c5765e4": "View Release Template", "i18n_ce1ecd8a5b": "There are many more related dependencies on open source components", "i18n_ce23a42b47": "task name", "i18n_ce40cd6390": "associative script", "i18n_ce559ba296": "Run command", "i18n_ce7e6e0ea9": "The current configuration is only valid for the selected workspace, other workspaces need to be configured separately", "i18n_ce84c416f9": "Please enter user information url [userInfoUri]", "i18n_ced3d28cd1": "Do not scan", "i18n_ceee1db95a": "container port", "i18n_ceffe5d643": "two-step verification app", "i18n_cf38e8f9fd": "The authorization path configuration currently distributed for the node", "i18n_cfa72dd73a": "Please enter the cron expression to check", "i18n_cfb00269fd": "Execute script", "i18n_cfbb3341d5": "The currently logged in account is:", "i18n_cfd482e5ef": "No data yet, please add the node project data first", "i18n_cfeea27648": "Create file /xxx/xxx/xxx", "i18n_cfeff30d2c": "Table of Contents:", "i18n_d00b485b26": "Rollback", "i18n_d0132b0170": "It cannot be operated during the restoration process...", "i18n_d02a9a85df": "Please select an alarm contact", "i18n_d047d84986": "A/Total", "i18n_d0874922f0": "Binding the specified directory can be managed online, and building the ssh publication directory also needs to be configured here", "i18n_d0a864909b": "How to generate public and private keys", "i18n_d0b2958432": "version number", "i18n_d0b7462bdc": "This editor can only edit the name information of the current SSH in this workspace", "i18n_d0be2fcd05": ": indicates an interval, e.g. on minutes, every two minutes, likewise * can be replaced by a list of numbers, separated by commas", "i18n_d0c06a0df1": "Please enter the verification code", "i18n_d0c879f900": "Before uploading", "i18n_d0eddb45e2": "private key", "i18n_d0f53484dc": "Script ID:", "i18n_d1498d9dbf": "build notes", "i18n_d159466d0a": "Linked Data Name", "i18n_d175a854a6": "build directory", "i18n_d17eac5b5e": "I want to join", "i18n_d18d658415": "script ID", "i18n_d19bae9fe0": "After the plug-in is successfully installed and started, the node information will be actively reported. If the reported IP + PROP can communicate normally, the node information will be added.", "i18n_d1aa9c2da9": "Subnet mask:", "i18n_d1b8eaaa9e": "You need to enter the verification code to confirm the binding before it takes effect.", "i18n_d1f0fac71d": "Script cannot be saved without node selected", "i18n_d1f56b0a7e": "The incoming parameters are: monitors Id, monitors Name, nodeId, nodeName, projectId, projectName, title, content, runStatus", "i18n_d242bc3990": "#You can also customize the basic image to achieve complex business", "i18n_d263a9207f": "Support html format", "i18n_d27cf91998": "Reference address:", "i18n_d2913cea31": "Are you sure you want to change the download authorization code? Please confirm whether the authorization code is correct by yourself. The download authorization code can only be used when the download is triggered. Incorrect authorization codes will not be able to download the update package properly", "i18n_d2cac1245d": "Whether to [", "i18n_d2e2560089": "file name", "i18n_d2f484ff7e": "If the last line of output is not the expected format item status will be Not running", "i18n_d2f4a1550a": "No node scripts", "i18n_d301fdfc20": "After turning on automatic user creation, the first login only automatically creates an account, and the administrator needs to manually assign the permission group.", "i18n_d30b8b0e43": "unbuilt", "i18n_d31d625029": "Joining the beta program can provide timely access to the latest features, some optimization features, and the fastest bug-fixing version, but the beta version may also be unstable in some new features.", "i18n_d324f8b5c9": " replace", "i18n_d35a9990f4": "There are no more node projects, please create the project first", "i18n_d373338541": "Support IP address, domain name, hostname", "i18n_d3ded43cee": "View the build log", "i18n_d3e480c8c0": "number of failures", "i18n_d3fb6a7c83": ", will automatically jump to the login page after 2 seconds", "i18n_d40b511510": "certificate", "i18n_d438e83c16": "project grouping", "i18n_d4744ce461": "The permission group has not been configured, so users cannot be created", "i18n_d47ea92b3a": "Edit certificate", "i18n_d4aea8d7e6": "execution times", "i18n_d4e03f60a9": "Check the project status when the plug-in side starts, and try to execute the startup project if the project status is not running.", "i18n_d5269713c7": "Indicates that when bundled as a folder, it will be packaged as", "i18n_d57796d6ac": " : Range: 0~ 59", "i18n_d584e1493b": "Search ssh name", "i18n_d58a55bcee": "close", "i18n_d5a73b0c7f": "upload", "i18n_d5c2351c0e": "Please select a week that can be executed", "i18n_d5c68a926e": "execution order", "i18n_d5d46dd79b": "minute level", "i18n_d615ea8e30": "Select upgrade file", "i18n_d61af4e686": "Breakpoint/sharding alias download", "i18n_d61b8fde35": "Switch cluster", "i18n_d64cf79bd4": "Are you sure you want to join the beta program?", "i18n_d65551b090": "Fragmented file upload", "i18n_d65d977f1d": "Fill in the operating parameters", "i18n_d679aea3aa": "Running", "i18n_d6937acda5": "The folder where the project is stored, the folder where the jar package is stored", "i18n_d6a5b67779": "system process", "i18n_d6cdafe552": "Session has been closed [project-console]", "i18n_d6eab4107a": "Java project (java -jar Springboot war) [not recommended]", "i18n_d72471c540": "browser ID", "i18n_d731dc9325": "Timestamp:", "i18n_d7471c0261": "Please select an execution node", "i18n_d75c02d050": "Stop project", "i18n_d7ac764d3a": "The distribution interval time (sequential restart, full sequential restart) method will only take effect", "i18n_d7ba18c360": "The distribution node refers to a script that automatically synchronizes the content of the script node after editing the script", "i18n_d7bebd0e5e": "Please go to the console to control the status operation.", "i18n_d7c077c6f6": "command log", "i18n_d7cc44bc02": "user profile", "i18n_d7d11654a7": "does not exist", "i18n_d7ec2d3fea": "name", "i18n_d7ee59f327": "Private key, if left blank, the default configuration in the $HOME/.ssh directory will be used. Supported configuration file directory: file:", "i18n_d7ef19d05b": "Template node not configured yet", "i18n_d81bb206a8": "none", "i18n_d82ab35b27": "The first N lines of the file", "i18n_d82b19148f": "Please select the machine node to synchronize the authorization", "i18n_d83aae15b5": "Online build files are mainly saved, warehouse files, build history products, etc. Active cleaning is not supported. If the file occupies too much, you can configure retention rules and whether to save warehouse, product files, etc. for a single build configuration", "i18n_d84323ba8d": "The warehouse may exist repeatedly after automatic migration, please solve it manually.", "i18n_d85279c536": "Do not refresh or close the browser window when uploading.", "i18n_d87940854f": "number of plans", "i18n_d87f215d9a": "Card", "i18n_d88651584f": "free space", "i18n_d8a36a8a25": "Edit Docker cluster", "i18n_d8bf90b42b": "Other users can configure permissions to lift restrictions", "i18n_d8c7e04c8e": "information", "i18n_d8db440b83": "You need to evaluate whether you can join the beta based on your business situation.", "i18n_d911cffcd5": "Download address", "i18n_d921c4a0b6": "Really want to delete \"", "i18n_d926e2f58e": "Cancel task", "i18n_d937a135b9": "Light eclipse", "i18n_d94167ab19": "network port", "i18n_d9435aa802": "parse mode", "i18n_d9531a5ac3": "The permission group cannot be created without configuring the user.", "i18n_d9569a5d3b": "Multiple IPs can be used", "i18n_d9657e2b5f": "Please enter the project folder", "i18n_d9ac9228e8": "create", "i18n_d9c28e376c": "Function Management", "i18n_da1abf0865": "Verification code 6 is a pure number", "i18n_da1cb76e87": "Please enter the script content", "i18n_da317c3682": "[Recommended] Use the fast installation method to import the machine and automatically add logical nodes", "i18n_da4495b1b4": "Address:", "i18n_da509a213f": "The workspace is used to isolate data. There can be different data, different permissions, different menus, etc. under the workspace to achieve permission control", "i18n_da671a4d16": "WeChat:", "i18n_da79c2ec32": "configuration example", "i18n_da89135649": "Enterprise Services", "i18n_da8cb77838": "Online upgrade", "i18n_da99dbfe1f": "distribution status", "i18n_dab864ab72": "quick binding", "i18n_dabdc368f5": "Please select an assignment type", "i18n_dacc2e0e62": "Hardware hard disk", "i18n_dadd4907c2": "The table of contents,", "i18n_daf783c8cd": "point", "i18n_db06c78d1e": "test", "i18n_db094db837": "Modify variable value address", "i18n_db2d99ed33": "The cluster address has not been configured, so the cluster cannot be switched.", "i18n_db4470d98d": "alarm status", "i18n_db4b998fbd": "Oauth2 usage tips", "i18n_db5cafdc67": "Do you really want to untie the node?", "i18n_db686f0328": "Public key, if not filled in, the default configuration in the $HOME/.ssh directory will be used. Supported configuration file directory: file:", "i18n_db709d591b": "out of sync", "i18n_db732ecb48": "delay", "i18n_db81a464ba": "When the build is executed, a container will be generated for execution, and the corresponding container will be automatically deleted after the build is completed.", "i18n_db9296212a": "timed build", "i18n_dba16b1b92": "build event", "i18n_dbad1e89f7": "two-step verification", "i18n_dbb166cf29": "service id", "i18n_dbb2df00cf": "release directory", "i18n_dbba7e107a": "publish project", "i18n_dbc0b66ca4": "result", "i18n_dc0d06f9c7": "Please fill in the published secondary catalog", "i18n_dc2961a26f": "Node name:", "i18n_dc2c61a605": "Build your own GitLab", "i18n_dc32f465da": "Container address tcp", "i18n_dc3356300f": "If you want to configure SSH, please go to [System Management] - > [Asset Management] - > [SSH Management] to configure.", "i18n_dc39b183ea": "Are you sure you want to ignore the binding two-step verification? It is strongly recommended that the super administrator turn on two-step verification to ensure account security.", "i18n_dcc846e420": "Batch build all parameters example", "i18n_dcd72e6014": "Get a single build state address", "i18n_dce5379cb9": "hide", "i18n_dcf14deb0e": "Proxy Address (127.0.0.1:8888)", "i18n_dd1d14efd6": "View script", "i18n_dd23fdf796": "You can switch nodes during editing, but pay attention to whether the data matches.", "i18n_dd4e55c39c": "Not started", "i18n_dd95bf2d45": "normal login", "i18n_dda8b4c10f": "Fragment upload", "i18n_ddc7d28b7b": "variable", "i18n_dddf944f5f": "Reset the page operation guide and navigation successfully.", "i18n_ddf0c97bce": "Please pay attention to backing up your data to prevent data loss!!", "i18n_ddf7d2a5ce": "command", "i18n_de17fc0b78": "Maximum usage of hard disk:", "i18n_de3394b14e": "No asset machine", "i18n_de4cf8bdfa": "Pay to join our technical exchange group to answer all your questions first", "i18n_de5dadc480": "Pull log", "i18n_de6bc95d3b": "Clear the current directory file", "i18n_de78b73dab": "single trigger address", "i18n_debdfce084": "Please enter a cluster name", "i18n_decef97c7c": "Server level IP authorization configuration", "i18n_deea5221aa": "tag", "i18n_df011658c3": "scope", "i18n_df1da2dc59": "The number of microseconds of CPU time that a container can obtain in one CPU cycle.", "i18n_df3833270b": "Address:", "i18n_df39e42127": "automate", "i18n_df59a2804d": "prohibit scanning", "i18n_df5f80946d": "#node mirror source https://registry.npmmirror.com/-/binary/node/", "i18n_df9497ea98": "exist", "i18n_df9d1fedc5": "Node distribution refers to the deployment of a project in multiple nodes using node distribution to complete project publishing operations in multiple nodes in one step", "i18n_dfb8d511c7": "user name", "i18n_dfcc9e3c45": "post-distribution operation", "i18n_e039ffccc8": "Restore this file to the project directory?", "i18n_e049546ff3": "Modified in [System Configuration Catalog]", "i18n_e06497b0fb": "View currently available containers", "i18n_e06caa0060": "File modification time", "i18n_e074f6b6af": "SMTP server port", "i18n_e07cbb381c": "There are no warehouses.", "i18n_e09d0d8c41": "It is not recommended to use common names such as", "i18n_e0a0e26031": "Create a container using the current image", "i18n_e0ae638e73": "Retaining products refers to whether to retain bundle-related files after the build is completed for rollback", "i18n_e0ba3b9145": "Node script", "i18n_e0ce74fcac": "auto scroll", "i18n_e0d6976b48": "Please select the cluster affinity group", "i18n_e0ea800e34": "Package formal environment npm i & & npm run build: prod", "i18n_e0ec07be7d": "Client side key", "i18n_e0f937d57f": "Temporary token", "i18n_e0fcbca309": "Workspace ID:", "i18n_e15f22df2d": "Do you really want to clear the build information?", "i18n_e166aa424d": "About the system", "i18n_e17a6882b6": "script tag", "i18n_e19cc5ed70": "synchronization node authorization", "i18n_e1c965efff": "Please select a status", "i18n_e1fefde80f": "The node account password is generated by the system by default: it can be viewed through the agent_authorize .json file in the plug-in data directory (if the account password is customized, there will be no such file)", "i18n_e222f4b9ad": "Before execution, you need to check whether the address in the command can be accessed in the corresponding server. If it cannot be accessed, the node cannot be automatically bound.", "i18n_e235b0d4af": "Submission ID:", "i18n_e257dd2607": "Please select SSH connection information", "i18n_e26dcacfb1": " check ", "i18n_e2adcc679a": "Unit ns second", "i18n_e2b0f27424": "The project ID will only be unique in the machine node, and the same project ID is allowed in different workspaces (the same workspace).", "i18n_e2be9bab6b": "Copy any of the following commands to the server that has not installed the plug-in side to execute. You need to release it before execution", "i18n_e2d8fba259": "Do not save", "i18n_e2f942759e": "session exception", "i18n_e30a93415b": "With a private token, you can manage the repository within your account without entering your account password. You can specify the permissions that the token has when you create it.", "i18n_e319a2a526": "Reset to regenerate the trigger address. After the reset is successful, the previous trigger address will be invalid. The build trigger is bound to the generate trigger to the operator. If the corresponding account is deleted, the trigger will be invalid.", "i18n_e31ca72849": "Upload compressed file", "i18n_e354969500": "Token Import", "i18n_e362bc0e8a": "Path: {source} (host) = > {destination} (container)", "i18n_e39de3376e": "distribution", "i18n_e39f4a69f4": "number of scripts", "i18n_e39ffe99e9": "Please enter your password", "i18n_e3cf0abd35": "Email verification code", "i18n_e3e85de50c": "Please choose a build method", "i18n_e3ead2bd0d": "Common build command examples", "i18n_e3ee3ca673": "Do not append script templates", "i18n_e4013f8b81": "machine name", "i18n_e414392917": "Cluster information:", "i18n_e42b99d599": "month", "i18n_e43359ca06": "Please select an SSH node", "i18n_e44f59f2d9": "pre-issue command", "i18n_e475e0c655": "certificate sharing", "i18n_e48a715738": "New file", "i18n_e4b51d5cd0": "running state", "i18n_e4bea943de": "Warehouse address", "i18n_e4bf491a0d": "Downloading, please wait...", "i18n_e4d0ebcd58": "Select cluster", "i18n_e5098786d3": "Args parameter", "i18n_e54029e15b": "exit the cluster", "i18n_e54c5ecb54": "edit build", "i18n_e5915f5dbb": "(There are compatibility issues, you need to test in advance in actual use) python3 sdk image use: https://repo.huaweicloud.com/python/{'${PYTHON3_VERSION}'}/Python- {'${PYTHON3_VERSION}'} .tar.xz", "i18n_e5a63852fd": "Node password, please check the information output by node startup", "i18n_e5ae5b36db": "Key words highlighting, support regularization (regularization may affect performance, please use as appropriate)", "i18n_e5f71fc31e": "Search", "i18n_e5fae81ed4": "No SSH information was found during the search", "i18n_e60389f6d6": "Current front-end packaging time:", "i18n_e60725e762": "Wednesday", "i18n_e63fb95deb": "number of queues", "i18n_e64d788d11": "Upgrade successful", "i18n_e6551a2295": "Referencing the workspace environment variables can be easily modified using the same password in many places later", "i18n_e6bf31e8e6": "Long term token", "i18n_e6cde5a4bc": "The latest version was not checked.", "i18n_e6e453d730": "Please enter a variable name", "i18n_e6e5f26c69": "QQ mailbox SSL", "i18n_e703c7367c": "Current status:", "i18n_e710da3487": "time", "i18n_e72a0ba45a": "user group", "i18n_e72f2b8806": "Enter the warehouse name or warehouse path to search.", "i18n_e747635151": "Script name", "i18n_e76e6a13dd": "Do not reference environment variables", "i18n_e78e4b2dc4": "level", "i18n_e7d83a24ba": "number of successes", "i18n_e7e8d4c1fb": "Breakpoint/sharding download", "i18n_e7ffc33d05": "Execute after upload", "i18n_e8073b3843": "Please select a user permission group", "i18n_e825ec7800": "protocol type", "i18n_e8321f5a61": "Publication method:", "i18n_e83a256e4f": "confirm", "i18n_e84b981eb4": "Configuration value (e.g. 5g)", "i18n_e8505e27f4": "Please read the instructions and precautions in the update log before upgrading and", "i18n_e8e3bfbbfe": "Confirm close", "i18n_e8f07c2186": "If not filled in, the txt in the compressed package will be parsed.", "i18n_e9290eaaae": "Close left", "i18n_e930e7890f": "The expression is similar to Linux crontab expression in that the expression is divided into five parts using spaces, in order:", "i18n_e95f9f6b6e": "SSL connection", "i18n_e96705ead1": "If the button is not available, it means that the current node has been closed and needs to be enabled in editing.", "i18n_e976b537f1": "cache monitoring", "i18n_e97a16a6d7": " Execute every two minutes", "i18n_e9bd4484a7": "Sender email account", "i18n_e9c2cb1326": "secondary ID", "i18n_e9e9373c6f": "Performing a task", "i18n_e9ea1e7c02": "File storage days, default 3650 days", "i18n_e9ec2b0bee": "And wait for the previous project to start and complete before closing the next project", "i18n_e9f2c62e54": "After adding default parameters, you need to fill in the parameter values when manually executing the script", "i18n_ea15ae2b7f": "option", "i18n_ea3c5c0d25": "Temporary file occupies space:", "i18n_ea58a20cda": "MACHINE DOCKER", "i18n_ea7fbabfa1": "Please enter your account name", "i18n_ea89a319ec": "#Host directory and container directory mount /host:/container: ro", "i18n_ea8a79546f": "Please enter the published file id.", "i18n_ea9f824647": "Pull warehouse timeout in seconds", "i18n_eaa5d7cb9b": "expiration date", "i18n_eadd05ba6a": "medium", "i18n_eaf987eea0": "Weight (relative weight).", "i18n_eb164b696d": "exclude publishing", "i18n_eb5bab1c31": "optional", "i18n_eb79cea638": "Friday", "i18n_eb7f9ceb71": "Script library:", "i18n_eb969648aa": "Please back up the data in advance before operating.", "i18n_ebc2a1956b": "edit monitoring", "i18n_ebc96f0a5d": "Total memory (memory + swap). Set to -1 to disable swap.", "i18n_ec1f13ff6d": "Total:", "i18n_ec219f99ee": "End of execution", "i18n_ec22193ed1": "Please select a group", "i18n_ec537c957a": "{Slot1} machine directory", "i18n_ec6e39a177": "Are you sure you want to download the latest version of the update?", "i18n_ec7ef29bdf": "Please enter static, carriage return supports entering multiple paths, the system will automatically filter../path, and the root path is not allowed to be entered", "i18n_ec989813ed": "Status information:", "i18n_eca37cb072": "creation time", "i18n_ecdf9093d0": "Expand multiple simultaneously", "i18n_ecff77a8d4": "use", "i18n_ed145eba38": "Hard disk occupancy", "i18n_ed19a6eb6f": "Online build file occupies space", "i18n_ed367abd1a": "Modify user profile", "i18n_ed39deafd8": "Edit repository", "i18n_ed40308fe9": "#maven mirror source https://mirrors.tuna.tsinghua.edu.cn/apache/maven/maven-3/", "i18n_ed6a8ee039": " : Represents multiple timing expressions", "i18n_ed8ea20fe6": "Installation ID", "i18n_edb4275dcd": "Gmail", "i18n_edb881412a": "Note: In order to avoid unnecessary events to execute the script, the remarks of the selected script contain the keyword of the event parameter that needs to be implemented. If the success event needs to be executed, then the remarks of the selected script need to include the success keyword", "i18n_edc1185b8e": "Attempt to auto-renew failed", "i18n_edd716f524": "Please select a first-level directory for publication", "i18n_ede2c450d1": "No login logs", "i18n_ee19907fad": "#Basic image, currently supports ubuntu-latest、ubuntu-git、ubuntu-1ms-latest、ubuntu-1ms-git", "i18n_ee4fac2f3c": "In order to avoid monitoring blockage caused by some nodes failing to respond in time, the node statistical timeout time is not affected by the node timeout configuration, and the default timeout time (10 seconds) will be used.", "i18n_ee6ce96abb": "s second", "i18n_ee8ecb9ee0": "priority", "i18n_ee9a51488f": "Please enter a publishing directory", "i18n_eeb6908870": "previous step", "i18n_eec342f34e": "The default account is: jpomAgent.", "i18n_eee6510292": "Configure the authorization directory", "i18n_eee83a9211": "resource", "i18n_eeef8ced69": "Unbinding checks for data correlations and automatically deletes node items and script cache information", "i18n_eef3653e9a": "JVM {slot1}, {slot2}. For example: -Xms512m -Xmx512m", "i18n_eef4dfe786": "Java project (java -Djava.ext.dirs = lib -cp conf: run.jar $MAIN_CLASS)", "i18n_ef016ab402": "Confirm the creation of this", "i18n_ef28d3bff2": "The page content automatically expands and a screen scroll bar appears", "i18n_ef651d15b0": "After creation, it cannot be modified. The distribution ID is equivalent to the project ID.", "i18n_ef734bf850": "More details", "i18n_ef7e3377a0": "allowed time slot", "i18n_ef800ed466": "The main class where the program runs (jar mode can be left blank)", "i18n_ef8525efce": "Assign to another workspace", "i18n_ef9c90d393": "There is no script library.", "i18n_efae7764ac": "Account login", "i18n_efafd0cbd4": "Password (6-18 digits, letters, symbols)", "i18n_efb88b3927": "system uptime", "i18n_efd32e870d": "Plugin build time", "i18n_efe71e9bec": "Do you really want to unbind the current node distribution?", "i18n_efe9d26148": "Do you really want to delete the certificate? Deleting will delete the certificate file together?", "i18n_f013ea9dcb": "Loading in progress", "i18n_f038f48ce5": "Edit script", "i18n_f04a289502": "Svn ssh required Login user", "i18n_f05e3ec44d": "Forbidden access, current IP restricts access", "i18n_f06f95f8e6": "Lonely Data", "i18n_f087eb347c": "Build command example", "i18n_f08afd1f82": "Selected", "i18n_f0a1428f65": "Account support for referencing workspace variables:", "i18n_f0aba63ae7": "issuer", "i18n_f0db5d58cb": "Enable two-step verification to make the account more secure", "i18n_f0eb685a84": "File to be compatible with your machine", "i18n_f105c1d31d": "executor of last resort", "i18n_f113c10ade": "empty", "i18n_f11569cfa9": "The displayed environment variables may trigger the modification of values or the addition of new variables during actual execution. Please refer to the final execution variable for accuracy", "i18n_f139c5cf32": "Enter a new name", "i18n_f175274df0": "Login name, account number, cannot be modified after creation", "i18n_f1a2a46f52": "#Which docker to use to build, fill in the docker tag, the default query is the first one available, if the tag queries more than one, also select the first result", "i18n_f1b2828c75": "Install the plug-in", "i18n_f1d8533c7f": "Please enter the certificate information or select the certificate information. Fill in the certificate information rules: Serial number: Certificate type", "i18n_f1e3ef0def": "Automatically delete local images after pushing them to a remote warehouse. If publishing to cluster service is enabled, it is not recommended to enable this option unless the cluster service uses remote mirroring", "i18n_f1fdaffdf0": "background build", "i18n_f240f9d69c": "Branch name:", "i18n_f26225bde6": "detail", "i18n_f26ef91424": "download", "i18n_f27822dd8a": "Submit message:", "i18n_f282058f75": "Static file projects (frontend, logs, etc.)", "i18n_f2d05944ad": "Create a Docker cluster", "i18n_f30f1859ba": "If you modify it based on Jpom secondary development", "i18n_f332f2c8df": "gateway", "i18n_f3365fbf4d": "Docker not fetched or monitoring disabled", "i18n_f33db5e0b2": "Click to refresh the build information", "i18n_f37f8407ec": "File ID:", "i18n_f3947e6581": "Open-source is not the same as free", "i18n_f3e93355ee": "Restart project", "i18n_f425f59044": "System version:", "i18n_f4273e1bb4": "File ID", "i18n_f49dfdace4": "permission group", "i18n_f4b7c18635": "The password length is 6-20.", "i18n_f4baf7c6c0": "not started", "i18n_f4bbbaf882": "Branch/Tab", "i18n_f4dd45fca9": "Please enter a remote address", "i18n_f4edba3c9d": "Unknown table type", "i18n_f4fb0cbecf": "No results yet.", "i18n_f5399c620e": "Do you really want to delete this node from the cluster?", "i18n_f562f75c64": "service address", "i18n_f56c1d014e": "Successful execution", "i18n_f5c3795be5": "official", "i18n_f5d0b69533": "The complete private key content, such as", "i18n_f5d14ee3f8": "Disk occupancy", "i18n_f5f65044ea": "The server level installed by the container cannot use local builds (because local builds depend on the local environment of the startup server level, container installation is not easy to manage local dependent plugins)", "i18n_f63345630c": "#cache node_modules files in a container to a docker volume", "i18n_f63870fdb0": "Please fill in the container name", "i18n_f652d8cca7": "Try auto-renewal...", "i18n_f66335b5bf": "Error message:", "i18n_f66847edb4": "Web App ID", "i18n_f668c8c881": "Cluster name:", "i18n_f685377a22": "script library ", "i18n_f68f9b1d1b": "last heartbeat time", "i18n_f6d6ab219d": "The time when the update did succeed after it was completed", "i18n_f6d96c1c8c": "For compatibility with Quartz expressions, both 6-bit and 7-bit expressions are supported, including:", "i18n_f6dee0f3ff": "distribution ID", "i18n_f712d3d040": "Remarks Example:", "i18n_f71316d0dd": "substitution reference", "i18n_f71a30c1b9": "Data directory footprint", "i18n_f7596f3159": "If you need to switch the build command in advance in other workspaces", "i18n_f76540a92e": "In preparation", "i18n_f782779e8b": "end time", "i18n_f7b9764f0f": "The corresponding address will be requested for project start, stop, and restart.", "i18n_f7e8d887d6": "Workspace environment variables", "i18n_f7f340d946": "Do you really want to clear SSH hidden field information? (password, private key)", "i18n_f8460626f0": "Node account, please check the information output by the node startup.", "i18n_f86324a429": "Use ANT expression to filter the specified directory to publish and exclude the specified directory", "i18n_f89cc4807e": "The authorization path refers to the folder where the project files are stored in the service", "i18n_f89fa9b6c6": "Select warehouse", "i18n_f8a613d247": "Please select a node", "i18n_f8b3165e0d": "The current project is disabled", "i18n_f8f20c1d1e": "Trim objects created before this timestamp, for example: 24h", "i18n_f8f456eb9a": "Type Project-specific types: reload, restart", "i18n_f92d505ff5": "Attention: By default, the system monitors whether SSH is functioning properly and triggers login operations multiple times. If the password is incorrect or security protection rules have restrictions that cannot be lifted, specific groups can be configured to disable monitoring (refer to the configuration file for detailed configuration)", "i18n_f932eff53e": "strip data", "i18n_f9361945f3": "hostname", "i18n_f967131d9d": "Warehouse name", "i18n_f976e8fcf4": "Monitor name", "i18n_f97a4d2591": "Please select which cluster you want to join", "i18n_f9898595a0": "Note: It is not recommended that the same group be bound to multiple clusters", "i18n_f98994f7ec": "publishing method", "i18n_f99ead0a76": "The image name is incorrect and cannot be updated", "i18n_f9ac4b2aa6": "operator", "i18n_f9c9f95929": "Java project (java -classpath)", "i18n_f9cea44f02": "There is currently no Docker in the workspace.", "i18n_f9f061773e": "If you don't fill it in, use the node to distribute the configured secondary directory", "i18n_fa2f7a8927": "failure strategy", "i18n_fa4aa1b93b": "Run project", "i18n_fa57a7afad": "Container tags, such as: xxxx: latest multiple separated by commas, configure additional environment variables files to support loading .env files in the warehouse directory environment variables such as: xxxx: {'${VERSION}'}", "i18n_fa624c8420": "After disabling, the user cannot log in to the platform.", "i18n_fa7f6fccfd": "Project name:", "i18n_fa7ffa2d21": "Unlock", "i18n_fa8e673c50": "Edit Workspace", "i18n_faa1ad5e5c": "agreement", "i18n_faaa995a8b": "Can be closed", "i18n_faaadc447b": "serial number", "i18n_fabc07a4f1": "Please select a monitoring operation", "i18n_fad1b9fb87": "The new script template needs to be added to the node management.", "i18n_fb1f3b5125": "Current Workspace Linked Data Statistics", "i18n_fb3a2241bb": "Status description:", "i18n_fb5bc565f3": "Failed to parse file:", "i18n_fb61d4d708": "Do you really want to roll back the build history?", "i18n_fb7b9876a6": "Please enter a script name", "i18n_fb852fc6cc": "in progress", "i18n_fb8fb9cc46": "statistical description", "i18n_fb91527ce5": "Node availability:", "i18n_fb9d826b2f": "The command to execute after publishing (non-blocking command), usually the command to start the project, such as: ps -aux {'|'} grep java", "i18n_fba5f4f19a": "DSL environment variables", "i18n_fbd7ba1d9b": "Last distribution time", "i18n_fbee13a873": "Total number of workspaces:", "i18n_fbfa6c18bf": "Allocated", "i18n_fbfeb76b33": "Left menu bar theme switch", "i18n_fc06c70960": "Are you sure you want to delete the current image?", "i18n_fc4e2c6151": "logged in user", "i18n_fc5fb962da": "Email password or authorization code", "i18n_fc92e93523": "Effective time", "i18n_fc954d25ec": "proxy", "i18n_fcaef5b17a": "Reuse another container network stack", "i18n_fcb4c2610a": "Notification exception", "i18n_fcb7a47b70": "Alibaba Cloud Email", "i18n_fcba60e773": "build", "i18n_fcbf0d0a55": "You need to install the dependent yarn & & yarn run build first.", "i18n_fcca8452fe": "The cluster address is mainly used to switch the workspace and automatically jump to the corresponding cluster.", "i18n_fcef976c7a": "private key content", "i18n_fd6e80f1e0": "normal", "i18n_fd7b461411": "Do not empty", "i18n_fd7e0c997d": "Select file", "i18n_fd93f7f3d7": "Scripts can be distributed to machine nodes and referenced in DSL projects to the point where multiple projects share the same script", "i18n_fda92d22d9": "The associated node will automatically identify whether there is a java environment in the server, if there is no Java environment can not quickly install the node", "i18n_fdba50ca2d": "If the port is exposed to the public network", "i18n_fdbac93380": "SMTP address: smtp.mxhichina.com, port 465 and open SSL, the username needs to be the same as the email sender, and the password is the login password of the email.", "i18n_fdbc77bd19": "safety", "i18n_fdcadf68a5": "SMTP port", "i18n_fde1b6fb37": "You need to configure the authorization directory for the machine in advance.", "i18n_fdfd501269": "Java SDK image use: https://mirrors.tuna.tsinghua.edu.cn/supported versions are: 8, 9, 10, 11, 12, 13, 14, 15, 16, 17", "i18n_fe1b192913": "After the directory is successfully created, you need to manually refresh the tree on the right to display it.", "i18n_fe231ff92f": "Close page operation guidance and navigation", "i18n_fe2df04a16": "version", "i18n_fe32def462": "active", "i18n_fe7509e0ed": "value", "i18n_fe828cefd9": "The project folder is the name of the directory where the project is actually stored", "i18n_fe87269484": "cluster modification time", "i18n_fea996d31e": "Please fill in the build name", "i18n_fec6151b49": "Account name", "i18n_feda0df7ef": "Account email", "i18n_ff17b9f9cd": "WeCom", "i18n_ff1fda9e47": "prohibit", "i18n_ff39c45fbc": "Use the host network stack within the container. Note: Host mode gives the container full access to local system services such as D-bus and is therefore considered insecure.", "i18n_ff3bdecc5e": "File view (if the account password is customized, this file will not be available)", "i18n_ff80d2671c": "Refresh in seconds", "i18n_ff9814bf6b": "trigger type", "i18n_ff9dffec4d": "search mode", "i18n_ffa9fd37b5": "Workspace management", "i18n_ffaf95f0ef": "Startup container, you can see many devices on the host, you can perform mount. You can start the docker container in the docker container.", "i18n_ffd67549cf": ": Range: 1~ 12, also supports case-insensitive aliases: \"jan\",\"feb\",\"mar\",\"apr\",\"may\",\"jun\",\"jul\",\"aug\",\"sep\",\"oct\",\"nov\",\"dec\"", "i18n_fffd3ce745": "share"}