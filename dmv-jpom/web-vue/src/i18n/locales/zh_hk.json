{"i18n_0006600738": "加入 Docker 集羣", "i18n_005de9a4eb": "構建歷史是用於記錄每次構建的信息,可以保留構建產物信息,構建日誌。同時還可以快速回滾發佈", "i18n_0079d91f95": "確定要將此數據置頂嗎？", "i18n_007f23e18f": "關閉 TLS 認證", "i18n_00a070c696": "點擊可以複製", "i18n_00b04e1bf0": "發送包", "i18n_00d5bdf1c3": "調用次數", "i18n_00de0ae1da": "文件上傳前需要執行的腳本(非阻塞命令)", "i18n_01081f7817": "請輸入允許編輯文件的後綴及文件編碼，不設置編碼則默認取系統編碼，多個使用換行。示例：設置編碼：txt{'@'}utf-8， 不設置編碼：txt", "i18n_010865ca50": "真的要停止項目麼？", "i18n_0113fc41fc": "全屏日誌", "i18n_01198a1673": "上傳小文件", "i18n_01226f48fc": "對於每一個子表達式，同樣支持以下形式：", "i18n_0128cdaaa3": "分配類型", "i18n_01ad26f4a9": "重置觸發器 token 信息,重置後之前的觸發器 token 將失效", "i18n_01b4e06f39": "重啟", "i18n_01e94436d1": "原密碼", "i18n_020d17aac6": "發送大小", "i18n_020f1ecd62": "開始上傳", "i18n_020f31f535": "路徑需要配置絕對路徑,不支持軟鏈", "i18n_0215b91d97": "構建序號id需要跟進實際情況替換", "i18n_0221d43e46": "遠程下載Url不為空", "i18n_0227161b3e": "執行方式", "i18n_022b6ea624": "您確定要刪除當前卷嗎？", "i18n_0253279fb8": "克隆深度", "i18n_02d46f7e6f": "真的要刪除這些構建歷史記錄麼？", "i18n_02d9819dda": "提示", "i18n_02db59c146": "禁止訪問的 IP 地址", "i18n_02e35447d4": "下載構建產物,如果按鈕不可用表示產物文件不存在,一般是構建沒有產生對應的文件或者構建歷史相關文件被刪除", "i18n_0306ea1908": "刪除鏡像", "i18n_031020489f": "當前工作空間您觸發的構建記錄", "i18n_03580275cb": "請選中要重啟的項目", "i18n_0360fffb40": "並開啟此開關", "i18n_036c0dc2aa": "系統取消分發", "i18n_0373ba5502": "需要您在需要被管理的服務器中安裝 agent ，並將 agent 信息新增到系統中", "i18n_03816381ec": "切換視圖", "i18n_0390e2f548": "參數{count}描述", "i18n_03a74a9a8a": "日誌路徑", "i18n_03c1f7c142": "請填選擇構建的倉庫", "i18n_03d9de2834": "項目運維", "i18n_03dcdf92f5": "隱私變量", "i18n_03e59bb33c": "緊湊", "i18n_03f38597a6": "速度", "i18n_0428b36ab1": "副本", "i18n_04412d2a22": "操作不能撤回奧", "i18n_044b38221e": "Java 項目(示例參考，具體還需要根據項目實際情況來決定)", "i18n_045cd62da3": "型號：", "i18n_045f89697e": "壓縮包進行發佈", "i18n_047109def4": "待處理", "i18n_04a8742dd7": "插件運行時間", "i18n_04edc35414": "模板節點", "i18n_051fa113dd": "方式連接 docker 是通過終端實現，每次操作 docker 相關 api 需要登錄一次終端", "i18n_05510a85b0": "系統中您所有操作日誌", "i18n_059ac641c0": "特權：", "i18n_059b86dbe1": "確定要刪除該發佈任務模板嗎？", "i18n_05b52ae2db": "{slot1} 用於容器構建選擇容器功能（fromTag）", "i18n_05cfc9af9d": "接收錯誤", "i18n_05e6d88e29": "分發節點是指在編輯完腳本後自動將腳本內容同步節點的腳本,一般用户節點分發功能中的 DSL 模式", "i18n_05e78c26b1": "單個觸發器地址中：第一個隨機字符串為命令腳本ID，第二個隨機字符串為 token", "i18n_05f6e923af": "執行錯誤", "i18n_0647b5fc26": "先停止", "i18n_066431a665": "請輸入證書描述", "i18n_066f903d75": "操後上移或者下移可能不會達到預期排序", "i18n_067638bede": "CPU數", "i18n_067eb0fa04": "如果這裏的報警聯繫人無法選擇，説明這裏面的管理員沒有設置郵箱，在右上角下拉菜單裏面的用户資料裏可以設置。", "i18n_0693e17fc1": "有新內容後是否自動滾動到底部", "i18n_06986031a7": "需要到原始工作空間中去控制節點分發", "i18n_06e2f88f42": "請輸入名稱", "i18n_0703877167": "關閉MFA", "i18n_0719aa2bb0": "重置密碼", "i18n_0728fee230": "請輸入公吿標題", "i18n_072fa90836": "壓縮 ", "i18n_0739b9551d": "端口協議", "i18n_07683555af": "當前版本號：", "i18n_0793aa7ba3": "maven sdk 鏡像使用：https://mirrors.tuna.tsinghua.edu.cn/apache/maven/maven-3/", "i18n_07a03567aa": "虛擬內存佔用", "i18n_07a0e44145": "主機名：", "i18n_07a828310b": "並行度", "i18n_07a8af8c03": "為當前項目實際的進程ID", "i18n_07b6bb5e40": "嚴格執行腳本（構建命令、事件腳本、本地發佈腳本、容器構建命令）執行返回狀態碼必須是 0、否則將構建狀態標記為失敗", "i18n_07d2261f82": "默認是當前時間到今年結束", "i18n_080b914139": "上傳包", "i18n_0836332bf6": "升級協議", "i18n_083b8a2ec9": "一個物理節點被多個服務端綁定也會產生孤獨數據奧", "i18n_08902526f1": "皮膚：", "i18n_0895c740a6": "交換內存佔用", "i18n_089a88ecee": "系統時間：", "i18n_08ab230290": "操作説明", "i18n_08ac1eace7": "文件上傳成功後需要執行的腳本(非阻塞命令)", "i18n_08b1fa1304": "請輸入用户名", "i18n_08b55fea3c": "管理", "i18n_0934f7777a": "新標籤終端", "i18n_095e938e2a": "停止", "i18n_09723d428d": "報警聯繫人", "i18n_09d14694e7": "需要 SSH 監控中能獲取到 docker 信息", "i18n_09e7d24952": "實際內存佔用率：", "i18n_0a056b0d5a": "動態文件", "i18n_0a1d18283e": "構建確認彈窗", "i18n_0a47f12ef2": "如果孤獨數據被工作空間下的其他功能關聯，修正後關聯的數據將失效對應功能無法查詢到關聯數據", "i18n_0a54bd6883": "Gmail 郵箱配置", "i18n_0a60ac8f02": "是", "i18n_0a63bf5b41": "軟內存限制。", "i18n_0a9634edf2": "地址通配符,* 表示所有地址都將使用代理", "i18n_0aa60d1169": "您還未登錄過", "i18n_0aa639865c": "真的要刪除機器 SSH 麼？", "i18n_0ac4999a4c": "網卡信息", "i18n_0ac9e3e675": "綁定成功後將不再顯示,強烈建議保存此二維碼或者下面的 MFA key", "i18n_0af04cdc22": "支持兩種方式填充：", "i18n_0af5d9f8e8": "當前區域為系統管理、資產管理中心", "i18n_0b23d2f584": "差異構建", "i18n_0b2fab7493": "當前 SSH 的授權目錄（文件目錄、文件後綴、禁止命令）需要請到 【系統管理】-> 【資產管理】-> 【SSH 管理】-> 操作欄中->關聯按鈕->對應工作空間->操作欄中->配置按鈕", "i18n_0b3edfaf28": "設置內存限制。", "i18n_0b58866c3e": "斷點/分片單文件下載", "i18n_0b6811e5b1": "使用語言", "i18n_0b76afbf5d": "允許執行的 CPU（例如，0-3、0", "i18n_0b9d5ba772": "請尊重開源協議，不要擅自修改版本信息，否則可能承擔法律責任。", "i18n_0baa0e3fc4": "發佈中", "i18n_0bac3db71c": "重啟服務端後失效", "i18n_0bbc7458b4": "回到首頁", "i18n_0bc45241af": "傳入參數有：outGivingId、outGivingName、status、statusMsg、executeTime", "i18n_0bf9f55e9d": "不能關閉", "i18n_0bfcab4978": "node sdk 鏡像使用：https://registry.npmmirror.com/-/binary/node", "i18n_0c0633c367": "不能刪除默認工作空間", "i18n_0c1de8295a": "獨立", "i18n_0c1e9a72b7": "將使用微隊列來排隊構建，避免幾乎同時觸發構建被中斷構建（一般用户倉庫合併代碼會觸發多次請求）,隊列保存在內存中,重啟將丟失", "i18n_0c1f1cd79b": "不自動重啟", "i18n_0c1fec657f": "秒", "i18n_0c2487d394": "下拉搜索默認搜索關鍵詞相關的前 10 個，以及已經選擇的機器節點", "i18n_0c256f73b8": "容器名稱：", "i18n_0c4eef1b88": "當為6位時，第一位表示", "i18n_0c5c8d2d11": "基礎信息：", "i18n_0c7369bbee": "開啟SSH訪問", "i18n_0cbf83cc07": "聯繫我們", "i18n_0ccaa1c8b2": " ：表示匹配這個位置所有的時間", "i18n_0ce54ecc25": "付費社羣", "i18n_0cf4f0ba82": "真的要保存當前配置嗎？如果配置有誤,可能無法啟動服務需要手動還原奧！！！ 保存成功後請及時關注重啟狀態！！", "i18n_0cf81d77bb": "請填寫倉庫地址", "i18n_0d44f4903a": "真的要釋放(刪除)當前項目麼？", "i18n_0d467f7889": "# 是否開啟日誌備份功能", "i18n_0d48f8e881": "請輸入服務地址", "i18n_0d50838436": "數據目錄", "i18n_0d98c74797": "其他", "i18n_0da9b12963": "用户數據", "i18n_0de68f5626": "登錄JPOM", "i18n_0e052223a4": "重啟服務端需要重新獲取", "i18n_0e16902c1e": "查看狀態", "i18n_0e1ecdae4a": "完整順序執行(有執行失敗將結束本次)", "i18n_0e25ab3b51": "證書的允許的 IP 需要和 docker host 一致", "i18n_0e44ae17ae": "服務端機器網絡", "i18n_0e502fed63": "重啟超時,請去服務器查看控制枱日誌排查問題", "i18n_0e55a594fd": "監控項目", "i18n_0e5f01b9be": "關聯工作空間ssh", "i18n_0ea78e4279": "查看日誌", "i18n_0ec9eaf9c3": "更多", "i18n_0eccc9451d": "# 備份文件保留個數", "i18n_0ee3ca5e88": "掃碼讚賞支持開源項目長期發展", "i18n_0ef396cbcc": "分發結果", "i18n_0f004c4cf7": "第三方登錄", "i18n_0f0a5f6107": "正常連接", "i18n_0f189dbaa4": "沒有任何用户", "i18n_0f4f503547": "請輸入版本", "i18n_0f539ff117": "真的要批量刪除選擇的鏡像嗎？已經被容器使用的鏡像無法刪除！", "i18n_0f59fe5338": "防火牆端口", "i18n_0f5fc9f300": "文件管理中心", "i18n_0f8403d07e": "刷新倒計時", "i18n_0fca8940a8": "沒有節點", "i18n_0ff425e276": "文件ID", "i18n_1012e09849": "處理失敗", "i18n_10145884ba": "文件後N行", "i18n_1014b33d22": "分組名稱", "i18n_101a86bc84": "請輸入...", "i18n_1022c545d1": "插件端啟動時自動檢查項目如未啟動將嘗試啟動", "i18n_102dbe1e39": "注意：環境變量存在作用域：當前工作空間或者全局，不能跨工作空間引用", "i18n_102e8ec6d5": "網絡流量信息", "i18n_104000e24a": "模板來源", "i18n_1058a0be42": "開啟 TLS 認證,證書信息：", "i18n_1062619d5a": "節點賬號密碼默認由系統生成：可以通過插件端數據目錄下 agent", "i18n_108d492247": "正則語法參考", "i18n_10c385b47e": "一鍵分發同步多個節點的系統配置", "i18n_10d6dfd112": "顯示後N行", "i18n_10f6fc171a": "SSH 名稱", "i18n_111e786daa": "填寫備註僅本次構建生效", "i18n_1125c4a50b": "真的要刪除分發信息麼？刪除後節點下面的項目也都將刪除", "i18n_113576ce91": "產物目錄：", "i18n_1149274cbd": "用户總數", "i18n_115cd58b5d": "】備份文件夾麼？", "i18n_1160ab56fd": "構建命令：", "i18n_116d22f2ab": "項目ID：", "i18n_11724cd00b": "集羣創建時間", "i18n_117a9cbc8d": "語言：", "i18n_11957d12e4": "報警中", "i18n_11e88c95ee": " 查找上一個", "i18n_121e76bb63": "請選擇構建對應的分支", "i18n_1235b052ff": "節點地址 (*************:2123)", "i18n_1278df0cfc": "關聯節點如果服務器存在 java 環境,但是插件端未運行則會顯示快速安裝按鈕", "i18n_127de26370": "SMTP 地址：【smtp.qq.com】，用户名一般是QQ號碼，密碼是郵箱授權碼，端口默認 587/465", "i18n_12934d1828": "日誌目錄是指控制枱日誌存儲目錄", "i18n_12afa77947": "開啟緩存構建目錄將保留倉庫文件,二次構建將 pull 代碼, 不開啟緩存目錄每次構建都將重新拉取倉庫代碼(較大的項目不建議關閉緩存)", "i18n_12d2c0aead": "請將此密碼複製吿知該用户", "i18n_12dc402a82": "參考數據", "i18n_130318a2a1": "路由無效，無法跳轉", "i18n_1303e638b5": "修改時間", "i18n_13627c5c46": "配置ssh", "i18n_138776a1dc": "默認是在插件端數據目錄/{'${projectId}'}/{'${projectId}'}.log", "i18n_138a676635": "注意", "i18n_13c76c38b7": "# scriptId 可以引用腳本庫中的腳本（G{'@'}xxx）其中 xxx 為腳本庫中的腳本標記，前提需要提取將對應腳本同步至對應機器節點", "i18n_13d10a9b78": "沒有資產SSH", "i18n_13d947ea19": "需要您先新增資產機器再分配機器節點（邏輯節點）到當前工作空間", "i18n_13f7bb78ef": "默認統計機器中除本地接口（環回或無硬件地址）網卡流量總和", "i18n_13f931c5d9": "查看任務", "i18n_1432c7fcdb": "系統公吿", "i18n_143bfbc3a1": "點擊重新同步當前工作空間邏輯節點項目信息", "i18n_143d8d3de5": "否則將刪除滿足條件的所有數據", "i18n_148484b985": "實現您需要配置 docker 容器到服務端中來管理，並且分配到當前工作空間中", "i18n_1498557b2d": "同時只能展開一個菜單", "i18n_14a25beebb": "10秒一次", "i18n_14d342362f": "標籤", "i18n_14dcfcc4fa": "還未執行reload", "i18n_14dd5937e4": "附加環境變量  .env 新增多個使用逗號分隔", "i18n_14e6d83ff5": "時間：", "i18n_14ee5b5dc5": "命令文件將在 {'${插件端數據目錄}'}/script/xxxx.sh 、bat 執行", "i18n_14feaa5b3a": "刷新倒計時 ", "i18n_1535fcfa4c": "發送", "i18n_156af3b3d1": "菜單配置", "i18n_1593dc4920": "真的要刪除該記錄麼？刪除後構建關聯的容器標籤將無法使用", "i18n_159a3a8037": "更新鏡像", "i18n_15c0ba2767": "上傳項目文件", "i18n_15c46f7681": "修改接口 HTTP 狀態碼為 200 並且響應內容為：success 才能確定操作成功反之均可能失敗", "i18n_15d5fffa6a": "響應結果", "i18n_15e9238b79": "接收", "i18n_15f01c43e8": "日誌備份列表", "i18n_15fa91e3ab": "天級別", "i18n_1603b069c2": "週一", "i18n_1622dc9b6b": "未知", "i18n_162e219f6d": "丟失", "i18n_164cf07e1c": "清空覆蓋", "i18n_16646e46b1": "產物文件大小：", "i18n_16a3a4ed35": "模板標記", "i18n_16b5e7b472": "直接構建", "i18n_16f7fa08db": "嗎？", "i18n_17006d4d51": "是否自動跳轉到系統頁面", "i18n_170fc8e27c": "週四", "i18n_174062da44": "分發方式", "i18n_1775ff0f26": "建議新增指定時間範圍", "i18n_178ad7e9bc": "參數中的 id 、token 和觸發構建一致、buildNumId 構建序號id", "i18n_17a101c23e": "孤獨數據是指機器節點裏面存在數據，但是無法和當前系統綁定上關係（關係綁定=節點ID+工作空間ID對應才行），一般情況下不會出現這樣的數據", "i18n_17a74824de": "構建方式", "i18n_17acd250da": "下移", "i18n_17b4c9c631": "沒有任何節點", "i18n_17b5e684e5": "需要到 節點管理中的【插件端配置】的授權配置中配置允許編輯的文件後綴", "i18n_17c06f6a8b": "最後執行時間", "i18n_17d444b642": "運行方式", "i18n_1810e84971": "才能使用 SSH 方式連接", "i18n_1818e9c264": "JVM總內存", "i18n_1819d0cdda": "如果開啟同步到文件管理中心，在構建發佈流程將自動執行同步到文件管理中心的操作。", "i18n_181e1ad17d": "長按可以拖動排序", "i18n_1857e7024c": "系統版本", "i18n_185926bf98": "全屏", "i18n_1862c48f72": "本地狀態：", "i18n_1880b85dc5": "黑白 ambiance", "i18n_18b0ab4dd2": "機器SSH名", "i18n_18b34cf50d": "不滾動", "i18n_18c63459a2": "默認", "i18n_18c7e2556e": "如果當前構建信息已經在其他頁面更新過，需要點擊刷新按鈕來獲取最新的信息，點擊刷新後未保存的數據也將丟失", "i18n_18d49918f5": "賬號被鎖定", "i18n_18eb76c8a0": "memory 最小 4M", "i18n_192496786d": "事件腳本", "i18n_19675b9d36": "清除代碼(倉庫目錄)為刪除服務器中存儲倉庫目錄裏面的所有東西,刪除後下次構建將重新拉起倉庫裏面的文件,一般用於解決服務器中文件和遠程倉庫中文件有衝突時候使用。執行時間取決於源碼目錄大小和文件數量如超時請耐心等待，或稍後重試", "i18n_1974fe5349": "綁定成功", "i18n_197be96301": "待完善", "i18n_19f974ef6a": "開啟差異發佈並且開啟清空發佈時將自動刪除項目目錄下面有的文件但是構建產物目錄下面沒有的文件【清空發佈差異上傳前會先執行刪除差異文件再執行上傳差異文件】", "i18n_19fa0be4d2": " 官方文檔", "i18n_19fcb9eb25": "時間", "i18n_1a2c905e87": "選擇單位", "i18n_1a44b9e2f7": "同步到其他工作空間", "i18n_1a55f76ace": "構建命令，構建產物相對路徑為：", "i18n_1a56bb2237": "至少選擇一個節點和項目", "i18n_1a6aa24e76": "執行", "i18n_1a704f73c2": "請選擇一個文件", "i18n_1a8f90122f": "提示信息 ", "i18n_1abf39bdb6": "# 將此目錄緩存到全局（多個構建可以共享此緩存目錄）", "i18n_1ad696efdc": "構建執行的命令(非阻塞命令)，如：mvn clean package、npm run build。支持變量：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_SOURCE_FILE}'}、{'${BUILD_NUMBER_ID}'}、倉庫目錄下 .env、工作空間變量", "i18n_1ae2955867": "指定 pom 文件打包 mvn -f xxx/pom.xml clean package", "i18n_1afdb4a364": "隱藏滾動條。縱向滾動方式提醒：滾輪，橫行滾動方式：Shift+滾輪", "i18n_1b03b0c1ff": "已經分配到工作空間的 Docker 或者集羣無法直接刪除，需要到分配到的各個工作空間逐一刪除後才能刪除資產 Docker 或者集羣", "i18n_1b38c0bc86": "備份文件存儲目錄：", "i18n_1b5266365f": "原始IP", "i18n_1b5bcdf115": "會話已經關閉[node-system-log]", "i18n_1b7cba289a": "數據統計", "i18n_1b8fff7308": "開啟 MFA", "i18n_1b963fd303": "【推薦】騰訊身份驗證碼", "i18n_1b973fc4d1": "分組名稱：", "i18n_1ba141c9ac": "請選擇軟鏈的項目", "i18n_1ba584c974": "配置容器", "i18n_1baae8183c": "是否解壓", "i18n_1c040e6b87": "一般情況下不建議降級操作", "i18n_1c10461124": "示例：key,key1 或者 key=value,key1=value1", "i18n_1c13276448": "當前工作空間關聯構建", "i18n_1c2e9d0c76": "沒有任何構建", "i18n_1c3cf7f5f0": "關聯", "i18n_1c61dfb86f": "掛載點", "i18n_1c8190b0eb": "請填寫項目 DSL 配置內容,可以點擊上方切換 tab 查看配置示例", "i18n_1c83d79715": "執行失敗", "i18n_1c9d3cb687": "用户名ID", "i18n_1cc82866a4": "分片操作數", "i18n_1d0269cb77": "已經分配到工作空間的 SSH 無法直接刪除，需要到分配到的各個工作空間逐一刪除後才能刪除資產 SSH", "i18n_1d263b7efb": "該選項僅本次構建生效", "i18n_1d38b2b2bc": "請選擇項目授權路徑", "i18n_1d53247d61": "請選擇邏輯節點", "i18n_1d650a60a5": "硬盤", "i18n_1d843d7b45": "此節點暫無項目", "i18n_1dc518bddb": "項目存儲的文件夾", "i18n_1dc9514548": "不等同於 PING 測試，此處測試成功表示網絡一定通暢，此處測試失敗網絡不一定不通暢", "i18n_1de9b781bd": "使用容器構建，docker 容器所在的宿主機需要有公網,因為需要遠程下載環境依賴的 sdk 和鏡像", "i18n_1e07b9f9ce": "請選擇要同步系統配置的機器節點", "i18n_1e4a59829d": "插件端開機自啟", "i18n_1e5533c401": "配置目錄", "i18n_1e5ca46c26": "排除發佈 ANT 表達式,多個使用逗號分隔", "i18n_1e88a0cfaf": "不發佈到 docker 集羣", "i18n_1e93bdad2a": "搜索項目名", "i18n_1eb378860a": "真的要 Kill 這個進程麼？", "i18n_1eba2d93fc": "禁用原因", "i18n_1ece1616bf": "如果插件端正常運行但是連接失敗請檢查端口是否開放,防火牆規則,雲服務器的安全組入站規則", "i18n_1ed46c4a59": "分發名稱（項目名稱）", "i18n_1f08329bc4": "搜索命令名稱", "i18n_1f0c93d776": " ：每分鐘執行", "i18n_1f0d13a9ad": "服務端分發同步的腳本不能直接刪除,需要到服務端去操作", "i18n_1f1030554f": "總計 {total} 條", "i18n_1f130d11d1": "SMTP 服務器", "i18n_1f4c1042ed": "文件夾", "i18n_1fa23f4daa": "過期時間", "i18n_1fd02a90c3": "用户", "i18n_200707a186": "創建後構建方式不支持修改", "i18n_2025ad11ee": "真的要解綁節點腳本麼？", "i18n_2027743b8d": "系統名稱:", "i18n_204222d167": "網絡延遲", "i18n_2064fc6808": "不顯示", "i18n_207243d77a": "如果要將工作空間分配給其他用户還需要到權限組管理", "i18n_207d9580c1": "表示週六", "i18n_209f2b8e91": "請輸入登錄密碼", "i18n_20a9290498": "您來到系統管理中心", "i18n_20c8dc0346": "演示賬號", "i18n_20e0b90021": "真的要刪除監控麼？", "i18n_20f32e1979": "角色：", "i18n_211354a780": "內的root只是外部的一個普通用户權限。默認false", "i18n_21157cbff8": "毫秒", "i18n_211a60b1d6": "編輯容器的一些基礎參數", "i18n_2141ffaec9": "狀態數據是異步獲取有一定時間延遲", "i18n_2168394b82": "文件id,精準搜索", "i18n_2171d1b07d": "默認參數", "i18n_2191afee6e": "升級超時,請去服務器查看控制枱日誌排查問題", "i18n_21d81c6726": "為當前工作空間中的容器配置標籤", "i18n_21da885538": "可以使用節點腳本：", "i18n_21dd8f23b4": "開源協議", "i18n_21e4f10399": "優先判斷禁用時段", "i18n_21efd88b67": "暫無數據", "i18n_220650a1f5": "配置後將保存到當前構建", "i18n_2213206d43": "點擊延遲可以查看對應節點網絡延遲歷史數據", "i18n_222316382d": "關聯節點", "i18n_2223ff647d": "清空發佈", "i18n_2245cf01a3": "您沒有權限訪問", "i18n_2246d128cb": "企業微信通知地址", "i18n_22482533ff": "私鑰內容,不填將使用默認的 $HOME/.ssh 目錄中的配置。支持配置文件目錄:file:/xxxx/xx", "i18n_224aef211c": "構建信息", "i18n_224e2ccda8": "配置", "i18n_2256690a28": "節點ID：", "i18n_22670d3682": "請選擇要使用的腳本", "i18n_226a6f9cdd": "請檢查是否開啟 ws 代理", "i18n_226b091218": "類型", "i18n_2296651945": "# 目前支持的 uses 插件端 java、maven、node、go、python3、gradle 。如果不滿足需求，可自行配置插件", "i18n_22b03c024d": "二維碼", "i18n_22c799040a": "容器", "i18n_22cf31df5d": "當前訪問IP：", "i18n_22e4da4998": "表示項目當前未運行", "i18n_22e888c2df": "到期時間", "i18n_2300ad28b8": "讀寫", "i18n_2314f99795": "檢測到新版本 ", "i18n_231f655e35": "當前程序打包時間：", "i18n_23231543a4": "修正", "i18n_2331a990aa": "掃碼轉賬支持開源項目長期發展", "i18n_233fb56ab2": "在 設置-->安全設置-->私人令牌 中獲取", "i18n_234e967afe": "發佈前執行的命令(非阻塞命令),一般是關閉項目命令,支持變量替換：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_RESULT_FILE}'}、{'${BUILD_NUMBER_ID}'}", "i18n_2351006eae": "附加環境變量", "i18n_23559b6453": "# 將容器中的 maven 倉庫文件緩存到 docker 卷中", "i18n_2356fe4af2": "配合腳本模版實現自定義項目管理", "i18n_2358e1ef49": "所屬工作空間： ", "i18n_235f0b52a1": "發送錯誤", "i18n_23b38c8dad": "會話已經關閉[upgrade]", "i18n_23b444d24c": "快速配置", "i18n_23eb0e6024": "暱稱", "i18n_242d641eab": "後綴", "i18n_2432b57515": "備註", "i18n_24384ba6c1": "確定要重新同步當前節點項目緩存信息嗎？", "i18n_24384dab27": "請輸入 value 的值", "i18n_244d5a0ed8": "構建參數", "i18n_2456d2c0f8": "如果容器以非零退出代碼退出，則重新啟動容器。可以指定次數：on-failure:2", "i18n_2457513054": "週六", "i18n_2482a598a3": "插件版本號", "i18n_248c9aa7aa": "構建狀態", "i18n_2493ff1a29": "自定義進程類型", "i18n_2499b03cc5": "保留產物：", "i18n_249aba7632": "天", "i18n_24ad6f3354": "如果垮機器（資產機器）遷移之前機器中的項目數據僅是邏輯刪除（項目文件和日誌均會保留）", "i18n_24cc0de832": "執行命令", "i18n_24d695c8e2": "集羣主機名", "i18n_250688d7c9": "發佈失敗", "i18n_250a999bb2": "容器標籤,如：xxxx:latest 多個使用逗號隔開", "i18n_25182fb439": "工作空間菜單", "i18n_251a89efa9": "查看當前狀態", "i18n_252706a112": "【推薦】微信小程序搜索 數盾OTP", "i18n_2527efedcd": "用户信息 url", "i18n_2560e962cf": "請選擇分發項目", "i18n_257dc29ef7": "搜索配置參考", "i18n_25b6c22d8a": "為避免顯示內容太多而造成瀏覽器卡頓,讀取日誌最後多少行日誌。修改後需要回車才能重新讀取，小於 1 則讀取所有", "i18n_25be899f66": "篩選之後本次發佈操作只發布篩選項,並且只對本次操作生效", "i18n_25c6bd712c": "請輸入獲取的計劃運行次數", "i18n_25f29ebbe6": "腳本日誌數：", "i18n_25f6a95de3": "確定要取消構建 【名稱：", "i18n_2606b9d0d2": "分發機器", "i18n_260a3234f2": "請選擇SSH", "i18n_2611dd8703": "當目標工作空間不存在對應的節點時候將自動創建一個新的節點（邏輯節點）", "i18n_26183c99bf": "文件中心", "i18n_2646b813e8": "登錄密碼", "i18n_267bf4bf76": "分發到節點中需要注意跨工作空間重名將被最後一次同步覆蓋", "i18n_2684c4634d": "版本：", "i18n_26a3378645": "請選擇運行方式", "i18n_26b5bd4947": "加載中...", "i18n_26bb841878": "新建", "i18n_26bd746dc3": "真的要清空項目目錄和文件麼？", "i18n_26c1f8d83e": "最後操作人", "i18n_26ca20b161": "來源", "i18n_26eccfaad1": "鏡像：", "i18n_26f95520a5": "執行命令包含：", "i18n_26ffe89a7f": "項目名：", "i18n_27054fefec": "執行腳本傳入參數有：startReady、pull、executeCommand、release、done、stop、success", "i18n_2770db3a99": "加載項目數據中...", "i18n_2780a6a3cf": "TLS 認證", "i18n_27b36afd36": "狀態碼為 0 的操作大部分為沒有操作結果或者異步執行", "i18n_27ba6eb343": "網關：", "i18n_27ca568be2": "繼續", "i18n_27d0c8772c": "如果誤操作會產生宂餘數據！！！", "i18n_27f105b0c3": "請選擇要升級的節點", "i18n_280379cee4": "保存並關閉", "i18n_282c8cda1f": "如果上報的節點信息包含多個 IP 地址需要用户確認使用具體的 IP 地址信息", "i18n_288f0c404c": "清空", "i18n_28b69f9233": "構建鏡像的過程不使用緩存", "i18n_28b988ce6a": "文件類型", "i18n_28bf369f34": "發佈後的文件名是：文件ID.後綴，並非文件真實名稱 （可以使用上傳後腳本隨意修改）", "i18n_28c1c35cd9": "主節點不能直接剔除", "i18n_28e0fcdf93": "還沒有容器或者未配置標籤不可以使用容器構建奧", "i18n_28e1c746f7": "ssh 名", "i18n_28e1eec677": "授權路徑", "i18n_28f6e7a67b": "靜態文件", "i18n_29139c2a1a": "文件名", "i18n_2926598213": "項目日誌", "i18n_293cafbbd3": "裁剪", "i18n_2953a9bb97": "您需要創建一個賬户用以後續登錄管理系統,請牢記超級管理員賬號密碼", "i18n_295bb704f5": "語言", "i18n_29b48a76be": "請選擇發佈方式", "i18n_29efa328e5": "未分發", "i18n_2a049f4f5b": "分發失敗", "i18n_2a0bea27c4": "執行域", "i18n_2a0c4740f1": "文件", "i18n_2a1d1da97a": "打包測試環境包 mvn clean package -Dmaven.test.skip=true -Ptest", "i18n_2a24902516": "集羣ID：", "i18n_2a38b6c0ae": "未升級成功：", "i18n_2a3b06a91a": "虛擬MAC", "i18n_2a3e7f5c38": "手動", "i18n_2a6a516f9d": "填寫運行命令", "i18n_2a813bc3eb": "立即下載", "i18n_2ad3428664": "請選擇發佈到集羣的服務名", "i18n_2adbfb41e9": "參數如果傳入", "i18n_2ae22500c7": "禁用時段", "i18n_2b04210d33": "進程號：", "i18n_2b0623dab9": "獨立容器", "i18n_2b0aa77353": "您確定要啟動當前容器嗎？", "i18n_2b0f199da0": "不執行，也不編譯測試用例 mvn clean package -Dmaven.test.skip=true", "i18n_2b1015e902": "參數描述沒有實際作用", "i18n_2b21998b7b": "確定要關閉兩步驗證嗎？關閉後賬號安全性將受到影響,關閉後已經存在的 mfa key 將失效", "i18n_2b36926bc1": "沒有任何構建歷史", "i18n_2b4bb321d7": "內容區域主題切換", "i18n_2b4cf3d74e": "請選擇要使用的構建", "i18n_2b52fa609c": "發生異常", "i18n_2b607a562a": "逐行執行", "i18n_2b696d1fec": "沉默時間", "i18n_2b6bc0f293": "操作", "i18n_2b788a077e": "等常用用户名，避免被其他用户有意或者無意操作造成登錄失敗次數過多從而超級管理員賬號被異常鎖定", "i18n_2b94686a65": "# 給容器新增環境變量", "i18n_2ba4c81587": "請輸入郵箱地址", "i18n_2bb1967887": "請找我們授權，否則會有法律風險。", "i18n_2be2175cd7": "執行容器 標籤", "i18n_2be75b1044": "全局", "i18n_2bef5b58ab": "不填寫則不更新", "i18n_2c014aeeee": "打包時間", "i18n_2c5b0e86e6": "用户密碼重置成功", "i18n_2c635c80ec": "發佈操作是指,執行完構建命令後將構建產物目錄中的文件用不同的方式發佈(上傳)到對應的地方", "i18n_2c74d8485f": "下載完成後需要手動選擇更新到節點才能完成節點更新奧", "i18n_2c8109fa0b": "當前目錄: ", "i18n_2c921271d5": "vue 項目(示例參考，具體還需要根據項目實際情況來決定)", "i18n_2cdbbdabf1": "構建產物目錄,相對倉庫的路徑,如 java 項目的 target/xxx.jar vue 項目的 dist", "i18n_2cdcfcee15": "功能豐富 專為兩步驗證碼", "i18n_2ce44aba57": "日誌目錄", "i18n_2d05c9d012": "關鍵詞,支持正則", "i18n_2d2238d216": "賬號新增成功", "i18n_2d3fd578ce": "確定要取批量消選中的構建嗎？注意：取消/停止構建不一定能正常關閉所有關聯進程", "i18n_2d455ce5cd": "下載中", "i18n_2d58b0e650": "選擇構建的標籤,不選為最新提交", "i18n_2d7020be7d": "比如常見的 .env 文件", "i18n_2d711b09bd": "內容", "i18n_2d842318fb": "週期", "i18n_2d94b9cf0e": "Dockerfile 構建方式不支持在這裏回滾", "i18n_2d9569bf45": "參數值,新增默認參數後在手動執行腳本時需要填寫參數值", "i18n_2d9e932510": "新增目錄", "i18n_2de0d491d0": "小時", "i18n_2e0094d663": "真的要刪除該集羣信息麼？1", "i18n_2e1f215c5d": "自動創建用户", "i18n_2e505d23f7": "下載導入模板", "i18n_2e51ca19eb": "如果節點選項是禁用，則表示對應數據有推薦關聯節點（低版本項目數據可能出現此情況）", "i18n_2e740698cf": "集羣IP", "i18n_2ea7e70e87": "命令文件將上傳至 {'${user.home}'}/.jpom/xxxx.sh 執行完成將自動刪除", "i18n_2ef1c35be8": "執行的 CPU", "i18n_2f4aaddde3": "刪除", "i18n_2f5e828ecd": "別名碼", "i18n_2f5e885bc6": "獲取單個構建日誌地址", "i18n_2f67a19f9d": "需要選發佈到集羣中的對應的服務名，需要提前去集羣中創建服務", "i18n_2f6989595f": "管理列表：", "i18n_2f8d6f1584": "昨天", "i18n_2f8dc4fb66": "真的要釋放分發信息麼？釋放之後節點下面的項目信息還會保留，如需刪除項目還需要到節點管理中操作", "i18n_2f8fd34058": "腳本模版是存儲在服務端中的命令腳本用於在線管理一些腳本命令，如初始化軟件環境、管理應用程序等", "i18n_2f97ed65db": "佔用", "i18n_2fc0d53656": "機器狀態(緩存)", "i18n_2ff65378a4": "真的要刪除對應工作空間的 SSH 麼？", "i18n_2fff079bc7": "發佈成功", "i18n_3006a3da65": "系統版本:", "i18n_300fbf3891": "發佈前停止是指在發佈文件到項目文件時先將項目關閉，再進行文件替換。避免 windows 環境下出現文件被佔用的情況", "i18n_302ff00ddb": "超級管理員", "i18n_3032257aa3": "詳情信息", "i18n_30849b2e10": "進程/端口", "i18n_30aaa13963": "序列號 (SN)", "i18n_30acd20d6e": "用户ID", "i18n_30d9d4f5c9": "新增關聯", "i18n_30e6f71a18": "自定義標籤通配表達式", "i18n_30e855a053": "取消分發", "i18n_30ff009ab3": "# java 鏡像源 https://mirrors.tuna.tsinghua.edu.cn/Adoptium/", "i18n_3103effdfd": "請輸入賬號名", "i18n_31070fd376": "手動回滾", "i18n_310c809904": "綁定到當前工作空間", "i18n_312e044529": " ：範圍：0(Sunday)~6(Saturday)，7也可以表示週日，同時支持不區分大小寫的別名：\"sun\",\"mon\", \"tue\", \"wed\",\"thu\",\"fri\", \"sat\"，", "i18n_312f45014a": "創建時間：", "i18n_31353ecf96": " 下載授權碼： ", "i18n_314f5aca4e": "單個觸發器地址中：第一個隨機字符串為構建ID，第二個隨機字符串為 token", "i18n_315eacd193": "上移", "i18n_31691a647c": "{slot1}端口", "i18n_3174d1022d": "容器構建注意", "i18n_3181790b4b": "服務端系統配置", "i18n_318ce9ea8b": "用户密碼提示", "i18n_31aaaaa6ec": "構建ID：", "i18n_31ac8d3a5d": "線程同步器", "i18n_31bca0fc93": "加入 beta 計劃可以及時獲取到最新的功能、一些優化功能、最快修復 bug 的版本，但是 beta版也可能在部分新功能上存在不穩定的情況。您需要根據您業務情況來評估是否可以加入 beta，在使用 beta版過程中遇到問題可以隨時反饋給我們，我們會盡快為您解答。", "i18n_31eb055c9c": "並行度,同一時間升級的容器數量", "i18n_31ecc0e65b": "項目", "i18n_3200fba1c6": "下載源：", "i18n_32112950da": "批量取消", "i18n_3241c7c05f": "建議使用服務端腳本分發到腳本：", "i18n_32493aeef9": "構建中", "i18n_329e2e0b2e": "指定目錄打包 yarn && yarn --cwd xxx build", "i18n_32a19ce88b": "控制枱日誌路徑", "i18n_32ac152be1": "更新", "i18n_32c65d8d74": "標題", "i18n_32cb0ec70e": "請輸入節點名稱", "i18n_32d0576d85": "的令牌", "i18n_32dcc6f36e": "重啟策略：no、always、unless-stopped、on-failure", "i18n_32e05f01f4": "集羣信息", "i18n_32f882ae24": "匹配零個或多個字符", "i18n_330363dfc5": "成功", "i18n_3306c2a7c7": "讀取默認", "i18n_33130f5c46": "操作成功", "i18n_3322338140": "請選擇發佈後操作", "i18n_332ba869d9": "一般用於節點環境一致的情況", "i18n_334a1b5206": "安裝節點", "i18n_335258331a": "已經讀取默認配置文件到編輯器中", "i18n_33675a9bb3": "集羣關聯的 docker 信息丟失,不能繼續使用管理功能", "i18n_339097ba2e": "準備分發", "i18n_33c9e2388e": "項目ID", "i18n_3402926291": "當前日誌文件大小：", "i18n_340eb70415": "日誌編碼格式", "i18n_346008472d": "匹配包含 異常 的行", "i18n_3477228591": "鏡像", "i18n_35134b6f94": "查看節點腳本", "i18n_3517aa30c2": "腳本里面支持的變量有：{'${PROJECT_ID}'}、{'${PROJECT_NAME}'}、{'${PROJECT_PATH}'}", "i18n_353707f491": "可以到【節點分發】=>【分發授權配置】修改", "i18n_353c7f29da": "請選擇模版節點", "i18n_35488f5ba8": "請選擇節點項目", "i18n_354a3dcdbd": "30秒一次", "i18n_3574d38d3e": "剩餘內存：", "i18n_35b89dbc59": "確認要下載最新版本嗎？", "i18n_35cb4b85a9": "【目前只使用匹配到的第一項】", "i18n_35fbad84cb": "描述根據創建時間升序第一個", "i18n_3604566503": "請填寫容器地址", "i18n_364bea440e": "請選擇要引用的腳本", "i18n_368ffad051": "{slot1}目錄", "i18n_36b3f3a2f6": "報警標題", "i18n_36b5d427e4": "請輸入工作空間描述", "i18n_36d00eaa3f": "差異構建：", "i18n_36d4046bd6": "引用腳本模板", "i18n_36df970248": "# version 需要在對應鏡像源中存在", "i18n_3711cbf638": "預佔資源", "i18n_37189681ad": "數據Id", "i18n_373a1efdc0": "請選中要關閉的項目", "i18n_374cd1f7b7": "創建集羣", "i18n_375118fad1": "物理節點腳本模板數據：", "i18n_375f853ad6": "硬件信息", "i18n_3787283bf4": "真的要刪除當前文件麼？", "i18n_37b30fc862": "請選擇皮膚", "i18n_37c1eb9b23": "配置文件路徑", "i18n_37f031338a": "上傳壓縮包並自動解壓", "i18n_37f1931729": "數據目錄佔用空間：", "i18n_383952103d": "此分片上傳是採用簡單邏輯實現，當上傳第一個分片時候使用覆蓋模式，後續分片使用追加模式。如果上傳中斷對應的文件是一個非完整文件將無法正常使用。", "i18n_384f337da1": "同步機制採用", "i18n_3867e350eb": "環境變量", "i18n_386edb98a5": "自定義腳本項目（python、nodejs、go、接口探活、es）【推薦】", "i18n_38a12e7196": "選擇證書文件", "i18n_38aa9dc2a0": "更多配置", "i18n_38ce27d846": "下一步", "i18n_38cf16f220": "確定", "i18n_38da533413": "下面命令將在", "i18n_3904bfe0db": "設置一個超級管理員賬號", "i18n_3929e500e0": "通常情況為項目遷移工作空間、遷移物理機器等一些操作可能產生孤獨數據", "i18n_396b7d3f91": "文件大小", "i18n_398ce396cd": "工作空間同步", "i18n_39b68185f0": "節點地址為插件端的 IP:PORT 插件端端口默認為：2123", "i18n_39c7644388": "端口號", "i18n_39e4138e30": "集羣創建時間：", "i18n_39f1374d36": "耗時", "i18n_3a1052ccfc": "引用環境變量", "i18n_3a17b7352e": "分鐘", "i18n_3a3778f20c": "任務ID", "i18n_3a3c5e739b": "批量構建參數", "i18n_3a3ff2c936": "卷標籤", "i18n_3a536dcd7c": "126郵箱", "i18n_3a57a51660": "腳本版本:{item}", "i18n_3a6000f345": "正在運行的線程同步器", "i18n_3a6970ac26": "文件共享", "i18n_3a6bc88ce0": "真的要刪除文件麼？", "i18n_3a6c2962e1": "密鑰算法", "i18n_3a71e860a7": "未開啟當前終端", "i18n_3a94281b91": "自由腳本是指直接在機器節點中執行任意腳本", "i18n_3aa69a563b": "節點分發是指,一個項目運行需要在多個節點(服務器)中運行,使用節點分發來統一管理這個項目(可以實現分佈式項目管理功能)", "i18n_3ac34faf6d": "通配符", "i18n_3adb55fbb5": "遷移工作空間", "i18n_3ae4c953fe": "當定時任務運行到的時間匹配這些表達式後，任務被啟動。", "i18n_3ae4ddf245": "真的要刪除該 Docker 麼？刪除只會檢查本地系統的數據關聯,不會刪除 docker 容器中數據", "i18n_3aed2c11e9": "自動", "i18n_3b14c524f6": "讀取次數", "i18n_3b19b2a75c": "真的要刪除腳本麼？", "i18n_3b885fca15": "緩存版本號", "i18n_3b9418269c": "請填寫關聯容器標籤", "i18n_3b94c70734": "項目狀態", "i18n_3ba621d736": "處理成功", "i18n_3baa9f3d72": "批量構建參數還支持指定參數,delay（延遲執行構建,單位秒）branchName（分支名）、branchTagName（標籤）、script（構建腳本）、resultDirFile（構建產物）、webhook（通知webhook）", "i18n_3bc5e602b2": "郵箱", "i18n_3bcc1c7a20": "最後修改人", "i18n_3bdab2c607": "10分鐘", "i18n_3bdd08adab": "描述", "i18n_3bf3c0a8d6": "節點", "i18n_3bf9c5b8af": " 分組名：", "i18n_3c014532b1": "構建耗時：", "i18n_3c070ea334": "如果關聯的構建關聯的倉庫被多個構建綁定（使用）不能遷移", "i18n_3c48d9b970": "批量構建參數 BODY json： [ { \"id\":\"1\", \"token\":\"a\" } ]", "i18n_3c586b2cc0": "自定義 host", "i18n_3c6248b364": "緩存信息", "i18n_3c6fa6f667": "cron表達式", "i18n_3c8eada338": "請選擇編碼方式", "i18n_3c91490844": "發佈操作", "i18n_3c99ea4ec2": "例如 2,3,6/3中，由於“/”優先級高，因此相當於2,3,(6/3)，結果與 2,3,6等價", "i18n_3c9eeee356": "真的要刪除日誌文件麼？", "i18n_3cc09369ad": "真的要刪除【", "i18n_3d06693eb5": "資源：", "i18n_3d0a2df9ec": "參數", "i18n_3d3b918f49": "執行構建", "i18n_3d3d3ed34c": "請輸入選擇關聯分組", "i18n_3d43ff1199": "置頂", "i18n_3d48c9da09": "授權配置", "i18n_3d61e4aaf1": "指定標籤", "i18n_3d6acaa5ca": "這個容器沒有網絡", "i18n_3d83a07747": "主機 Host", "i18n_3dc5185d81": "私有", "i18n_3dd6c10ffd": "上傳升級包", "i18n_3e445d03aa": "文件不存在啦", "i18n_3e51d1bc9c": "請選擇發佈的SSH", "i18n_3e54c81ca2": "接收流量", "i18n_3e7ef69c98": "監控操作", "i18n_3e8c9c54ee": "選擇分組", "i18n_3ea6c5e8ec": "分發結束", "i18n_3eab0eb8a9": "本地腳本", "i18n_3ed3733078": "終端日誌", "i18n_3edddd85ac": "日", "i18n_3ee7756087": "請先選擇節點", "i18n_3f016aa454": "鏡像標籤：", "i18n_3f18d14961": "兩步驗證碼", "i18n_3f1d478da4": "服務端腳本、SSH腳本可以使用 G{'@'}(\"xxxx\") 格式來引用，當存在引用時系統會自動替換引用腳本庫中的腳本內容", "i18n_3f2d5bd6cc": "在文件第 2 - 2 行中搜索", "i18n_3f414ade96": "參數描述,{slot1},僅是用於提示參數的含義", "i18n_3f553922ae": "】目錄和文件麼？", "i18n_3f5af13b4b": "# scriptId 可以是項目路徑下腳本文件名或者系統中的腳本模版ID", "i18n_3f719b3e32": "衝突數", "i18n_3f78f88499": "打包時間：", "i18n_3f8b64991f": "解壓時候自動剔除壓縮包裏面多餘的文件夾名", "i18n_3f8cedd1d7": "用於靜態文件綁定和讀取(不建議配置大目錄，避免掃描消耗過多資源)", "i18n_3fb2e5ec7b": "登錄日誌", "i18n_3fb63afb4e": "退出碼", "i18n_3fbdde139c": "確認密碼", "i18n_3fca26a684": "批量觸發參數 BODY json： [ { \"id\":\"1\", \"token\":\"a\" } ]", "i18n_3fea7ca76c": "狀態", "i18n_401c396b51": "日誌編碼格式是指項目日誌文件的編碼格式", "i18n_402d19e50f": "登錄", "i18n_40349f5514": "數：", "i18n_4055a1ee9c": "通用的字段有：createTimeMillis、modifyTimeMillis", "i18n_406a2b3538": "何為孤獨數據", "i18n_4089cfb557": "關聯分組主要用於資產監控來實現不同服務端執行不同分組下面的資產監控", "i18n_40aff14380": "鏡像ID", "i18n_40da3fb58b": "新建狀態", "i18n_40f8c95345": "臨時文件目錄", "i18n_411672c954": "請輸入文件描述", "i18n_412504968d": "當目標工作空間不存在對應的 SSH 時候將自動創建一個新的 SSH", "i18n_41298f56a3": "構建失敗", "i18n_413d8ba722": "舊版程序包占有空間：", "i18n_413f20d47f": "系統 採用 oshi 庫來監控系統，在 oshi 中使用 /proc/meminfo 來獲取內存使用情況。", "i18n_41638b0a48": "用於區別文件是否為同一類型,可以針對同類型進行下載管理", "i18n_417fa2c2be": "參數{index}描述", "i18n_4188f4101c": "沒有docker", "i18n_41d0ecbabd": "Block IO 權重", "i18n_41e8e8b993": "深色", "i18n_41e9f0c9c6": "工作節點", "i18n_41fdb0c862": "請先上傳或者下載新版本", "i18n_4244830033": "請選擇證書文件", "i18n_424a2ad8f7": "準備", "i18n_429b8dfb98": "項目分發", "i18n_429d4dbc55": "# 本示例僅供參考實際需要您按照倉庫情況和構建流程自行配置", "i18n_42a93314b4": "基礎鏡像", "i18n_42b6bd1b2f": "倉庫路徑", "i18n_42f766b273": "掛載分區", "i18n_42fd64c157": "先啟動", "i18n_4310e9ed7d": "請選擇項目運行方式", "i18n_43250dc692": "觸發器管理", "i18n_434d888f6f": "請選擇文件中心的文件", "i18n_434d9bd852": "創建用户後自動關聯上對應的權限組", "i18n_4360e5056b": "加載數據中", "i18n_436367b066": "項目管理", "i18n_4371e2b426": "請輸入項目名稱", "i18n_43886d7ac3": "新增運行參數", "i18n_4393b5e25b": "環回", "i18n_43c61e76e7": "注意：目前對 SSH key 訪問 git 倉庫地址不支持使用 ssh-keygen -t rsa -C", "i18n_43d229617a": "待選擇", "i18n_43e534acf9": "寬鬆", "i18n_43ebf364ed": "請選擇備份類型", "i18n_4403fca0c0": "清除", "i18n_44473c1406": "開啟緩存構建目錄將保留倉庫文件,二次構建將 pull 代碼, 不開啟緩存目錄每次構建都將重新拉取倉庫代碼(較大的項目不建議關閉緩存) 、特別説明如果緩存目錄中缺失版本控制相關文件將自動刪除後重新拉取代碼", "i18n_4482773688": "請輸入權限組名稱", "i18n_44876fc0e7": "如果不可以選擇則表示對應的用户沒有配置郵箱", "i18n_449fa9722b": "為了考慮系統安全我們強烈建議超級管理員開啟兩步驗證來確保賬號的安全性", "i18n_44a6891817": "新增構建", "i18n_44c4aaa1d9": "運行模式", "i18n_44d13f7017": "限定時間", "i18n_44ed625b19": "網絡異常", "i18n_44ef546ded": "項目監控 【暫不支持遷移】", "i18n_44efd179aa": "退出登錄", "i18n_45028ad61d": "證書密碼", "i18n_4524ed750d": "工作空間名", "i18n_456d29ef8b": "日誌", "i18n_458331a965": "確認要上傳文件更新到最新版本嗎？", "i18n_45a4922d3f": "關聯數據", "i18n_45b88fc569": "匹配路徑中的零個或多個目錄", "i18n_45f8d5a21d": "真的要刪除用户麼？", "i18n_45fbb7e96a": "項目孤獨數據", "i18n_46032a715e": "還沒有選擇構建方式", "i18n_4604d50234": "錯誤信息", "i18n_46097a1225": "修正孤獨數據", "i18n_46158d0d6e": "禁用監控", "i18n_461e675921": "當前數據為默認狀態,操後上移或者下移可能不會達到預期排序,還需要對相關數據都操作後才能達到預期排序", "i18n_461ec75a5a": "路徑：", "i18n_461fdd1576": "打包生產環境包 mvn clean package -Dmaven.test.skip=true -Pprod", "i18n_4637765b0a": "未啟用", "i18n_463e2bed82": "批量更新", "i18n_4642113bba": "點擊儀表盤查看監控歷史數據", "i18n_4645575b77": "工作空間描述", "i18n_464f3d4ea3": "角色", "i18n_465260fe80": "年", "i18n_4696724ed3": "觸發器", "i18n_46a04cdc9c": "文件描述：", "i18n_46aca09f01": "解綁會檢查數據關聯性,不會真實請求節點刪除項目信息", "i18n_46ad87708f": "ssh名稱", "i18n_46c8ba7b7f": "如果按鈕不可用,請去資產管理 ssh 列表的關聯中新增當前工作空間允許管理的授權文件夾", "i18n_46e3867956": "執行中", "i18n_46e4265791": "構建 ID", "i18n_4705b88497": "作用域", "i18n_47072e451e": "管理節點：", "i18n_470e9baf32": "允許執行的內存節點", "i18n_471c6b19cf": "遷移前您檢查遷出機器和遷入機器的連接狀態和網絡狀態避免未知錯誤或者中斷造成流程失敗產生宂餘數據！！！！", "i18n_4722bc0c56": "終端", "i18n_473badc394": "發佈的節點", "i18n_4741e596ac": "報警時間", "i18n_475a349f32": "當前構建還沒有生成觸發器", "i18n_475cd76aec": "統計的網卡：", "i18n_47768ed092": "極不安全", "i18n_47bb635a5c": "數據可能出現一定時間延遲", "i18n_47d68cd0f4": "服務", "i18n_47dd8dbc7d": "搜索項目ID", "i18n_47e4123886": "新增分發", "i18n_47ff744ef6": "編輯文件", "i18n_481ffce5a9": "匹配秒", "i18n_4826549b41": "命令模版是用於在線管理一些腳本命令，如初始化軟件環境、管理應用程序等", "i18n_48281fd3f0": "真的要刪除構建信息麼？刪除也將同步刪除所有的構建歷史記錄信息", "i18n_4838a3bd20": "按住 Ctr 或者 Alt/Option 鍵點擊按鈕快速回到第一頁", "i18n_4871f7722d": "任務更新時間", "i18n_48735a5187": "剩餘空間(未分配)", "i18n_48a536d0bb": "修改容器配置，重新運行", "i18n_48d0a09bdd": "淺色", "i18n_48e79b3340": "】文件麼？", "i18n_48fe457960": "(存在兼容問題,實際使用中需要提前測試) go sdk 鏡像使用：https://studygolang.com/dl/golang/go{'${GO_VERSION}'}.linux-{'${ARCH}'}.tar.gz", "i18n_4956eb6aaa": "負載", "i18n_49574eee58": "確定要操作嗎？", "i18n_49645e398b": "如果配置錯誤需要重啟服務端並新增命令行參數 --rest:ip_config 將恢復默認配置", "i18n_497bc3532b": "JVM 參數", "i18n_497ddf508a": "新建空白文件", "i18n_498519d1af": "刷新數據", "i18n_499f058a0b": "退出登錄成功", "i18n_49a9d6c7e6": "通過以下二維碼進行一次性捐款贊助，請作者喝一杯咖啡☕️", "i18n_49d569f255": "請輸入要檢查的 host", "i18n_49e56c7b90": "確認修改", "i18n_4a00d980d5": "簡單好用", "i18n_4a0e9142e7": "釘釘", "i18n_4a346aae15": "插件版本:", "i18n_4a4e3b5ae4": "描述：", "i18n_4a5ab3bc72": "操作：", "i18n_4a6f3aa451": " ：每個點鐘的5分執行，00:05,01:05……", "i18n_4a98bf0c68": "任務詳情", "i18n_4aac559105": "權重", "i18n_4ab578f3df": "環境變量：", "i18n_4ad6e58ebc": "機器SSH", "i18n_4af980516d": "為了您的賬號安全系統要求必須開啟兩步驗證來確保賬號的安全性", "i18n_4b027f3979": "提醒", "i18n_4b0cb10d18": "請輸入 SMTP host", "i18n_4b1835640f": "在 Settings-->Developer settings-->Personal access tokens 中獲取", "i18n_4b386a7209": "獲取變量值地址", "i18n_4b404646f4": "容器標籤,如：key1=values1&keyvalue2", "i18n_4b5e6872ea": "駐留集", "i18n_4b96762a7e": "最後修改時間", "i18n_4b9c3271dc": "重置", "i18n_4ba304e77a": "釘釘賬號登錄", "i18n_4bbc09fc55": "在文件第 3 - 20 行中搜索", "i18n_4c096c51a3": "端口號：", "i18n_4c0eead6ff": "新增參數", "i18n_4c28044efc": "確認要將選中的  ", "i18n_4c69102fe1": "再判斷允許時段。配置允許時段後用户只能在對應的時段執行相應功能的操作", "i18n_4c7c58b208": "請選擇節點狀態", "i18n_4c7e4dfd33": "當目標工作空間不存在對應的節點時候將自動創建一個新的docker（邏輯docker）", "i18n_4c83203419": "跳轉到第三方系統中", "i18n_4c9bb42608": "前綴", "i18n_4cbc136874": "文件夾：", "i18n_4cbc5505c7": "差異構建是指構建時候是否判斷倉庫代碼有變動，如果沒有變動則不執行構建", "i18n_4ccbdc5301": "菜單", "i18n_4cd49caae4": "分發耗時", "i18n_4ce606413e": "倉庫類型", "i18n_4cfca88db8": "選擇分發文件", "i18n_4d18dcbd15": "真的要還原備份信息麼？", "i18n_4d351f3c91": "禁止 IP", "i18n_4d49b2a15f": "自動執行：docker", "i18n_4d775d4cd7": "顯示", "i18n_4d7dc6c5f8": "寫", "i18n_4d85ac1250": "系統管理", "i18n_4d85c37f0d": "工作空間：", "i18n_4d9c3a0ed0": "Script 內容", "i18n_4dc781596b": "中使用瞭如下開源軟件，我們衷心感謝有了他們的開源 Jpom 才能更完善", "i18n_4df483b9c7": "項目文件 ", "i18n_4e33dde280": "當前目錄:", "i18n_4e54369108": "文件類型沒有觸發器功能", "i18n_4e7e04b15d": "服務名稱必填", "i18n_4ed1662cae": "請選擇連接方式", "i18n_4ee2a8951d": "接口響應 ContentType 均為：text/plain", "i18n_4ef719810b": "沒有任何運行中的任務", "i18n_4effdeb1ff": "在文件第 1 - 2 行中搜索", "i18n_4f08d1ad9f": "算法 OID", "i18n_4f095befc0": "此配置僅對服務端管理生效, 工作空間的 ssh 配置需要單獨配置", "i18n_4f35e80da6": "路徑", "i18n_4f4c28a1fb": "文件內容格式要求：env_name=xxxxx 不滿足格式的行將自動忽略", "i18n_4f50cd2a5e": "緊湊模式", "i18n_4f52df6e44": "關閉中", "i18n_4f8a2f0b28": "未運行", "i18n_4f8ca95e7b": "名", "i18n_4f9e3db4b8": "選擇構建", "i18n_4fb2400af7": "容器是運行中可以進入終端", "i18n_4fb95949e5": "開啟中", "i18n_4fdd2213b5": "項目 ID", "i18n_500789168c": "清空還原將會先刪除項目目錄中的文件再將對應備份文件恢復至當前目錄", "i18n_5011e53403": "發佈集羣", "i18n_503660aa89": "排除：", "i18n_50411665d7": "保留個數", "i18n_50453eeb9e": "當前工作空間還沒有邏輯節點不能創建節點腳本奧", "i18n_504c43b70a": "端口/PID", "i18n_5068552b18": "歷史監控圖表", "i18n_50940ed76f": "下載成功", "i18n_50951f5e74": "請選擇分支", "i18n_50a299c847": "構建名稱", "i18n_50c7929dd9": " 歡迎 ", "i18n_50d2671541": "確定是同一個腳本", "i18n_50ed14e70b": "深色 dracula", "i18n_50f472ee4e": "單位秒，默認 10 秒,最小 3 秒", "i18n_50f975c08e": "構建產物保留天數，小於等於 0 為跟隨全局保留配置。注意自動清理僅會清理記錄狀態為：（構建結束、發佈中、發佈失敗、發佈失敗）的數據避免一些異常構建影響保留個數", "i18n_50fb61ef9d": "腳本名稱", "i18n_50fe3400c7": "真的要刪除該執行記錄嗎？", "i18n_50fefde769": "是否為壓縮包", "i18n_512e1a7722": "請選擇操作者", "i18n_51341b5024": "服務端分發的腳本", "i18n_514b320d25": "如何選擇構建方式", "i18n_5169b9af9d": "信息丟失", "i18n_5177c276a0": "集羣不能手動創建，創建需要多個服務端使用通一個數據庫，並且配置不同的集羣 id 來自動創建集羣信息", "i18n_518df98392": "從尾搜索", "i18n_5195c0d198": "可以管理{count}個工作空間", "i18n_51c92e6956": "同步系統配置", "i18n_51d47ddc69": "回調 url", "i18n_51d6b830d4": "在線構建目錄", "i18n_52409da520": "聯繫人", "i18n_527466ff94": "請求參數", "i18n_527f7e18f1": "上傳前請閲讀更新日誌裏面的説明和注意事項並且更新前", "i18n_52a8df6678": "】文件夾麼？", "i18n_52b526ab9e": "清空瀏覽器緩存配置將恢復默認", "i18n_52b6b488e2": "腳本模版是存儲在節點(插件端),執行也都將在節點裏面執行,服務端會定時去拉取執行日誌,拉取頻率為 100 條/分鐘", "i18n_52c6af8174": "請輸入客户端密鑰 [clientSecret]", "i18n_52d24791ab": "真的要刪除這些文件麼？", "i18n_52eedb4a12": "報警方式", "i18n_52ef46c618": "不發佈：只執行構建流程並且保存構建歷史", "i18n_532495b65b": "副本數", "i18n_53365c29c8": "下載狀態：", "i18n_534115e981": "信息不完整不能編輯", "i18n_5349f417e9": "搜關鍵詞", "i18n_536206b587": "當前機器還未監控到任何數據", "i18n_537b39a8b5": "必填", "i18n_53bdd93fd6": "查看腳本庫", "i18n_541e8ce00c": "關於開源軟件", "i18n_542a0e7db4": "同步授權", "i18n_543296e005": "請輸入授權 url [authorizationUri]", "i18n_543a5aebc8": "真的刪除當前變量嗎？", "i18n_543de6ff04": "分發狀態消息", "i18n_54506fe138": "重置選擇", "i18n_5457c2e99f": "# 使用 copy 文件的方式緩存，反之使用軟鏈的形式。copy 文件方式緩存 node_modules 可以避免 npm WARN reify Removing non-directory", "i18n_547ee197e5": "新建目錄", "i18n_5488c40573": "節點項目", "i18n_54f271cd41": "腳本模板", "i18n_5516b3130c": "飛書賬號登錄", "i18n_551e46c0ea": " 名稱： ", "i18n_55405ea6ff": "導出", "i18n_556499017a": "項目文件會存放到", "i18n_5569a840c8": "請輸入IP禁止,多個使用換行,支持配置IP段 ***********/*************,***********/24", "i18n_55721d321c": "參數描述", "i18n_55939c108f": "輸入文件或者文件夾名", "i18n_55abea2d61": "服務端", "i18n_55b2d0904f": "在執行多節點分發時候使用 順序重啟、完整順序重啟 時候需要保證項目能正常重啟", "i18n_55cf956586": "加入集羣", "i18n_55d4a79358": "配置需要聲明使用具體的 docker 來執行構建相關操作(建議使用服務端所在服務器中的 docker)", "i18n_55da97b631": " ，範圍0~59，但是第一位不做匹配當為7位時，最後一位表示", "i18n_55e690333a": "當前工作空間還沒有 Docker 集羣", "i18n_55e99f5106": "釘釘通知地址", "i18n_55f01e138a": "微信讚賞", "i18n_56071a4fa6": "超時時間", "i18n_56230405ae": "解綁不會真實請求節點刪除腳本信息", "i18n_562d7476ab": "週日", "i18n_56469e09f7": "請到【系統管理】-> 【資產管理】-> 【Docker管理】新增Docker，或者將已新增的Docker授權關聯、分配到此工作空間", "i18n_56525d62ac": "掃描", "i18n_566c67e764": "已經分配到工作空間的機器無法直接刪除，需要到分配到的各個工作空間逐一刪除後才能刪除資產機器", "i18n_5684fd7d3d": "賬號新密碼為：", "i18n_56bb769354": "下載前請閲讀更新日誌裏面的説明和注意事項並且更新前", "i18n_56d9d84bff": "工作空間中邏輯節點中的項目數量：", "i18n_570eb1c04f": "硬盤佔用率：", "i18n_5734b2db4e": "讀取行數", "i18n_576669e450": "請選中要啟動的項目", "i18n_5785f004ea": "請勿手動刪除數據目錄下面文件,如果需要刪除需要提前備份或者已經確定對應文件棄用後才能刪除", "i18n_578adf7a12": "請仔細確認後配置，ip配置後立即生效。配置時需要保證當前ip能訪問！127.0.0.1 該IP不受訪問限制.支持配置IP段 ***********/*************,***********/24", "i18n_578ca5bcfd": "163郵箱", "i18n_57978c11d1": "日誌彈窗會非全屏打開", "i18n_579a6d0d92": "命令值", "i18n_57b7990b45": "當目標工作空間已經存在 SSH 時候將自動同步 SSH 賬號、密碼、私鑰等信息", "i18n_57c0a41ec6": "當前數據為默認狀態", "i18n_57cadc4cf3": "會使用 PING 檢查", "i18n_5805998e42": "重啟策略", "i18n_5854370b86": "跟蹤文件", "i18n_585ae8592f": "重建容器", "i18n_5866b4bced": "集羣數：", "i18n_587a63264b": "覆蓋還原", "i18n_588e33b660": "賬號如果開啟 MFA(兩步驗證)，使用 Oauth2 登錄不會驗證 MFA(兩步驗證)", "i18n_589060f38e": "升級中，請稍候...", "i18n_5893fa2280": "郵箱賬號", "i18n_58cbd04f02": "SSH 是指,通過 SSH 命令的方式對產物進行發佈或者執行多條命令來實現發佈(需要到 SSH 中提前去新增)", "i18n_58e998a751": "刪除會檢查數據關聯性,並且節點不存在項目或者腳本", "i18n_58f9666705": "大小", "i18n_590b9ce766": "目前支持都插件有（更多插件盡情期待）：", "i18n_590dbb68cf": "結束時間：", "i18n_590e5b46a0": "自動備份", "i18n_592c595891": "開始時間", "i18n_5936ed11ab": "腳本庫用於存儲管理通用的腳本,腳本庫中的腳本不能直接執行。", "i18n_593e04dfad": "菜單主題", "i18n_597b1a5130": "更新狀態", "i18n_59a15a0848": "同步機制採用 IP+PORT+連接方式 確定是同一個服務器", "i18n_59c316e560": "分發文件", "i18n_59c75681b4": "通知對象", "i18n_59cf15fe6b": "模板", "i18n_59d20801e9": "在文件第 17 - 20 行中搜索", "i18n_5a0346c4b1": "編輯用户", "i18n_5a1367058c": "返回首頁", "i18n_5a1419b7a2": "數據名稱", "i18n_5a42ea648d": "自建 gitlab 訪問地址", "i18n_5a5368cf9b": "密碼錯誤", "i18n_5a63277941": "的值有：stop、beforeStop、start、beforeRestart、fileChange", "i18n_5a7ea53d18": "docker信息", "i18n_5a8727305e": "請不要優先退出管理節點", "i18n_5a879a657b": "交換內存", "i18n_5aabec5c62": "父級ID", "i18n_5ab90c17a3": "任務結束", "i18n_5ab9bf3591": " 是否需要使用 sudo 執行：docker system dial-stdio ", "i18n_5ad7f5a8b2": "結果", "i18n_5ae4a8f177": "請輸入沉默時間", "i18n_5afe5e7ed4": "編輯關聯項目", "i18n_5b1f0fd370": "用於創建節點分發項目、文件中心發佈文件", "i18n_5b3ffc2910": "分發中", "i18n_5b47861521": "名稱：", "i18n_5baaef6996": "點擊重新同步當前工作空間邏輯節點腳本模版信息", "i18n_5badae1d90": "沒有任何腳本", "i18n_5bb162ecbb": "JVM剩餘內存", "i18n_5bb5b33ae4": "所以這裏 我們", "i18n_5bca8cf7ee": "自定義host, xxx:192.168.0.x", "i18n_5bcda1b4d7": "會話已經關閉[system-log]", "i18n_5bd1d267a9": "在 preferences-->Access Tokens 中獲取", "i18n_5c3b53e66c": "修改文件", "i18n_5c4d3c836f": "需要驗證 MFA", "i18n_5c502af799": "容器名稱必填", "i18n_5c56a88945": "停用", "i18n_5c89a5353d": "分配節點", "i18n_5c93055d9c": "一般用於服務器無法連接且已經確定不再使用", "i18n_5ca6c1b6c7": "請填寫集羣名稱", "i18n_5cb39287a8": "監控功能", "i18n_5cc7e8e30a": "修改文件權限", "i18n_5d07edd921": "請填寫集羣IP", "i18n_5d14e91b01": "主要ID", "i18n_5d368ab0a5": "執行命令將自動替換為 sh 命令文件、並自動加載環境變量：/etc/profile、/etc/bashrc、~/.bashrc、~/.bash_profile", "i18n_5d414afd86": "從尾搜索、文件前2行、文件後3行", "i18n_5d459d550a": "處理中", "i18n_5d488af335": "遠程下載文件", "i18n_5d5fd4170f": "的值有：1", "i18n_5d6f47d670": "項目為靜態文件夾", "i18n_5d803afb8d": "不能和節點正常通訊", "i18n_5d817c403e": "沒有選擇任何數據", "i18n_5d83794cfa": "節點名稱：", "i18n_5d9c139f38": "內容主題", "i18n_5dc09dd5bd": "重連 ", "i18n_5dc1f36a27": "證書描述", "i18n_5dc78cb700": "構建產物保留個數，小於等於 0 為跟隨全局保留配置（如果數值大於 0 將和全局配置對比最小值來參考）。注意自動清理僅會清理記錄狀態為：（構建結束、發佈中、發佈失敗、發佈失敗）的數據避免一些異常構建影響保留個數。 將在創建新的構建記錄時候檢查保留個數", "i18n_5dc7b04caa": "查看的進程數量", "i18n_5dff0d31d0": "如果需要定時自動執行則填寫,cron 表達式.默認未開啟秒級別,需要去修改配置文件中:[system.timerMatchSecond]）", "i18n_5e32f72bbf": "刷新文件表格", "i18n_5e46f842d8": "監控用户", "i18n_5e9f2dedca": "是否成功", "i18n_5ecc709db7": "執行時候默認不加載全部環境變量、需要腳本里面自行加載", "i18n_5ed197a129": "重置初始化在啟動時候傳入參數", "i18n_5ef040a79d": "丟棄包", "i18n_5ef72bdfce": "命令內容支持工作空間環境變量", "i18n_5effe31353": "剔除文件夾", "i18n_5f4c724e61": "請輸入任務名", "i18n_5f5cd1bb1e": "新增關聯項目是指,將已經在節點中創建好的項目關聯為節點分發項目來實現統一管理", "i18n_5fafcadc2d": "會話已經關閉[node-script-consloe]", "i18n_5fbde027e3": "可以引用工作空間的環境變量 變量佔位符 {'${xxxx}'} xxxx 為變量名稱", "i18n_5fc6c33832": " 跳至行", "i18n_5fea80e369": "沒有資產DOCKER", "i18n_5fffcb255d": "插件運行", "i18n_601426f8f2": "推送到倉庫", "i18n_603dc06c4b": "您訪問的頁面不存在", "i18n_60585cf697": " 歡迎", "i18n_607558dbd4": "項目數", "i18n_607e7a4f37": "查看", "i18n_609b5f0a08": "時", "i18n_60b4c08f5c": "您確定要停止當前容器嗎？", "i18n_6106de3d87": "JDK版本", "i18n_61341628ab": " ：表示列表", "i18n_6143a714d0": "編碼格式", "i18n_616879745d": "凌晨0點和中午12點", "i18n_61955b0e4b": "沒有項目狀態以及控制等功能", "i18n_61a3ec6656": "介紹", "i18n_61bfa4e925": "需要在倉庫裏面 dockerfile,如果多文件夾查看可以指定二級目錄如果 springboot-test-jar:springboot-test-jar/Dockerfile", "i18n_61c0f5345d": "SMTP 地址：【smtp.163.com, smtp.126.com...】，密碼是郵箱授權碼，端口默認 25，SSL 端口 465", "i18n_61e7fa1227": "編輯節點", "i18n_61e84eb5bb": "開始時間：", "i18n_620489518c": "參數{index}值", "i18n_620efec150": "更多開源説明", "i18n_62170d5b0a": "搜索參考", "i18n_6228294517": "菜單配置只對非超級管理員生效", "i18n_622d00a119": "執行腳本的路徑", "i18n_624f639f16": "通用郵箱", "i18n_625aa478e2": "從尾搜索、文件前0行、文件後3行", "i18n_625fb26b4b": "取消", "i18n_627c952b5e": "總空間", "i18n_6292498392": " 查找下一個", "i18n_629a6ad325": "安全管理", "i18n_629f3211ca": "修剪類型", "i18n_631d5b88ab": "請輸入項目存放路徑授權，回車支持輸入多個路徑，系統會自動過濾 ../ 路徑、不允許輸入根路徑", "i18n_632a907224": "重置為重新生成觸發地址,重置成功後之前的觸發器地址將失效,觸發器綁定到生成觸發器到操作人上,如果將對應的賬號刪除觸發器將失效", "i18n_6334eec584": "5秒一次", "i18n_635391aa5d": "下載產物", "i18n_637c9a8819": "至少選擇1個節點項目", "i18n_638cddf480": "創建人,全匹配", "i18n_639fd37242": "目前使用的 docker swarm 集羣，需要先創建 swarm 集羣才能選擇", "i18n_63b6b36c71": "選擇證書", "i18n_63c9d63eeb": "可以同時展開多個菜單", "i18n_63dd96a28a": "密碼支持引用工作空間變量：", "i18n_63e975aa63": "安裝ID:", "i18n_640374b7ae": "掛載卷", "i18n_641796b655": "構建完成", "i18n_6428be07e9": "配置系統公吿", "i18n_643f39d45f": "非懸空", "i18n_6446b6c707": "暱稱長度為2-10", "i18n_646a518953": "請輸入項目ID", "i18n_6470685fcd": "：表示匹配這個位置任意的時間（與\"*\"作用一致）", "i18n_649231bdee": "文件後綴", "i18n_64933b1012": "存儲選項", "i18n_6496a5a043": "命令名稱", "i18n_649d7fcb73": "新集羣需要手動配置集羣管理資產分組、集羣訪問地址", "i18n_649d90ab3c": "關閉右側", "i18n_649f8046f3": "請選擇SSH節點", "i18n_64c083c0a9": "結果描述", "i18n_64eee9aafa": "開機時間", "i18n_652273694e": "主機", "i18n_65571516e2": "構建備註：", "i18n_657969aa0f": "編輯  Docker", "i18n_657f3883e3": "不執行發佈流程", "i18n_65894da683": "發佈方式:", "i18n_65cf4248a8": "不能初始化", "i18n_65f66dfe97": "清空當前緩衝區內容", "i18n_66238e0917": "已經存在的賬號與外部系統賬號不一致時不支持綁定外部系統賬號", "i18n_663393986e": "解綁", "i18n_6636793319": "真的要刪除節點麼？刪除會檢查數據關聯性,並且節點不存在項目或者腳本", "i18n_664b37da22": "備份", "i18n_664c205cc3": "真的要清除倉庫隱藏字段信息麼？（密碼，私鑰）", "i18n_667fa07b52": "個節點升級到", "i18n_66aafbdb72": "最新構建ID", "i18n_66ab5e9f24": "新增", "i18n_66b71b06c6": "上傳壓縮文件（自動解壓）", "i18n_66c15f2815": "匹配包含數字的行", "i18n_66e9ea5488": "日誌名稱", "i18n_6707667676": "主機名", "i18n_6709f4548f": "隨機生成", "i18n_67141abed6": "項目授權路徑+項目文件夾", "i18n_67425c29a5": "超時時間(s)", "i18n_674a284936": "當isMatchSecond為 true 時才會匹配秒部分默認都是關閉的", "i18n_674e7808b5": "mfa 驗證碼", "i18n_679de60f71": "請填寫日誌項目名稱", "i18n_67aa1c0169": "請填寫構建命令", "i18n_67aa2d01b9": "工作空間的菜單、環境變量、節點分發授權需要逐一配置", "i18n_67b667bf98": "部分備份", "i18n_67e3d3e09c": "批量構建", "i18n_67e7f9e541": "監控週期", "i18n_6816da19f3": "關閉其他", "i18n_6835ed12b9": "環境變量的key", "i18n_685e5de706": "容器構建", "i18n_6863e2a7b5": "腳本執行歷史", "i18n_686a19db6a": "自動刪除", "i18n_68a1faf6e2": "批量構建傳入其他參數將同步執行修改", "i18n_68af00bedb": "表格視圖才能使用工作空間同步功能", "i18n_68c55772ca": "請輸入授權方的網頁應用ID", "i18n_69056f4792": "部分操作狀態碼可能為 0", "i18n_690a3d1a69": "執行容器", "i18n_691b11e443": "當前工作空間", "i18n_6928f50eb3": "支持配置系統參數：", "i18n_69384c9d71": "點擊查看歷史趨勢", "i18n_693a06987c": "請填寫用户賬號", "i18n_6948363f65": "取消定時,不再定時執行（支持 ! 前綴禁用定時執行，如：!0 0/1 * * * ?）", "i18n_694fc5efa9": "刷新", "i18n_695344279b": "文件上傳id生成失敗：", "i18n_6953a488e3": "選擇邏輯節點", "i18n_697d60299e": "檢測到當前已經登錄賬號", "i18n_69c3b873c1": "本地構建", "i18n_69c743de70": "節點的IP", "i18n_69de8d7f40": "還原", "i18n_6a359e2ab3": "scriptId也可以引入腳本庫中的腳本,需要提前同步至機器節點中", "i18n_6a49f994b1": "構建過程執行對應的腳本,開始構建,構建完成,開始發佈,發佈完成,構建異常,發佈異常", "i18n_6a4a0f2b3b": "同步機制採用節點地址確定是同一個服務器（節點）", "i18n_6a588459d0": "工作空間名稱", "i18n_6a620e3c07": "同步", "i18n_6a658517f3": "任務日誌", "i18n_6a66d4cdf3": "延遲,容器回滾間隔時間", "i18n_6a6c857285": "分發節點", "i18n_6a8402afcb": "解析文件,準備上傳中 ", "i18n_6a8c30bd06": "加載編輯器中", "i18n_6a922e0fb6": "插件端端口", "i18n_6a9231c3ba": "函數 args 參數，非必填", "i18n_6aa7403b18": "如果使用 SSH 方式但是 SSH 無法選擇，是表示系統沒有監測到 docker 服務", "i18n_6aab88d6a3": "保存並重啟", "i18n_6ab78fa2c4": "郵箱地址", "i18n_6ac61b0e74": "建議還原和當前版本一致的文件或者臨近版本的文件", "i18n_6ad02e7a1b": "頁面資源加載中....", "i18n_6adcbc6663": "配置方式：SSH列表->操作欄中->關聯按鈕->對應工作空間->操作欄中->配置按鈕", "i18n_6af7686e31": "分鐘刷新一次", "i18n_6b0bc6432d": "操作者", "i18n_6b189bf02d": "容器數：", "i18n_6b29a6e523": "啟動項目", "i18n_6b2e348a2b": "定時執行", "i18n_6b46e2bfae": "真的當前工作空間麼", "i18n_6b4fd0ca47": "支持配置發送方：遵循RFC-822標準 發件人可以是以下形式：", "i18n_6b6d6937d7": "163郵箱 SSL", "i18n_6bb5ba7438": "限制禁止在在線終端執行的命令", "i18n_6be30eaad7": "請輸入超時時間", "i18n_6bf1f392c0": "當前狀態", "i18n_6c08692a3a": "密碼若沒修改可以不用填寫", "i18n_6c14188ba0": "不能下載目錄", "i18n_6c24533675": "請選擇一位報警聯繫人或者填寫webhook", "i18n_6c72e9d9de": "編輯分發項目", "i18n_6c776e9d91": "項目啟動,停止,重啟,文件變動都將請求對應的地址,非必填，GET請求", "i18n_6d110422ce": "（需要選擇以什麼方式去重存儲發佈模板，默認每一種方式只會存儲保留最新的發佈模板,別名模板優先級最高）", "i18n_6d5f0fb74b": "鏡像構建成功後是否需要推送到遠程倉庫", "i18n_6d68bd5458": "全量備份", "i18n_6d7f0f06be": "請選擇發佈操作", "i18n_6d802636ab": "隱私", "i18n_6da242ea50": "任務Id", "i18n_6dcf6175d8": "現在就去", "i18n_6de1ecc549": "查看服務端腳本", "i18n_6e02ee7aad": "週期的長度，以微秒為單位。", "i18n_6e2d78a20e": "從尾搜索、文件前20行、文件後3行", "i18n_6e60d2fc75": "頁面啟用緊湊模式", "i18n_6e69656ffb": "文件不能為空", "i18n_6e70d2fb91": "構建參數,如：key1=value1&key2=value2", "i18n_6ea1fe6baa": "基礎信息", "i18n_6eb39e706c": "編輯機器", "i18n_6ef90ec712": "請填寫要拉取的鏡像名稱", "i18n_6f15f0beea": "兩次密碼不一致...", "i18n_6f32b1077d": "請輸入工作空間備註,留空使用默認的名稱", "i18n_6f5b238dd2": " SSH、本地命令發佈都執行變量替換,系統預留變量有：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_RESULT_FILE}'}、{'${BUILD_NUMBER_ID}'}", "i18n_6f6ee88ec4": "支持開源", "i18n_6f73c7cf47": "保留天數：", "i18n_6f7ee71e77": "靜態目錄", "i18n_6f854129e9": "分組/標籤", "i18n_6f8907351b": "同步節點配置", "i18n_6f8da7dcca": "節點地址格式為：IP:PORT (示例：*************:2123)", "i18n_6f9193ac80": "啟用兩步驗證", "i18n_6fa1229ea9": "一鍵分發同步多個節點的授權配置", "i18n_6ffa21d235": "分發節點是指將變量同步到對應節點，在節點腳本中也可以使用當前變量", "i18n_7006410585": "無論返回什麼退出代碼，始終重新啟動容器。", "i18n_7010264d22": "沒有開啟任何認證", "i18n_702430b89d": "頁面啟用寬鬆模式", "i18n_702afc34a0": "差異發佈：", "i18n_7030ff6470": "錯誤", "i18n_7035c62fb0": "賬號", "i18n_704f33fc74": "從頭搜索、文件前0行、文件後3行", "i18n_706333387b": "此功能不能保證新增的容器和之前容器參數完全一致請慎重使用。", "i18n_7088e18ac9": "卷", "i18n_708c9d6d2a": "請選擇", "i18n_70a6bc1e94": "當前系統已經初始化過啦,不能重複初始化", "i18n_70b3635aa3": "執行時間", "i18n_70b5b45591": "快速安裝", "i18n_70b9a2c450": "真的要退出系統麼？", "i18n_710ad08b11": "禁用", "i18n_7114d41b1d": "超級管理員沒有任何限制", "i18n_712cdd7984": "合作諮詢", "i18n_713c986135": "容器構建會在 docker 中生成相關掛載目錄,一般情況不需要人為操作", "i18n_7156088c6e": "編碼方式", "i18n_71584de972": "非服務器開機自啟,如需開機自啟建議配置", "i18n_715ec3b393": "用於快捷同步其他機器節點的配置", "i18n_7173f80900": "拒絕", "i18n_71a2c432b0": "編輯變量", "i18n_71bbc726ac": "跟隨系統", "i18n_71c6871780": "定時任務表達式", "i18n_71dc8feb59": "未配置", "i18n_71ee088528": "橋接模式：", "i18n_7220e4d5f9": "方式", "i18n_7229ecc631": "次", "i18n_7293bbb0ff": "總 inode 數", "i18n_729eebb5ff": "沒有對應的SSH", "i18n_72d14a3890": "請選擇用户的權限組", "i18n_72d46ec2cf": "登錄信息過期", "i18n_72d4ade571": ",僅是用於提示參數的含義", "i18n_72e7a5d105": "鏡像id", "i18n_72eae3107e": "灰綠 abbott", "i18n_72ebfe28b0": "定時", "i18n_7307ca1021": "自動啟動", "i18n_7327966572": "徹底刪除", "i18n_7329a2637c": "集羣ID", "i18n_73485331c2": "文件信息", "i18n_73578c680e": "數據目錄是指程序在運行過程中產生的文件以及數據存儲目錄", "i18n_73651ba2db": "批量重啟", "i18n_7370bdf0d2": "腳本日誌", "i18n_738a41f965": "項目名稱", "i18n_73a87230e0": "文件系統類型", "i18n_73b7b05e6e": " 將腳本分發到對應的機器節點中，對應的機器節點可以引用對應的腳本 ", "i18n_73b7e8e09e": "在使用 beta 版過程中遇到問題可以隨時反饋給我們，我們會盡快為您解答。", "i18n_73c980987a": "加載容器可用標籤中....", "i18n_73d8160821": "以 yaml/yml 格式配置,scriptId 為項目路徑下的腳本文件的相對路徑或者腳本模版ID，可以到腳本模版編輯彈窗中查看 scriptId", "i18n_73ed447971": "強烈建議您使用 TLS 證書", "i18n_73f798a129": "免費社羣", "i18n_7457228a61": "遠程下載地址", "i18n_74bdccbb5d": "我的工作空間", "i18n_74c5c188ae": "操作成功接口 HTTP 狀態碼為 200", "i18n_74d5f61b9f": "構建觸發", "i18n_74d980d4f4": "在單頁列表裏面 file 類型項目將自動排序到最後", "i18n_74dc77d4f7": "容器id", "i18n_74dd7594fc": "發生報警時候請求", "i18n_74ea72bbd6": "集羣管理", "i18n_751a79afde": "30分鐘", "i18n_7527da8954": "普通用户", "i18n_7548ea6316": "點擊可以摺疊左側菜單欄", "i18n_75528c19c7": "自動重啟", "i18n_7561bc005e": "構建過程請求,非必填，GET請求", "i18n_75769d1ac8": "讀", "i18n_757a730c9e": "無法連接", "i18n_758edf4666": "從頭搜索、文件前2行、文件後3行", "i18n_75c63f427a": "此選項為一個實驗屬性實際效果基本無差異", "i18n_75fc7de737": "路由", "i18n_7617455241": "文件中如果存在：MemAvailable、MemTotal 這兩個字段，那麼 oshi 直接使用，所以本系統 中內存佔用計算方式：內存佔用=(total-available)/total", "i18n_762e05a901": "差異發佈是指對應構建產物和項目文件夾裏面的文件是否存在差異,如果存在增量差異那麼上傳或者覆蓋文件。", "i18n_7650487a87": "地址", "i18n_76530bff27": "請輸入私人令牌", "i18n_7653297de3": "跳轉", "i18n_765592aa05": "如果未掛載容器數據目錄請提前備份數據後再使用此功能。", "i18n_765d09eea5": "當前文件不可讀,需要配置可讀文件授權", "i18n_767fa455bb": "目錄", "i18n_768e843a3e": "類 192", "i18n_769d88e425": "完成", "i18n_76aebf3cc6": "日誌大小", "i18n_76ebb2be96": "1分鐘", "i18n_77017a3140": "關聯容器標籤", "i18n_770a07d78f": "當目標工作空間不存在對應的 腳本 時候將自動創建一個新的 腳本", "i18n_771d897d9a": "狀態碼", "i18n_77373db7d8": "接收報警消息,非必填，GET請求", "i18n_7737f088de": "批量重新啟動", "i18n_773b1a5ef6": "請選擇語言模式", "i18n_775fde44cf": "進程端口緩存：", "i18n_7760785daf": "自由腳本", "i18n_7764df7ccc": "開啟差異發佈但不開啟清空發佈時相當於只做增量和變動更新", "i18n_77688e95af": "容器重建是指使用已經創建的容器參數重新創建一個相同的容器。", "i18n_776bf504a4": "上傳提示", "i18n_7777a83497": "請輸入構建備註,長度小於 240", "i18n_77834eb6f5": "您使用本系統", "i18n_7785d9e038": "低版本項目數據未存儲節點ID，對應項目數據也將出來在孤獨數據中（此類數據不影響使用）", "i18n_77b9ecc8b1": "備份名稱", "i18n_77c1e73c08": "腳本存放路徑：{'${user.home}'}/.jpom/xxxx.sh，執行腳本路徑：{'${user.home}'}，執行腳本方式：bash {'${user.home}'}/.jpom/xxxx.sh par1 par2", "i18n_77c262950c": "使用 Access Token 一次導入多個項目", "i18n_77e100e462": "還沒有狀態消息", "i18n_77e501b44b": "日誌文件：", "i18n_780afeac65": "是否開啟", "i18n_780fb9f3d0": "更新時間：", "i18n_7824ed010c": "真的取消當前發佈任務嗎？", "i18n_7854b52a88": "啟用", "i18n_787fdcca55": "系統配置", "i18n_788a3afc90": "已失聯", "i18n_78a4b837e3": "能通訊的IP", "i18n_78b2da536d": "構建過程請求對應的地址,開始構建,構建完成,開始發佈,發佈完成,構建異常,發佈異常", "i18n_78ba02f56b": "真的要徹底刪除分發信息麼？刪除後節點下面的項目也都將徹底刪除，徹底項目會自動刪除項目相關文件奧(包含項目日誌，日誌備份，項目文件)", "i18n_78caf7115c": "任務名稱", "i18n_78dccb6e97": "所有節點(插件端)", "i18n_79076b6882": "真的要批量刪除這些構建信息麼？刪除也將同步刪除所有的構建歷史記錄信息，如果中途刪除失敗將終止刪除操作", "i18n_7912615699": "連接狀態", "i18n_791870de48": "倉庫密碼", "i18n_791b6d0e62": "排名按照字母 a-z 排序", "i18n_79698c57a2": "當前工作空間還沒有節點", "i18n_798f660048": "模版節點", "i18n_799ac8bf40": "支持變量引用：{'${TASK_ID}'}、{'${FILE_ID}'}、{'${FILE_NAME}'}、{'${FILE_EXT_NAME}'}、{'${RELEASE_PATH}'}", "i18n_79a7072ee1": "令牌 url", "i18n_79c6b6cff7": "關聯分組", "i18n_79d3abe929": "複製", "i18n_7a28e9cd4a": "當監控到持續異常時監控通知發送成功後在一段時間內不重複發送報警通知", "i18n_7a30792e2a": "編輯 SSH", "i18n_7a3c815b1e": "文件目錄", "i18n_7a4ecc606c": "鏡像標籤,如：key1=values1&keyvalue2 使用 URL 編碼", "i18n_7a5dd04619": "注意執行相關命令需要所在服務器中存在對應的環境", "i18n_7a7e25e9eb": "確定要將此數據下移嗎？下移操作可能因為列表後續數據沒有排序值操作無效！", "i18n_7a811cc1e5": "複製 ", "i18n_7a93e0a6ae": "選擇企業版本或者購買授權：", "i18n_7aa81d1573": "請輸入文件名稱", "i18n_7aaee3201a": "如果需要刪除需要提前備份或者已經確定對應文件棄用後才能刪除 !!!!", "i18n_7afb02ed93": "當前沒有可以引用的環境變量", "i18n_7b2cbfada9": "發佈前停止：", "i18n_7b36b18865": "分區ID", "i18n_7b61408779": "# 項目文件備份路徑", "i18n_7b8e7d4abc": "真的要刪除執行記錄麼？", "i18n_7b961e05d0": "表示月的最後一天", "i18n_7bcbf81120": "接收包", "i18n_7bcc3f169c": "# 內置變量 {'${JPOM_WORKING_DIR}'} {'${JPOM_BUILD_ID}'}", "i18n_7bf62f7284": "手動取消分發", "i18n_7c0ee78130": "構建日誌", "i18n_7c223eb6e9": "發佈系統公吿", "i18n_7c9bb61536": "日誌項目名稱", "i18n_7cb8d163bb": "變量名稱", "i18n_7cc3bb7068": "不會真實請求節點刪除項目信息", "i18n_7ce511154f": "創建之後不能修改", "i18n_7d23ca925c": "服務端時間", "i18n_7d3f2fd640": "在文件第 3 - 2147483647 行中搜索", "i18n_7ddbe15c84": "網絡", "i18n_7dde69267a": "未綁定集羣的分組：", "i18n_7de5541032": "如果 ssh 沒有配置授權目錄是不能選擇的喲", "i18n_7dfc7448ec": "真的要刪除倉庫信息麼？", "i18n_7dfcab648d": "產物", "i18n_7e000409bb": "容易出現挖礦情況", "i18n_7e1b283c57": " 添加", "i18n_7e2b40fc86": "選擇節點", "i18n_7e300e89b1": "分發成功", "i18n_7e33f94952": "，如果想要切換路徑後執行命令則需要", "i18n_7e359f4b71": "硬盤總量：", "i18n_7e58312632": "編輯日誌搜索", "i18n_7e866fece6": "請輸入兩步驗證碼", "i18n_7e930b95ef": "發佈文件", "i18n_7e951d56d9": "操作時間", "i18n_7e9f0d2606": "項目是指,節點中的某一個項目,需要提前在節點中創建項目", "i18n_7ef30cfd31": "附加環境變量是指讀取倉庫指定環境變量文件來新增到執行構建運行時", "i18n_7f0abcf48d": "需要到編輯中去為一個節點綁定一個 ssh信息才能啟用該功能", "i18n_7f3809d36b": "構建結束", "i18n_7f5bcd975b": "cpu佔用", "i18n_7f7c624a84": "批量操作", "i18n_7f7ee903da": "發佈隱藏文件", "i18n_7fb5bdb690": "軟件致謝", "i18n_7fb62b3011": "批量刪除", "i18n_7fbc0f9aae": "執行時間開始", "i18n_7fc88aeeda": "修改密碼", "i18n_800dfdd902": "今天", "i18n_80198317ff": "並向您的朋友推薦或分享：", "i18n_8023baf064": "通知狀態", "i18n_80669da961": "CPU佔用", "i18n_807ed6f5a6": "暫無任何數據", "i18n_8086beecb3": "標籤名稱：", "i18n_808c18d2bb": "值為 true 表示項目當前為運行中", "i18n_809b12d6a0": "請耐心等待暫時不用刷新頁面", "i18n_80cfc33cbe": "確認重置", "i18n_81301b6813": "打開終端", "i18n_81485b76d8": "請輸入主機地址", "i18n_814dd5fb7d": "真的要刪除備份信息麼？", "i18n_815492fd8d": "舊版程序包占有空間", "i18n_8160b4be4e": "異常關閉", "i18n_819767ada1": "用户名", "i18n_8198e4461a": "項目:", "i18n_81afd9e713": "隊列等待", "i18n_81c1dff69c": "解決辦法", "i18n_81d7d5cd8a": "命令詳細描述", "i18n_81e4018e9d": "懸空類型", "i18n_82416714a8": "需要測試的端口", "i18n_824607be6b": "保留天數", "i18n_824914133f": "沒有任何腳本庫", "i18n_8283f063d7": "項目完整目錄", "i18n_828efdf4e5": "開啟MFA數", "i18n_82915930eb": "併發執行", "i18n_829706defc": "在線構建（構建關聯倉庫、構建歷史）", "i18n_829abe5a8d": "分組", "i18n_82b89bd049": "日誌彈窗會全屏打開", "i18n_82d2c66f47": "批量分配", "i18n_8306971039": "所屬用户", "i18n_8309cec640": "請選擇節點項目,可能是節點中不存在任何項目,需要去節點中創建項目", "i18n_833249fb92": "當前文件用時", "i18n_8347a927c0": "修改", "i18n_835050418f": "確認要上傳最新的插件包嗎？", "i18n_8351876236": "別名 ", "i18n_83611abd5f": "發佈", "i18n_8363193305": "請輸入回調重定向 url [redirectUri]", "i18n_8388c637f6": "自啟動", "i18n_83aa7f3123": "分發id", "i18n_83c61f7f9e": "請選擇監控用户", "i18n_83ccef50cd": "當目標工作空間已經存在 腳本 時候將自動同步 腳本內容、默認參數、定時執行、描述", "i18n_83f25dbaa0": "綁定節點", "i18n_8400529cfb": "重置自定義的進程名信息", "i18n_8432a98819": "操作功能", "i18n_843f05194a": "顯示所有", "i18n_84415a6bb1": "重置下載 token 信息,重置後之前的下載 token 將失效", "i18n_844296754e": "虛擬內存", "i18n_84592cd99c": "可以理解為項目打包的目錄。 如 Jpom 項目執行（構建命令）", "i18n_84597bf5bc": "阿里雲企業郵箱配置", "i18n_84632d372f": "點擊查看詳情", "i18n_84777ebf8b": "安全提醒", "i18n_847afa1ff2": "請輸入IP授權,多個使用換行,0.0.0.0 是開放所有IP,支持配置IP段 ***********/*************,***********/24", "i18n_848c07af9b": "管理面板", "i18n_848e4e21da": "如：--server", "i18n_8493205602": "開", "i18n_84aa0038cf": "系統日誌", "i18n_84b28944b7": "超時時間(S)", "i18n_84d331a137": "秒 (值太小可能會取不到節點狀態)", "i18n_84e12f7434": "會話已經關閉[ssh-terminal]", "i18n_853d8ab485": "正在構建", "i18n_85451d2eb5": "請輸入變量值", "i18n_8580ad66b0": "真的要徹底刪除項目麼？徹底項目會自動刪除項目相關文件奧(包含項目日誌，日誌備份，項目文件)", "i18n_85be08c99a": "未查詢到任何數據", "i18n_85cfcdd88b": "本地構建是指直接在服務端中的服務器執行構建命令", "i18n_85da2e5bb1": "重啟中，請稍候...", "i18n_85ec12ccd3": "延遲,容器升級間隔時間", "i18n_85f347f9d0": "用户限制用户只能對應的工作空間裏面操作對應的功能", "i18n_85fe5099f6": "集羣", "i18n_86048b4fea": "移除", "i18n_860c00f4f7": "每小時", "i18n_863a95c914": "真的要保存當前配置嗎？如果配置有誤,可能無法啟動服務需要手動還原奧！！！", "i18n_867cc1aac4": " ：範圍：0~23", "i18n_869b506d66": "獨立的項目分發請到分發管理中去修改", "i18n_869ec83e33": "未使用", "i18n_86b7eb5e83": "刪除前需要將關聯數據都刪除後才能刪除當前工作空間？", "i18n_86c1eb397d": "切換賬號", "i18n_86cd8dcead": "啟動時間", "i18n_86e9e4dd58": "倉庫lastcommit", "i18n_86f3ec932c": "讀取大小", "i18n_86fb7b5421": "節點賬號", "i18n_8704e7bdb7": "請輸入令牌 url [accessTokenUri]", "i18n_871cc8602a": "二級目錄", "i18n_8724641ba8": "間隔（/） &gt; 區間（-） &gt; 列表（,）", "i18n_872ad6c96e": "支持引用全局腳本庫（G{'@'}(\"xx\") xx 為腳本標記）", "i18n_8756efb8f4": "真的要刪除當前文件夾麼？", "i18n_87659a4953": "確認要關閉 beta 計劃嗎？", "i18n_8780e6b3d1": "文件管理", "i18n_878aebf9b2": "登錄名稱", "i18n_87d50f8e03": "容器ID", "i18n_87db69bd44": "限制資源", "i18n_87dec8f11e": "錯誤的工作空間數據", "i18n_87e2f5bf75": "追加腳本模板", "i18n_87eb55155a": "行數：", "i18n_8813ff5cf8": "如果是在啟動服務端後安裝並配置的環境變量需要通過終端命令來重啟服務端才能生效", "i18n_883848dd37": "實際內存佔用", "i18n_8844085e15": "凌晨0點", "i18n_884ea031d3": "請輸入變量描述", "i18n_8887e94cb7": "順序執行(有執行失敗將繼續)", "i18n_888df7a89e": "不推薦", "i18n_88ab27cfd0": "分組/標籤:", "i18n_88b4b85562": "打包預發佈環境 npm i && npm run build:stage", "i18n_88b79928e7": "證書丟失", "i18n_88c5680d0d": "管理狀態：", "i18n_88c85a2506": "新增的節點(插件端)將自動", "i18n_88e6615734": "解綁會檢查數據關聯性,同時將自動刪除節點項目和腳本緩存信息,一般用於服務器無法連接且已經確定不再使用", "i18n_88f5c7ac4a": "請選擇排序字段", "i18n_8900539e06": "寫入大小", "i18n_89050136f8": "發佈後操作", "i18n_891db2373b": "自動刷新", "i18n_897d865225": "鏡像數：", "i18n_89944d6ccb": "標籤限制為字母數字且長度 1-10", "i18n_899dbd7b9a": "CPU型號", "i18n_899fe0c5dd": "節點地址為插件端的 IP:PORT 插件端端口默認為：212", "i18n_89a40a1a8b": "分發過程請求,非必填，GET請求", "i18n_89cfb655e0": "容器名標籤", "i18n_89d18c88a3": "請輸入文件任務名", "i18n_89f5ca6928": "支持通配符", "i18n_8a1767a0d2": "開啟此選項後可以正常發佈隱藏文件", "i18n_8a3e316cd7": "不編碼", "i18n_8a414f832f": "集羣地址", "i18n_8a49e2de39": "QQ 郵箱配置", "i18n_8a4dbe88b8": "點擊進入節點管理", "i18n_8a745296f4": "開機時間：", "i18n_8aa25f5fbe": "發佈類型", "i18n_8ae2b9915c": "請填寫第", "i18n_8aebf966b2": "集羣訪問地址", "i18n_8b1512bf3a": "如果端口", "i18n_8b2e274414": "上次重載結果", "i18n_8b3db55fa4": "集羣ID:", "i18n_8b63640eee": "賬號被禁用", "i18n_8b6e758e4c": "懸停到儀表盤上顯示具體含義", "i18n_8b73b025c0": "簡單易用，但不支持密鑰導出備份", "i18n_8b83cd1f29": "要拉取的鏡像名稱", "i18n_8ba971a184": "私人令牌", "i18n_8ba977b4b7": "# 限制備份指定文件後綴（支持正則）", "i18n_8bd3f73502": "節點密碼", "i18n_8be76af198": "163 郵箱配置", "i18n_8be868ba1b": "類 10", "i18n_8c0283435b": "：表示連續區間，例如在分上，表示2,3,4,5,6,7,8分", "i18n_8c24b5e19c": "請使用應用掃碼綁定令牌,然後輸入驗證碼確認綁定才生效", "i18n_8c2da7cce9": "沒有任何證書", "i18n_8c4db236e1": "請輸入腳本標記，標記只能是字母或者數字長度需要小於 20 並且全局唯一", "i18n_8c61c92b4b": "備份類型", "i18n_8c66392870": "需要使用 ssh-keygen -m PEM -t rsa -b 4096 -C", "i18n_8c67370ee5": "如果產物同步到文件中心,當前值會共享", "i18n_8c7c7f3cfa": "服務端腳本", "i18n_8c7ce1da57": "開啟 dockerTag 版本遞增後將在每次構建時自動將版本號最後一位數字同步為構建序號ID, 如：當前構建為第 100 次構建 testtag:1.0 -> testtag:1.100,testtag:1.0.release -> testtag:1.100.release。如果沒有匹配到數字將忽略遞增操作", "i18n_8c7d19b32a": "允許執行的內存節點 (MEM) (0-3, 0,1)。 僅在 NUMA 系統上有效。", "i18n_8cae9cb626": "淺色 idea", "i18n_8ccbbb95a4": "請填寫遠程URL", "i18n_8cd628f495": "所有的IP：", "i18n_8d0fa2ee2d": "請輸入端口號", "i18n_8d1286cd2e": "沒有任何分發日誌", "i18n_8d13037eb7": "狀態消息：", "i18n_8d202b890c": "批量觸發器地址", "i18n_8d3d771ab6": "編輯集羣", "i18n_8d5956ca2a": "以 yaml/yml 格式配置,scriptId 為項目路徑下的腳本文件的相對路徑或者服務端腳本模版ID，可以到服務端腳本模版編輯彈窗中查看 scriptId", "i18n_8d5c1335b6": "容器名稱數字字母,且長度大於1", "i18n_8d62b202d9": "請選擇要使用的文件", "i18n_8d63ef388e": "暫停", "i18n_8d6d47fbed": "#  在指定目錄執行: ./ 項目目錄  /root/ 特定目錄 默認在 {'${jpom_agent_data_path}'}/script_run_cache ", "i18n_8d6f38b4b1": "文件描述", "i18n_8d90b15eaf": "# 宿主機文件上傳到容器 /host:/container:true", "i18n_8d92fb62a7": "請選擇模板節點", "i18n_8d9a071ee2": "導入", "i18n_8da42dd738": "秒級別（默認未開啟秒級別,需要去修改配置文件中:[system.timerMatchSecond]）", "i18n_8dbe0c2ffa": "佔用空間：", "i18n_8dc09ebe97": "獲取", "i18n_8dc8bbbc20": "文件系統", "i18n_8de2137776": "集羣任務", "i18n_8e2ed8ae0d": "【獨立分發】", "i18n_8e331a52de": "只允許訪問的 IP 地址", "i18n_8e34aa1a59": "以此機器節點配置為模板", "i18n_8e389298e4": "導出鏡像", "i18n_8e38d55231": "真的要徹底退出系統麼？徹底退出將退出登錄和清空瀏覽器緩存", "i18n_8e54ddfe24": "啟動", "i18n_8e6184c0d3": "項目可能支持關聯如下數據：", "i18n_8e6a77838a": "請選擇要分發到的機器節點", "i18n_8e872df7da": "注意是整行不能包含空格", "i18n_8e89763d95": "宿主機ip", "i18n_8e8bcfbb4f": "確定要修剪對應的信息嗎？修剪會自動清理對應的數據", "i18n_8e9bd127fb": "需要提前為工作空間配置授權目錄", "i18n_8ea4c3f537": "從尾搜索、文件前100行、文件後100行", "i18n_8ea93ff060": "節點腳本模版是存儲在節點中的命令腳本用於在線管理一些腳本命令，如初始化軟件環境、管理應用程序等", "i18n_8ef0f6c275": "關閉 beta 計劃", "i18n_8f0bab9a5a": "在讀取的日誌文件數", "i18n_8f0c429b46": "遷移操作不具有事務性質，如果流程被中斷或者限制條件不滿足可能產生宂餘數據！！！！", "i18n_8f36f2ede7": "工作空間名稱：", "i18n_8f3747c057": "服務名稱", "i18n_8f40b41e89": "過期時間：", "i18n_8f7a163ee9": "快速安裝插件端", "i18n_8f8f88654f": "暫無節點信息", "i18n_8fb7785809": "如果在生成私鑰的過程中有加密，那麼需要把加密密碼填充到上面的密碼框中", "i18n_8fbcdbc785": "請輸入別名碼", "i18n_8fd9daf8e9": "保證在內網中使用可以忽略 TLS 證書", "i18n_8fda053c83": "寫入次數", "i18n_8ffded102f": "如果需要定時自動構建則填寫,cron 表達式.默認未開啟秒級別,需要去修改配置文件中:[system.timerMatchSecond]）", "i18n_900c70fa5f": "警吿", "i18n_9014d6d289": "備份列表", "i18n_90154854b6": "請輸入host", "i18n_901de97cdb": "方式請求接口參數傳入到請求體 ContentType 請使用：text/plain", "i18n_903b25f64e": "未知狀態", "i18n_904615588b": "文件類型沒有控制枱功能", "i18n_9057ac9664": "請選擇觸發類型", "i18n_9065a208e8": "通過 URL 下載遠程文件到項目文件夾,需要到對應的工作空間下授權目錄配置中配置允許的 HOST 授權", "i18n_906f6102a7": "重啟成功", "i18n_9086111cff": "關聯工作空間 docker", "i18n_90b5a467c1": "刷新目錄", "i18n_90c0458a4c": "導入備份", "i18n_90eac06e61": "宿主機目錄", "i18n_912302cb02": "瀏覽器", "i18n_9136e1859a": "Docker鏡像", "i18n_913ef5d129": "執行重啟", "i18n_916cde39c4": "所有參數將拼接成字符串以空格分隔形式執行腳本,需要注意參數順序和未填寫值的參數將自動忽略", "i18n_916ff9eddd": "請輸入暱稱", "i18n_917381e4a5": "當前下載源：", "i18n_91985e3574": "自動探測", "i18n_91a10b8776": " 腳本庫 ", "i18n_920f05031b": "狀態描述", "i18n_922b76febd": "運行模式必填", "i18n_923f8d2688": "發佈後命令", "i18n_9255f9c68f": "會話已經關閉[tail-file]", "i18n_92636e8c8f": "跳過", "i18n_9282b1e5da": "企業微信掃碼", "i18n_929e857766": "證書類型", "i18n_92c6aa6db9": "如果您 SSH 機器中存在 docker 但是系統沒有監測到，您需要到【配置管理】-", "i18n_92dde4c02b": "廣吿投放", "i18n_92e3a830ae": "幫助", "i18n_92f0744426": "容器構建是指使用 docker 容器執行構建,這樣可以達到和宿主機環境隔離不用安裝依賴環境", "i18n_92f3fdb65f": "倉庫：", "i18n_92f9a3c474": "切換語言後頁面將自動刷新", "i18n_9300692fac": "標記引用", "i18n_9302bc7838": "請輸入要檢查的端口", "i18n_930882bb0a": "個", "i18n_9308f22bf6": "單個觸發器地址中：第一個隨機字符串為腳本ID，第二個隨機字符串為 token", "i18n_930fdcdf90": "配置名 （如：size）", "i18n_9324290bfe": "如：key1", "i18n_932b4b7f79": "注意：在每一個子表達式中優先級：", "i18n_934156d92c": "創建分發項目", "i18n_9341881037": "確定要取批量構建嗎？注意：同時運行多個構建將佔用較大的資源,請慎重使用批量構建,如果批量構建的數量超多構建任務隊列等待數，構建任務將自動取消", "i18n_935b06789f": "您還未執行操作", "i18n_9362e6ddf8": "危險操作！！！", "i18n_938246ce8b": "任務模板", "i18n_938dd62952": "執行路徑", "i18n_939d5345ad": "提交", "i18n_93e1df604a": "機器分組", "i18n_93e894325d": "批量啟動", "i18n_9402665a2c": " 持續搜索（對話框不會自動關閉，按 Enter 查找下一個，按 Shift-Enter 查找上一個）", "i18n_9412eb8f99": "請填寫平台地址", "i18n_9443399e7d": " ，範圍1970~2099，但是第7位不做解析，也不做匹配", "i18n_94763baf5f": "可以到節點管理中的【插件端配置】=>【授權配置】修改", "i18n_947d983961": "温馨提示", "i18n_948171025e": "會話已經關閉[docker-log]", "i18n_949934d97c": "超大", "i18n_949a8b7bd2": "列設置", "i18n_94aa195397": "證書文件", "i18n_94ca71ae7b": "請選擇要使用的證書", "i18n_94d4fcca1b": "創建賬號", "i18n_952232ca52": "構建歷史可能佔有較多硬盤空間,建議根據實際情況配置保留個數", "i18n_953357d914": "忽略校驗 state", "i18n_953ec2172b": "未重啟成功：", "i18n_954fb7fa21": "真的要刪除項目麼？刪除項目不會刪除項目相關文件奧,建議先清理項目相關文件再刪除項目", "i18n_956ab8a9f7": "配置文件嗎？配置文件一旦創建不能通過管理頁面刪除的奧？", "i18n_957c1b1c50": "集羣節點", "i18n_95a43eaa59": "創建人", "i18n_95b351c862": "編輯", "i18n_95c5c939e4": "可選擇的列表和項目授權目錄是一致的，即相同配置", "i18n_95dbee0207": "遠程下載安全HOST", "i18n_96283fc523": "備份文件不存在", "i18n_964d939a96": " 長名稱：", "i18n_969098605e": "環境變量是指配置在系統中的一些固定參數值，用於腳本執行時候快速引用。", "i18n_96972aa0df": "# 是否開啟差異備份（僅備份變動過的文件）", "i18n_96b78bfb6a": "請勿手動刪除數據目錄下面文件 !!!!", "i18n_96c1c8f4ee": "灰綠 abcdef", "i18n_96c28c4f17": "加入到哪個集羣", "i18n_96d46bd22e": "手動刷新統計", "i18n_96e6f43118": "容器 runtime", "i18n_974be6600d": "密碼必須包含數字，字母，字符，且大於6位", "i18n_977bfe8508": "標籤(TAG)", "i18n_979b7d10b0": "構建中斷", "i18n_97a19328a8": "立即開啟", "i18n_97cb3c4b2e": "工作空間環境變量用於構建命令相關", "i18n_97d08b02e7": "網絡端口測試", "i18n_97ecc1bbe9": "輸出流量", "i18n_981cbe312b": "至", "i18n_9829e60a29": "實時版本號", "i18n_98357846a2": "表格視圖才能使用批量操作功能", "i18n_983f59c9d4": "驗證碼", "i18n_9878af9db5": "請到【系統管理】-> 【資產管理】-> 【Docker管理】新增Docker並創建集羣，或者將已存在的的 Docker 集羣授權關聯、分配到此工作空間", "i18n_9880bd3ba1": "此工具用於檢查 cron 表達式是否正確,以及計劃運行時間", "i18n_989f1f2b61": "真的要重啟項目麼？", "i18n_98a315c0fc": "授權", "i18n_98cd2bdc03": "表格視圖才能使用同步配置功能", "i18n_98d69f8b62": "工作空間", "i18n_98e115d868": "運行中的定時任務", "i18n_9914219dd1": "從頭搜索", "i18n_9932551cd5": "內存", "i18n_993a5c7eee": "#配置説明：https://docs.docker.com/engine/api/v1.43/#tag/Container/operation/ContainerCreate", "i18n_99593f7623": "客户端ID", "i18n_9964d6ed3f": "掛載", "i18n_996dc32a98": "系統類型", "i18n_9970ad0746": "主題", "i18n_9971192b6a": "。如：http", "i18n_9973159a4d": "匹配一個字符", "i18n_998b7c48a8": "查看容器", "i18n_99b3c97515": "小時級別", "i18n_99cba05f94": "真的要刪除 SSH 麼？當前 ssh 關聯的腳本在刪除後均將失效", "i18n_99d3e5c718": " 開始搜索", "i18n_99f0996c0a": "請選擇日誌目錄", "i18n_9a00e13160": "單個觸發器地址中：第一個隨機字符串為項目ID(服務端)，第二個隨機字符串為 token", "i18n_9a0c5b150c": "編輯 命令", "i18n_9a2ee7044f": "變量值", "i18n_9a436e2a53": "修剪具有指定標籤的對象,多個使用逗號分隔", "i18n_9a4b872895": "集羣操作", "i18n_9a56bb830e": "用户暱稱", "i18n_9a77f3523e": "鏡像 tag", "i18n_9a7b52fc86": "所有", "i18n_9a8eb63daf": "配置工作空間權限", "i18n_9ab433e930": "其他配置", "i18n_9ac4765895": "一個整數值，表示此容器相對於其他容器的相對 CPU 權重。", "i18n_9adf43e181": "正在構建數", "i18n_9ae40638d2": "部署證書", "i18n_9af372557e": "服務端端口", "i18n_9aff624153": "監控", "i18n_9b0bc05511": "在文件第 1 - 100 行中搜索", "i18n_9b1c5264a0": "上傳後", "i18n_9b280a6d2d": "節點:", "i18n_9b3e947cc9": "節點狀態：", "i18n_9b5f172ebe": "綁定集羣", "i18n_9b7419bc10": "QQ郵箱", "i18n_9b74c734e5": "節點賬號密碼為插件端的賬號密碼,並非用户賬號(管理員)密碼", "i18n_9b78491b25": "請輸入授權路徑，回車支持輸入多個路徑，系統會自動過濾 ../ 路徑、不允許輸入根路徑", "i18n_9b7ada2613": " ：範圍：1~31，", "i18n_9b9e426d16": "在讀取的日誌文件數：", "i18n_9ba71275d3": ",請耐心等待暫時不用刷新頁面,升級成功後會自動刷新", "i18n_9baca0054e": "修改人", "i18n_9bbb6b5b75": "真的要刪除日誌搜索麼？", "i18n_9bd451c4e9": "節點已經存在", "i18n_9be8ff8367": "支持變量替換：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_RESULT_FILE}'}、{'${BUILD_NUMBER_ID}'}", "i18n_9bf4e3c9de": "創建分發項目是指,全新創建一個屬於節點分發到項目,創建成功後項目信息將自動同步到對應的節點中,修改節點分發信息也自動同步到對應的節點中", "i18n_9bf5aa6672": "web socket 錯誤,請檢查是否開啟 ws 代理", "i18n_9c19a424dc": "請輸入原密碼", "i18n_9c2a917905": "搜索命令", "i18n_9c2f1d3f39": "內的root擁有真正的root權限。", "i18n_9c3a3e1b03": "父級不存在自動刪除", "i18n_9c3a5e1dad": "請到【系統管理】-> 【資產管理】-> 【機器管理】新增節點，或者將已新增的機器授權關聯、分配到此工作空間", "i18n_9c3c05d91b": "節點地址建議使用內網地址", "i18n_9c55e8e0f3": "允許執行的 CPU（例如，0-3、0,1）。", "i18n_9c66f7b345": "真的要刪除機器麼？刪除會檢查數據關聯性", "i18n_9c84cd926b": "新機器還需要綁定工作空間，因為我們建議將不同集羣資源分配到不同的工作空間來管理", "i18n_9c942ea972": "證書生成方式可以參考文檔）來連接 docker 提升安全性", "i18n_9c99e8bec9": "不填寫則發佈至項目的根目錄", "i18n_9cac799f2f": "選擇分組名", "i18n_9caecd931b": "字段", "i18n_9cd0554305": "如果不需要保留較多構建歷史信息可以到服務端修改構建相關配置參數", "i18n_9ce5d5202a": "運行的Jar包：", "i18n_9d577fe51b": "文件來源", "i18n_9d5b1303e0": "創建集羣會將嘗試獲取 docker 中集羣信息，如果存在集羣信息將自動同步集羣信息到系統，反之不存在集羣信息將自動創建 swarm 集羣", "i18n_9d7d471b77": "請選擇節點角色", "i18n_9d89cbf245": "分發名稱", "i18n_9dd62c9fa8": "終端命令無限制", "i18n_9ddaa182bd": "插件端時間：", "i18n_9de72a79fe": "查看文件", "i18n_9e09315960": "重建", "i18n_9e0c797c04": "選擇監控使用的語言", "i18n_9e2e02ef08": "分發類型", "i18n_9e4ae8a24f": "釘釘掃碼", "i18n_9e560a4162": "方式生成的 SSH key", "i18n_9e5ffa068e": "基本信息", "i18n_9e6b699597": "nanoCPUs 最小 1000000", "i18n_9e78f02aad": "參數描述,參數描述沒有實際作用,僅是用於提示參數的含義", "i18n_9e96d9c8d3": "系統負載:", "i18n_9e98fa5c0d": "文件名欄支持右鍵菜單", "i18n_9ec961d8cb": "選擇構建產物", "i18n_9ee0deb3c8": "網絡開了小差！請重試...:", "i18n_9ee9d48699": "創建後不支持修改", "i18n_9f01272a10": " 法律風險", "i18n_9f0de3800b": "請填寫倉庫名稱", "i18n_9f4a0d67c6": "文件名建議不要出現空白、製表、其他空白、\\、/、：、*、？、\"、<、>、\\、{'|'} 等特殊字符", "i18n_9f52492fbc": "配置詳情請參考配置示例", "i18n_9f6090c819": "傳入參數有：buildId、buildName、type、statusMsg、triggerTime", "i18n_9f6fa346d8": "請輸入 SSH 名稱", "i18n_9f70e40e04": "運行時間", "i18n_9fb12a2d14": "發佈後執行的命令(非阻塞命令),一般是啟動項目命令 如：ps -aux {'|'} grep java, 支持變量替換：{'${ BUILD_ID }'}、{'${ BUILD_NAME }'}、{'${ BUILD_RESULT_FILE }'}、{'${ BUILD_NUMBER_ID }'} ", "i18n_9fb61a9936": "版本遞增", "i18n_9fc2e26bfa": "請選擇項目", "i18n_9fca7c455f": "登錄時間", "i18n_9febf31146": "請選擇文件", "i18n_9ff5504901": "傳入環境變量有：buildId、buildName、type、statusMsg、triggerTime、buildNumberId、buildSourceFile", "i18n_a001a226fd": "更新時間", "i18n_a03c00714f": "批量關閉", "i18n_a03ea1e864": "請選擇分發到的節點", "i18n_a04b7a8f5d": "單個觸發器請求支持將參數解析為環境變量傳入腳本執行，比如傳入參數名為 abc=efg 在腳本中引入則為：{'${trigger_abc}'}", "i18n_a050cbc36d": "您的瀏覽器版本太低，不支持該功能", "i18n_a056d9c4b3": "選擇腳本", "i18n_a05c1667ca": "構建歷史", "i18n_a08cbeb238": "並且配置正確的環境變量", "i18n_a09375d96c": "懸空", "i18n_a093ae6a6e": "自動續期", "i18n_a0a111cbbd": "分發項目-", "i18n_a0a3d583b9": "總內存：", "i18n_a0b9b4e048": "請輸入客户端ID [clientId]", "i18n_a0d0ebc519": "全局代理", "i18n_a0e31d89ff": "一般建議 10 秒以上", "i18n_a0f1bfad78": "數據目錄大小包含：臨時文件、在線構建文件、數據庫文件等", "i18n_a11cc7a65b": "請輸入內容", "i18n_a13d8ade6a": "關聯節點數據是異步獲取有一定時間延遲", "i18n_a14da34559": "資源監控異常", "i18n_a156349591": " 查看", "i18n_a1638e78e8": "匹配包含 a 或者 b 的行", "i18n_a17450a5ff": "選擇壓縮文件", "i18n_a17b5ab021": "當前文件已經存在啦", "i18n_a17b905126": "關鍵詞時自動識別為隱私變量", "i18n_a17bc8d947": "控制枱日誌只是啟動項目輸出的日誌信息,並非項目日誌。可以關閉控制枱日誌備份功能：", "i18n_a189314b9e": "不發佈", "i18n_a1a3a7d853": "現成生成", "i18n_a1b745fba0": "請輸入備份名稱", "i18n_a1bd9760fc": "定時任務", "i18n_a1c4a75c2d": "是一款開源軟件您使用這個項目並感覺良好，或是想支持我們繼續開發，您可以通過如下方式支持我們：", "i18n_a1da57ab69": "支付寶轉賬", "i18n_a1e24fe1f6": "用户時間", "i18n_a1f58b7189": "參數{count}值", "i18n_a1fb7f1606": "腳本管理", "i18n_a20341341b": "顯示前N行", "i18n_a24d80c8fa": "項目啟動,停止,重啟,文件變動都將請求對應的地址", "i18n_a25657422b": "變量名", "i18n_a2741f6eb3": "系統環境變量中變量名包含：", "i18n_a2a0f52afe": "不填將使用默認的 $HOME/.ssh 目錄中的配置,使用優先級是：id_dsa>id_rsa>identity", "i18n_a2ae15f8a7": "構建流程", "i18n_a2e62165dc": "真的要保存當前配置嗎？IP 授權請慎重配置奧( 授權是指只允許訪問的 IP ),配置後立馬生效 如果配置錯誤將出現無法訪問的情況,需要手動恢復奧！！！", "i18n_a2ebd000e4": "不做任何操作", "i18n_a3296ef4f6": "全屏終端", "i18n_a33a2a4a90": "服務端同步的腳本不能在此修改", "i18n_a34545bd16": "構建參數,如：key1=values1&keyvalue2 使用 URL 編碼", "i18n_a34b91cdd7": "環境變量還可以用於倉庫賬號密碼、ssh密碼引用", "i18n_a34c24719b": "開始執行任務", "i18n_a35740ae41": "操作提示", "i18n_a3751dc408": " ：12點的每分鐘執行", "i18n_a37c573d7b": "可以是 Unix 時間戳、日期格式的時間戳或 Go 持續時間字符串（例如 10m、1h30m），相對於守護進程機器的時間計算。", "i18n_a38ed189a2": "上傳更新前請閲讀更新日誌裏面的説明和注意事項並且", "i18n_a39340ec59": "禁止命令", "i18n_a396da3e22": "當前工作空間還沒有項目並且也沒有任何節點", "i18n_a3d0154996": "文件狀態", "i18n_a3f1390bf1": "修改後如果有原始關聯數據將失效，需要重新配置關聯", "i18n_a4006e5c1e": "創建備份", "i18n_a421ec6187": "編輯環境變量", "i18n_a4266aea79": "真的要刪除此服務麼？", "i18n_a436c94494": "飛書掃碼", "i18n_a472019766": "節點Id", "i18n_a497562c8e": "執行人", "i18n_a4f5cae8d2": "開啟狀態", "i18n_a4f629041c": "路徑需要配置絕對路徑", "i18n_a50fbc5a52": "支持指定網卡名稱來綁定：", "i18n_a51cd0898f": "容器名稱", "i18n_a51d8375b7": "選擇靜態文件", "i18n_a52a10123f": "如果升級失敗需要手動恢復奧", "i18n_a52aa984cd": "真的要刪除權限組麼？", "i18n_a53d137403": "會話已經關閉[free-script]", "i18n_a55ae13421": "請配置下載授權碼", "i18n_a5617f0369": "SSH連接信息", "i18n_a577822cdd": "保存並構建", "i18n_a59d075d85": "自定義克隆深度，避免大倉庫全部克隆", "i18n_a5d1c511d7": "模板名稱", "i18n_a5d550f258": "間隔時間", "i18n_a5daa9be44": "上傳前請檢查包是否完整,否則可能出現更新後無法正常啟動的情況！！", "i18n_a5e9874a96": "請選擇發佈到哪個 docker 集羣", "i18n_a5f84fd99c": "非隱私", "i18n_a6269ede6c": "管理節點", "i18n_a62fa322b4": "證書將打包成 zip 文件上傳到對應的文件夾", "i18n_a637a42173": "選擇的構建歷史產物已經不存在啦", "i18n_a63fe7b615": "分配到工作空間後還需要到關聯中進行配置對應工作空間才能完美使用奧", "i18n_a657f46f5b": "周", "i18n_a66644ff47": "類 172", "i18n_a66fff7541": "遠程下載URL", "i18n_a6bf763ede": "機器節點", "i18n_a6fc9e3ae6": "上傳文件", "i18n_a74b62f4bb": "硬盤信息", "i18n_a75a5a9525": "發佈目錄,構建產物上傳到對應目錄", "i18n_a75b96584d": "服務Id", "i18n_a75f781415": "服務端菜單", "i18n_a7699ba731": "上傳成功", "i18n_a76b4f5000": "管理員數", "i18n_a77cc03013": "如果引用腳本庫需要提前將對應腳本分發到機器節點中才能正常使用", "i18n_a795fa52cd": "徹底退出", "i18n_a7a9a2156a": "請輸入確認密碼", "i18n_a7c8eea801": "嘗試自動續簽成功", "i18n_a7ddb00197": "阿里雲企業郵箱 SSL", "i18n_a805615d15": "type 的值有：startReady、pull、executeCommand、release、done、stop、success、error", "i18n_a810520460": "密碼", "i18n_a823cfa70c": "容器標籤", "i18n_a84a45b352": "升級策略", "i18n_a8754e3e90": "填寫正確的IP地址", "i18n_a87818b04f": "等待開始", "i18n_a8920fbfad": "命令路徑請修改為您的服務器中的實際路徑", "i18n_a89646d060": "創建工作空間後還需要在對應工作空間中分別管理對應數據", "i18n_a8f44c3188": "賬號是系統特定演示使用的賬號", "i18n_a90cf0796b": "信息：", "i18n_a912a83e6f": "插件版本", "i18n_a918bde61d": "您還未構建", "i18n_a91ce167c1": "文件id", "i18n_a9463d0f1a": "搜索模式,默認查看文件最後多少行，從頭搜索指從指定行往下搜索，從尾搜索指從文件尾往上搜索多少行", "i18n_a94feac256": "載速度根據網速來確定,如果網絡不佳下載會較慢", "i18n_a952ba273f": "聯繫時請備註來意", "i18n_a9795c06c8": "沒有SSH", "i18n_a98233b321": "使用微軟全家桶的推薦", "i18n_a9886f95b6": "確定要刪除此腳本庫嗎？", "i18n_a9add9b059": "數據存儲目錄：", "i18n_a9b50d245b": "不綁定", "i18n_a9c52ffd40": "嚴格執行：", "i18n_a9c999e0bd": "建議在上傳後的腳本中對文件進行自定義更名，SSH 上傳默認為：{'${FILE_ID}'}.{'${FILE_EXT_NAME}'}", "i18n_a9de52acb0": "操作方法", "i18n_a9eed33cfb": "如果版本相差大需要重新初始化數據來保證和當前程序裏面字段一致", "i18n_a9f94dcd57": "部署", "i18n_aa53a4b93a": "沒有任何網絡接口信息", "i18n_aa9236568f": "統計趨勢", "i18n_aabdc3b7c0": "項目路徑", "i18n_aac62bc255": "點擊查看日誌", "i18n_aacd9caa4a": "查看環境變量", "i18n_aad7450231": "請輸入選擇綁定的集羣", "i18n_aadf9d7028": "用於下載遠程文件來進行節點分發和文件上傳", "i18n_aaeb54633e": "重載", "i18n_ab006f89e7": "手動刪除", "i18n_ab13dd3381": "自建 Gitlab 賬號登錄", "i18n_ab3615a5ad": "下載安裝包", "i18n_ab3725d06b": "會話已經關閉", "i18n_ab7f78ba4c": "空間ID(全匹配)", "i18n_ab968d842f": "構建鏡像嘗試去更新基礎鏡像的新版本", "i18n_ab9a0ee5bd": "文件夾路徑 需要在倉庫裏面 dockerfile", "i18n_ab9c827798": "沒有docker集羣", "i18n_abb6b7260b": "如果多選 ssh 下面目錄只顯示選項中的第一項，但是授權目錄需要保證每項都配置對應目錄", "i18n_abba4043d8": "從頭搜索、文件前20行、文件後3行", "i18n_abba4775e1": "命令參數", "i18n_abd9ee868a": "網絡模式：bridge、container:<name{'|'}id>、host、container、none", "i18n_abdd7ea830": "請輸入新密碼", "i18n_abee751418": "容器Id: ", "i18n_ac00774608": "第", "i18n_ac0158db83": "任務id", "i18n_ac2f4259f1": "新版本：", "i18n_ac408e4b03": "請選擇證書類型", "i18n_ac5f3bfa5b": "選擇要監控的項目,file 類型項目不可以監控", "i18n_ac762710a5": "支持自定義排序字段：sort", "i18n_ac783bca36": "真的要退出並切換賬號登錄麼？", "i18n_acb4ce3592": "請選擇靜態文件中的文件", "i18n_acd5cb847a": "失敗", "i18n_ace71047a0": "請到【系統管理】-> 【資產管理】-> 【SSH管理】新增SSH，或者將已新增的SSH授權關聯、分配到此工作空間", "i18n_acf14aad3c": "不用挨個配置。配置後會覆蓋之前的配置", "i18n_ad209825b5": "請選擇修剪類型", "i18n_ad311f3211": "請選擇倉庫", "i18n_ad35f58fb3": "佔用空間", "i18n_ad4b4a5b3b": "宿主", "i18n_ad780debbc": "回滾策略", "i18n_ad8b626496": "真的要刪除構建歷史記錄麼？", "i18n_ad9788b17d": "異常恢復", "i18n_ad9a677940": "指定 settings 文件打包 mvn -s xxx/settings.xml clean package", "i18n_adaf94c06b": "執行結果", "i18n_adbec9b14d": "創建備份信息", "i18n_adcd1dd701": "返回列表", "i18n_add91bb395": "邏輯節點", "i18n_ade63665b2": "文件合併中", "i18n_ae0d608495": "是否使用MFA", "i18n_ae0fd9b9d2": "備份時間", "i18n_ae12edc5bf": "點擊複製文件路徑", "i18n_ae17005c0c": "未加入", "i18n_ae35be7986": "token,全匹配", "i18n_ae653ec180": "詳細描述", "i18n_ae6838c0e6": "節點分發", "i18n_ae809e0295": "後綴,精準搜索", "i18n_aeade8e979": "未初始化", "i18n_aeb44d34e6": "一次性捐款贊助", "i18n_aec7b550e2": "刪除工作空間確認", "i18n_aed1dfbc31": "中", "i18n_aef1a0752a": "注意：配置項目日誌編碼格式後項目的“日誌搜索”功能讀取日誌文件時也將跟隨此編碼格式讀取日誌文件", "i18n_aefd8f9f27": "請選擇還原方式", "i18n_af013dd9dc": "重啟成功後會自動刷新", "i18n_af0df2e295": "需要到 ssh 信息中配置允許編輯的文件後綴", "i18n_af14cd6893": "請填寫構建 DSL 配置內容,可以點擊上方切換 tab 查看配置示例", "i18n_af3a9b6303": "企業微信掃碼賬號登錄", "i18n_af427d2541": "數據更新時間", "i18n_af4d18402a": "已經斷開連接啦", "i18n_af51211a73": "頁面內容會出現滾動條", "i18n_af708b659f": "內存:", "i18n_af7c96d2b9": "同步機制採用容器 host 確定是同一個服務器（docker）", "i18n_af83388834": "觸發備份 ", "i18n_af924a1a14": "下載異常", "i18n_af98c31607": "物理節點項目數量：", "i18n_afa8980495": "請輸入允許編輯文件的後綴及文件編碼，不設置編碼則默認取系統編碼，示例：設置編碼：txt{'@'}utf-8， 不設置編碼：txt", "i18n_afb9fe400b": "使用率：", "i18n_b04070fe42": "選擇代理類型", "i18n_b04209e785": "關聯數據：", "i18n_b05345caad": "所有者", "i18n_b07a33c3a8": "請選擇分發節點", "i18n_b095ceda99": "選擇的腳本需要將監聽的事件名配置到腳本描述中才能生效奧", "i18n_b0b9df58fd": "SSH節點", "i18n_b0fa44acbb": "佔用率：", "i18n_b10b082c25": "的值有：stop、beforeStop、start、beforeRestart", "i18n_b1192f8f8e": "真的取消當前分發嗎？", "i18n_b11b0c93fa": "如果在 Linux 中實際運行內存可能和您直接使用 free -h 命令查詢到 free 和 total 字段計算出數值相差過大那麼此時就是您當前服務器中的交換內存引起的", "i18n_b12d003367": "隱私字段", "i18n_b153126fc2": "請輸入工作空間名稱", "i18n_b15689296a": "風險提醒", "i18n_b15d91274e": "關閉", "i18n_b166a66d67": "確定要將此數上移嗎？", "i18n_b17299f3fb": "插件端進程ID：", "i18n_b1785ef01e": "節點名稱", "i18n_b186c667dc": "分發過程請求對應的地址,開始分發,分發完成,分發失敗,取消分發", "i18n_b188393ea7": "發佈的SSH", "i18n_b1a09cee8e": "清空還原", "i18n_b1dae9bc5c": "管理員", "i18n_b28836fe97": "分發 ID 等同於項目 ID", "i18n_b28c17d2a6": " (MEM) (0-3, 0,1)。 僅在 NUMA 系統上有效。", "i18n_b29fd18c93": "請選擇指定發佈的項目", "i18n_b2f296d76a": "5分鐘", "i18n_b30d07c036": "批量關閉啟動", "i18n_b328609814": "管理員擁有：管理服務端的部分權限", "i18n_b339aa8710": "表格", "i18n_b33c7279b3": "認證方式", "i18n_b3401c3657": "容器目錄", "i18n_b341f9a861": "任務時間", "i18n_b343663a14": "清空發佈是指在上傳新文件前,會將項目文件夾目錄裏面的所有文件先刪除後再保存新文件", "i18n_b36e87fe5b": "不執行，但是編譯測試用例 mvn clean package -DskipTests", "i18n_b37b786351": "分組名", "i18n_b384470769": "同步緩存", "i18n_b38d6077d6": "登錄IP", "i18n_b38d7db9b0": "下載構建日誌,如果按鈕不可用表示日誌文件不存在,一般是構建歷史相關文件被刪除", "i18n_b3913b9bb7": "請輸入構建環境變量：xx=abc 多個變量回車換行即可", "i18n_b399058f25": "強大安全的密碼管理付費應用", "i18n_b39909964f": "請輸入郵箱賬號", "i18n_b3b1f709d4": "剔除", "i18n_b3bda9bf9e": "請選擇工作空間", "i18n_b3ef35a359": "源倉庫", "i18n_b3f9beb536": "：3~18分，每5分鐘執行一次，即0:03, 0:08, 0:13, 0:18, 1:03, 1:08……", "i18n_b3fe677b5f": "失敗率", "i18n_b408105d69": "密碼字段和密鑰字段在編輯的時候不會返回，如果需要重置或者清空就請點我", "i18n_b437a4d41d": "也支持 URL 參數格式：test_par=123abc&test_par2=abc21", "i18n_b44479d4b8": "可用標籤", "i18n_b4750210ef": "集羣修改時間：", "i18n_b499798ec5": "禁用分發節點", "i18n_b4a8c78284": "選擇工作空間", "i18n_b4c83b0b56": "倉庫賬號", "i18n_b4dd6aefde": "會話已經關閉[script-console]", "i18n_b4e2b132cf": "插件端運行端口默認使用：", "i18n_b4fc1ac02c": "取消構建", "i18n_b4fd7afd31": "個性配置", "i18n_b513f53eb4": "超時時間 單位秒", "i18n_b515d55aab": "可以通過證書管理中提前上傳或者點擊後面選擇證書去選擇/導入證書", "i18n_b53dedd3e0": "發佈前執行的命令(非阻塞命令),一般是關閉項目命令", "i18n_b55f286cba": "載前請閲讀更新日誌裏面的説明和注意事項並且", "i18n_b56585aa18": "配置後可以控制想要在某個時間段禁止用户操作某些功能，優先判斷禁用時段", "i18n_b57647c5aa": "真的要解綁腳本關聯的節點麼？", "i18n_b57ecea951": "已經運行時間：", "i18n_b5a1e1f2d1": "觸發類型：", "i18n_b5a6a07e48": "週二", "i18n_b5b51ff786": "上傳 SQL 文件", "i18n_b5c291805e": "初始化系統", "i18n_b5c3770699": "控制枱", "i18n_b5c5078a5d": "所有的IPV4列表", "i18n_b5ce5efa6e": "集羣服務", "i18n_b5d0091ae3": "構建ID", "i18n_b5d2cf4a76": "當目標工作空間已經存在 腳本 時候將自動同步 腳本內容、默認參數、自動執行、描述", "i18n_b5fdd886b6": "全屏查看日誌", "i18n_b60352bc4f": "虛擬", "i18n_b6076a055f": "登錄失敗", "i18n_b61a7e3ace": "腳本名稱：", "i18n_b63c057330": "真的要刪除操作監控麼？", "i18n_b650acd50b": "恢復默認名稱", "i18n_b6728e74a4": "運行目錄：", "i18n_b6a828205d": "緩存構建", "i18n_b6afcf9851": "禁止命令是不允許在終端執行的命令，多個逗號隔開。(超級管理員沒有任何限制)", "i18n_b6c9619081": "端口：", "i18n_b6e8fb4106": "平台登錄", "i18n_b6ee682dac": "插件數：", "i18n_b714160f52": "分發項目 ID", "i18n_b71a7e6aab": "本地命令", "i18n_b7579706a3": "校驗", "i18n_b7c139ed75": "如果項目目錄較大或者涉及到深目錄，建議關閉掃描避免獲取項目目錄掃描過長影響性能", "i18n_b7cfa07d78": "確認綁定", "i18n_b7df1586a9": "當目標工作空間已經存在節點時候將自動同步 docker 倉庫配置信息", "i18n_b7ea5e506c": "系統信息", "i18n_b7ec1d09c4": "服務ID", "i18n_b7f770d80b": "需要先安裝依賴 npm i && npm run build", "i18n_b8545de30e": "請至少選擇 1 個節點", "i18n_b85b213579": "發件人名稱", "i18n_b86224e030": "節點狀態", "i18n_b87c9acca3": "真的要強制退出集羣嗎？", "i18n_b8915a4933": "真的關閉當前用户的兩步驗證麼？", "i18n_b8ac664d98": "勾選數據表", "i18n_b90a30dd20": "此處不填不會修改密碼", "i18n_b91961bf0b": "傳入參數有：projectId、projectName、type、result", "i18n_b922323119": "鏡像標籤,如：key1=value1&key2=value2", "i18n_b939d47e23": "公鑰", "i18n_b953d1a8f1": "不能關閉了", "i18n_b96b07e2bb": "僅修剪未使用和未標記的鏡像", "i18n_b9a4098131": "觸發器地址", "i18n_b9af769752": "鏡像名稱必填", "i18n_b9b176e37a": "請選擇腳本", "i18n_b9bcb4d623": "插件：", "i18n_b9c1616fd5": "SSH方式連接的 docker 不建議用於容器構建（SSH 方式用於構建非常不穩定）", "i18n_b9c4cf7483": " 全部替換", "i18n_b9c52d9a85": "文件名：", "i18n_ba17b17ba2": "沒有任何SSH腳本命令", "i18n_ba1f68b5dd": "這樣使得", "i18n_ba20f0444c": "強制刪除", "i18n_ba311d8a6a": "腳本", "i18n_ba3a679655": "以 yaml/yml 格式配置", "i18n_ba452d57f2": "使用率最大的分區：", "i18n_ba52103711": "剩餘 inode 數", "i18n_ba619a0942": "默認構建錯誤將自動忽略隱藏文件", "i18n_ba6e91fa9e": "權限", "i18n_ba6ea3d480": "頁面全屏，高度 100%。局部區域可以滾動", "i18n_ba8d1dca4a": "注意：", "i18n_baafe06808": "安全組規則", "i18n_bab17dc6b1": "選擇進程名", "i18n_baef58c283": "請輸入標籤名 字母數字 長度 1-10", "i18n_baefd3db91": "授權可以直接訪問的目錄，多個回車換行即可", "i18n_bb316d9acd": "下載速度根據網速來確定,如果網絡不佳下載會較慢", "i18n_bb4409015b": "機器 ssh 名", "i18n_bb4740c7a7": "執行 命令", "i18n_bb5aac6004": "構建產物同步到文件中心保留天數", "i18n_bb667fdb2a": "未報警", "i18n_bb7eeae618": "僅統計：", "i18n_bb8d265c7e": "版本需要大於 18", "i18n_bb9a581f48": "登錄成功,需要驗證 MFA", "i18n_bb9ef827bf": "禁止訪問", "i18n_bba360b084": "真的要刪除對應的觸發器嗎？", "i18n_bbbaeb32fc": "機器延遲", "i18n_bbcaac136c": "表中的錯誤數據嗎？", "i18n_bbd63a893c": "自動檢測服務端所在服務器中是否存在 docker，如果存在將自動新增到列表中", "i18n_bbf2775521": "鏡像名稱", "i18n_bc2c23b5d2": "修剪操作會刪除相關數據，請謹慎操作。請您再確認本操作後果後再使用", "i18n_bc2f1beb44": "真的要解鎖用户麼？", "i18n_bc4b0fd88a": "網絡 Reachable 測試", "i18n_bc8752e529": "分發項目", "i18n_bcaf69a038": "請選擇一個節點", "i18n_bcc4f9e5ca": "如", "i18n_bcf48bf7a8": "授權 url", "i18n_bcf83722c4": "變量描述", "i18n_bd0362bed3": "新增分組", "i18n_bd49bc196c": "編輯項目", "i18n_bd4e9d0ee2": "原始名：", "i18n_bd5d9b3e93": "使用哪個 docker 構建,填寫 docker 標籤（ 標籤在 docker 編輯頁面配置） 默認查詢可用的第一個,如果tag 查詢出多個將依次構建", "i18n_bd6c436195": "請輸入腳本描述", "i18n_bd7043cae3": "遠程下載", "i18n_bd7c7abc8c": "分組為虛擬邏輯分組，並非獨立管理分組數據（如果在此處新增後並未保存相關數據對應的分組不會被保存）", "i18n_bd7c8c96bc": "手動上傳", "i18n_bda44edeb5": "不能操作", "i18n_bdc1fdde6c": "beta計劃：", "i18n_bdd4cddd22": "將還原【", "i18n_bdd87b63a6": "微信二維碼", "i18n_bdd9d38d7e": "列寬", "i18n_be166de983": "軟鏈的項目", "i18n_be1956b246": "深色2 blackboard", "i18n_be2109e5b1": "確定要重置用户密碼嗎？", "i18n_be24e5ffbe": "Java 項目（java -jar xxx）", "i18n_be28f10eb6": "請選擇發佈的一級目錄和填寫二級目錄", "i18n_be381ac957": "請選擇要使用的倉庫", "i18n_be3a4d368e": "分發中、2：分發結束、3：已取消、4：分發失敗", "i18n_be4b9241ec": "默認狀態碼為 200 表示執行成功", "i18n_be5b6463cf": "語法參考", "i18n_be5fbbe34c": "保存", "i18n_beafc90157": "分發到機器節點中的腳本庫在節點腳本支持使用 G{'@'}(\"xxxx\")格式來引用，當存在引用時系統會自動替換引用腳本庫中的腳本內容", "i18n_bebcd7388f": "加載構建數據中", "i18n_bec98b4d6a": "狀態：", "i18n_becc848a54": "私鑰文件絕對路徑（絕對路徑前面新增 file", "i18n_bef1065085": "Chrome 擴展", "i18n_bf0e1e0c16": "輸入倉庫名稱或者倉庫路徑進行{slot1}", "i18n_bf77165638": "您確定要重啟當前容器嗎？", "i18n_bf7da0bf02": "新密碼", "i18n_bf91239ad7": "命令描述", "i18n_bf93517805": "下列配置信息僅在當前瀏覽器生效", "i18n_bf94b97d1a": "修改時間：", "i18n_bfacfcd978": "將取消自動加載環境變量", "i18n_bfc04cfda7": "分支", "i18n_bfda12336c": "搜索查看", "i18n_bfe68d5844": "鏈接", "i18n_bfe8fab5cd": "需要配置授權目錄（授權才能正常使用發佈）,授權目錄主要是用於確定可以發佈到哪些目錄中", "i18n_bfed4943c5": "參數值", "i18n_c00fb0217d": "請填寫用户暱稱", "i18n_c03465ca03": "禁用數量", "i18n_c0996d0a94": " ：每週一和週二的11:59執行", "i18n_c0a9e33e29": "請選擇構建對應的分支,必選", "i18n_c0ad27a701": "實際可用環境變量", "i18n_c0d19bbfb3": "請輸入 key 的值", "i18n_c0d38f475f": "軟內存", "i18n_c0d5d68f5f": "忽略", "i18n_c0e498a259": "點擊圖標查看關聯的所有任務", "i18n_c0f4a31865": "邏輯刪除", "i18n_c11eb9deff": "文件MD5", "i18n_c12ba6ff43": "我們有權利追訴破壞開源並因此獲利的團隊個人的全部違法所得，也歡迎給我們提供侵權線索。", "i18n_c163613a0d": "如果當前集羣還存在可能出現數據不一致問題奧", "i18n_c1690fcca5": "導入證書", "i18n_c16ab7c424": "】 嗎？注意：取消/停止構建不一定能正常關閉所有關聯進程", "i18n_c1786d9e11": "節點地址", "i18n_c17aefeebf": "系統名：", "i18n_c18455fbe3": "授權信息錯誤", "i18n_c195df6308": "異常", "i18n_c1af35d001": "構建產物", "i18n_c1b72e7ded": "為變量名稱", "i18n_c23fbf156b": "未選擇ssh", "i18n_c26e6aaabb": "擅自修改或者刪除版權信息有法律風險", "i18n_c2add44a1d": "一些例子：", "i18n_c2b2f87aca": "腳本孤獨數據", "i18n_c2ee58c247": "構建命令", "i18n_c2f11fde3a": "初始化系統賬户", "i18n_c31ea1e3c4": "沒有任何操作日誌", "i18n_c325ddecb1": "CPU 週期的長度，以微秒為單位。", "i18n_c32e7adb20": "請輸入遠程下載安全HOST，回車支持輸入多個路徑，示例 https://www.test.com 等", "i18n_c34175dbef": "控制枱日誌備份路徑: ", "i18n_c3490e81bf": "重啟創建之前會自動將之前的容器刪除掉", "i18n_c34f1dc2b9": "參數中的 id 、token 和觸發構建一致", "i18n_c3512a3d09": "請選選擇類型", "i18n_c35c1a1330": "排序值", "i18n_c360e994db": "排序", "i18n_c36ab9a223": "為 docker bridge 上的容器創建一個新的網絡堆棧", "i18n_c37ac7f024": "清除代碼", "i18n_c3aeddb10d": "當前工作空間還沒有邏輯節點不能創建節點分發奧", "i18n_c3f28b34bb": "集羣名稱", "i18n_c43743d213": "推送後刪除", "i18n_c446efd80d": "編輯 Script", "i18n_c4535759ee": "系統提示", "i18n_c46938460b": "系統使用 docker http 接口實現和 docker 通訊和管理，但是默認", "i18n_c469afafe0": "您確定要刪除當前容器嗎？", "i18n_c494fbec77": "手動新增", "i18n_c4a61acace": "命令？", "i18n_c4b5d36ff0": "節點狀態會自動識別服務器中是否存在 java 環境,如果沒有 Java 環境不能快速安裝節點", "i18n_c4cfe11e54": "如果上傳的壓縮文件是否自動解壓 支持的壓縮包類型有 tar", "i18n_c4e0c6b6fe": "篩選項目", "i18n_c4e2cd2266": "還需要對相關數據都操作後才能達到預期排序", "i18n_c5099cadcd": "插件數", "i18n_c53021f06d": "填寫【xxx", "i18n_c530a094f9": "構建方式:", "i18n_c538b1db4a": "軟鏈項目（類似於項目副本使用相關路徑的文件）", "i18n_c583b707ba": "個參數的描述", "i18n_c5a2c23d89": "非全屏", "i18n_c5aae76124": "綁定 SSH", "i18n_c5bbaed670": "狀態碼錯誤", "i18n_c5c3583bfc": "發送驗證碼", "i18n_c5c69827c5": "等網絡端口限制", "i18n_c5de93f9c7": "需要手動確認", "i18n_c5e7257212": "當前節點ID：", "i18n_c5f9a96133": "系統默認將對 demo 賬號限制很多權限。非演示場景不建議使用 demo 賬號", "i18n_c600eda869": "命令文件將在 {'${數據目錄}'}/script/xxxx.sh、bat 執行", "i18n_c618659cea": "自定義分支通配表達式", "i18n_c6209653e4": "SMTP 服務器域名", "i18n_c68dc88c51": "請輸入監控名稱", "i18n_c6a3ebf3c4": "接收大小", "i18n_c6c2497dbe": "請輸入項目日誌文件的編碼格式，默認跟隨全局配置", "i18n_c6e4cddba0": "請選擇監控的功能", "i18n_c6f1c6e062": "目錄不能編輯", "i18n_c6f6a9b234": "遷移到其他工作空間", "i18n_c704d971d6": "請填寫構建和產物", "i18n_c7099dabf6": "正在上傳文件", "i18n_c71a67ab03": "分發後", "i18n_c75b14a04e": "監控頻率可以到服務端配置文件中修改", "i18n_c75d0beca8": "開啟頁面操作引導、導航", "i18n_c7689f4c9a": "這裏構建命令最終會在服務器上執行。如果有多行命令那麼將", "i18n_c76cfefe72": "端口", "i18n_c7c4e4632f": ",更新期間允許的失敗率", "i18n_c7e0803a17": "密碼只會出現一次，關閉窗口後無法再次查看密碼", "i18n_c806d0fa38": "壓縮包", "i18n_c83752739f": "支持的字段可以通過接口返回的查看", "i18n_c840c88b7c": "真的要清空 【", "i18n_c84ddfe8a6": "執行日誌", "i18n_c8633b4b77": "通過私人令牌導入倉庫", "i18n_c87bd94cd7": "鏡像Id: ", "i18n_c889b9f67d": "新增關聯項目", "i18n_c89e9681c7": "臨時文件佔用空間", "i18n_c8a2447aa9": "加入", "i18n_c8b2aabc07": "文件中如果不存在：MemAvailable，那麼 MemAvailable = MemFree+Active(file)+Inactive(file)+SReclaimable，所以本系統 中內存佔用計算方式：內存佔用=(total-(MemFree+Active(file)+Inactive(file)+SReclaimable))/total", "i18n_c8c452749e": "選擇 SQL 文件", "i18n_c8c45e8467": "請根據自身項目啟動時間來配置", "i18n_c8c6e37071": "温馨提醒", "i18n_c8ce4b36cb": "重命名", "i18n_c90a1f37ce": "節點id", "i18n_c96b442dfb": "通用郵箱 SSL", "i18n_c96f47ec1b": "異步請求不能保證有序性", "i18n_c972010694": "產物目錄", "i18n_c9744f45e7": "否", "i18n_c97e6e823a": "重新啟動容器，除非它已被停止", "i18n_c983743f56": "總內存", "i18n_c996a472f7": "每天0/12點刷新一次", "i18n_c99a2f7ed8": "啟動命令", "i18n_c9b0f8e8c8": "真的要刪除", "i18n_c9b79a2b4f": "全局代理配置後將對服務端的網絡生效，代理實現方式：ProxySelector", "i18n_c9daf4ad6b": "多線程", "i18n_ca32cdfd59": "內存使用率：", "i18n_ca527c48cf": "是否將本次發佈的信息存儲為發佈任務模板，方便下次快捷使用相同配置信息來發布任務。", "i18n_ca69dad8fc": "流程執行完腳本後，輸出的內容最後一行必須為：running", "i18n_ca774ec5b4": "上次構建基於 commitId：", "i18n_caa9b5cd94": "需要將標籤值配置到構建 DSL 中的", "i18n_cab7517cb4": "節點地址：", "i18n_cabdf0cd45": "選擇產物", "i18n_cac26240b5": "容器日誌", "i18n_cac6ff1d82": "批量獲取構建狀態地址", "i18n_cad01fe13c": "黑白 ambiance-mobile", "i18n_caed797183": "reserveCount 表示觸發器生成的備份保留個數", "i18n_caf335a345": "java信息", "i18n_cb09b98416": "個性配置區", "i18n_cb156269db": "單位秒,最小值 1 秒", "i18n_cb25f04b46": "在文件最後 3 行中搜索", "i18n_cb28aee4f0": "節點分發【暫不支持遷移】", "i18n_cb46672712": "日誌閲讀 【暫不支持遷移】", "i18n_cb93a1f4a5": "、root、manager", "i18n_cb951984f2": "已存在", "i18n_cb9b3ec760": "批量觸發參數 BODY json： [ { \"id\":\"1\", \"token\":\"a\",\"action\":\"status\" } ]", "i18n_cbc44b5663": "執行時間結束", "i18n_cbcc87b3d4": "、名稱、版權等", "i18n_cbce8e96cf": "證書信息", "i18n_cbd9ffe1b8": "sudo執行", "i18n_cbdc4f58f6": "請輸入機器的名稱", "i18n_cbdcabad50": "釋放", "i18n_cbee7333e4": "本地命令是指,在服務端本地執行多條命令來實現發佈", "i18n_cc3a8457ea": "節點狀態是異步獲取有一定時間延遲", "i18n_cc42dd3170": "開啟", "i18n_cc51f34aa4": "編輯服務", "i18n_cc5dccd757": "工作空間中邏輯節點中腳本模版數量：", "i18n_cc617428f7": "隱私變量是指一些密碼字段或者關鍵密鑰等重要信息，隱私字段只能修改不能查看（編輯彈窗中無法看到對應值）。 隱私字段一旦創建後將不能切換為非隱私字段", "i18n_cc637e17a0": "產物:", "i18n_cc92cf1e25": "請填寫產物目錄", "i18n_cc9a708364": "狀態碼錯誤 ", "i18n_cca4454cf8": "請輸入公吿內容", "i18n_ccb2fdd838": "切換工作空間", "i18n_ccb91317c5": "命令內容", "i18n_ccea973fc7": "當前節點地址：", "i18n_cd1aedc667": "在 設置 --> 應用 --> 生成令牌", "i18n_cd649f76d4": "時間範圍", "i18n_cd998f12fa": "當目標工作空間已經存在節點時候將自動同步節點授權信息、代理配置信息", "i18n_cda84be2f6": "操作日誌", "i18n_cdc478d90c": "系統名", "i18n_ce043fac7d": "當前工作空間還沒有SSH", "i18n_ce07501354": "點擊數字查看運行中的任務", "i18n_ce1c5765e4": "查看發佈模板", "i18n_ce1ecd8a5b": "還有更多相關依賴開源組件", "i18n_ce23a42b47": "任務名", "i18n_ce40cd6390": "關聯腳本", "i18n_ce559ba296": "運行命令", "i18n_ce7e6e0ea9": "當前配置僅對選擇的工作空間生效,其他工作空間需要另行配置", "i18n_ce84c416f9": "請輸入用户信息 url [userInfoUri]", "i18n_ced3d28cd1": "不掃描", "i18n_ceee1db95a": "容器端口", "i18n_ceffe5d643": "兩步驗證應用", "i18n_cf38e8f9fd": "當前為節點分發的授權路徑配置", "i18n_cfa72dd73a": "請輸入要檢查的 cron 表達式", "i18n_cfb00269fd": "執行腳本", "i18n_cfbb3341d5": "當前登錄的賬號是：", "i18n_cfd482e5ef": "暫無數據,請先新增節點項目數據", "i18n_cfeea27648": "創建文件 /xxx/xxx/xxx", "i18n_cfeff30d2c": "目錄：", "i18n_d00b485b26": "回滾", "i18n_d0132b0170": "還原過程中不能操作哦...", "i18n_d02a9a85df": "請選擇報警聯繫人", "i18n_d047d84986": "個 / 共", "i18n_d0874922f0": "綁定指定目錄可以在線管理，同時構建 ssh 發佈目錄也需要在此配置", "i18n_d0a864909b": "方式生成公私鑰", "i18n_d0b2958432": "版本號", "i18n_d0b7462bdc": "此編輯僅能編輯當前 SSH 在此工作空間的名稱信息", "i18n_d0be2fcd05": "：表示間隔時間，例如在分上，表示每兩分鐘，同樣*可以使用數字列表代替，逗號分隔", "i18n_d0c06a0df1": "請輸入驗證碼", "i18n_d0c879f900": "上傳前", "i18n_d0eddb45e2": "私鑰", "i18n_d0f53484dc": "腳本ID：", "i18n_d1498d9dbf": "構建備註", "i18n_d159466d0a": "關聯數據名", "i18n_d175a854a6": "構建目錄", "i18n_d17eac5b5e": "我要加入", "i18n_d18d658415": "腳本ID", "i18n_d19bae9fe0": "插件端安裝並啟動成功後將主動上報節點信息,如果上報的 IP+PROP 能正常通訊將新增節點信息", "i18n_d1aa9c2da9": "子網掩碼：", "i18n_d1b8eaaa9e": "需要輸入驗證碼,確認綁定後才生效奧", "i18n_d1f0fac71d": "沒有選擇節點不能保存腳本", "i18n_d1f56b0a7e": "傳入參數有：monitorId、monitorName、nodeId、nodeName、projectId、projectName、title、content、runStatus", "i18n_d242bc3990": "# 您還可以自定義基礎鏡像來實現複雜業務", "i18n_d263a9207f": "支持 html 格式", "i18n_d27cf91998": "參考地址：", "i18n_d2913cea31": "確定要更改下載授權碼碼？請您自行確認授權碼是否正確，僅到觸發下載時才能使用到下載授權碼，錯誤的授權碼將不能正常下載更新包", "i18n_d2cac1245d": "是否將【", "i18n_d2e2560089": "文件名稱", "i18n_d2f484ff7e": "。如果輸出最後一行不是預期格式項目狀態將是未運行", "i18n_d2f4a1550a": "沒有任何節點腳本", "i18n_d301fdfc20": "開啟自動創建用户後第一次登錄僅自動創建賬號，還需要管理員手動分配權限組", "i18n_d30b8b0e43": "未構建", "i18n_d31d625029": "加入 beta 計劃可以及時獲取到最新的功能、一些優化功能、最快修復 bug 的版本，但是 beta 版也可能在部分新功能上存在不穩定的情況。", "i18n_d324f8b5c9": " 替換", "i18n_d35a9990f4": "已無更多節點項目，請先創建項目", "i18n_d373338541": "支持IP 地址、域名、主機名", "i18n_d3ded43cee": "查看構建日誌", "i18n_d3e480c8c0": "失敗次數", "i18n_d3fb6a7c83": ",2 秒後將自動跳轉到登錄頁面", "i18n_d40b511510": "證書", "i18n_d438e83c16": "項目分組", "i18n_d4744ce461": "還沒有配置權限組,不能創建用户", "i18n_d47ea92b3a": "編輯證書", "i18n_d4aea8d7e6": "執行次數", "i18n_d4e03f60a9": "插件端啟動的時候檢查項目狀態，如果項目狀態是未運行則嘗試執行啟動項目", "i18n_d5269713c7": "表示構建產物為文件夾時將打包為", "i18n_d57796d6ac": " ：範圍：0~59", "i18n_d584e1493b": "搜索ssh名稱", "i18n_d58a55bcee": "關", "i18n_d5a73b0c7f": "上傳", "i18n_d5c2351c0e": "請選擇可以執行的星期", "i18n_d5c68a926e": "執行順序", "i18n_d5d46dd79b": "分鐘級別", "i18n_d615ea8e30": "選擇升級文件", "i18n_d61af4e686": "斷點/分片別名下載", "i18n_d61b8fde35": "切換集羣", "i18n_d64cf79bd4": "確認要加入 beta 計劃嗎？", "i18n_d65551b090": "分片上傳文件", "i18n_d65d977f1d": "填寫運行參數", "i18n_d679aea3aa": "運行中", "i18n_d6937acda5": "項目存儲的文件夾，jar 包存放的文件夾", "i18n_d6a5b67779": "系統進程", "i18n_d6cdafe552": "會話已經關閉[project-console]", "i18n_d6eab4107a": "Java 項目（java -jar Springboot war）【不推薦】", "i18n_d72471c540": "瀏覽器標識", "i18n_d731dc9325": "時間戳：", "i18n_d7471c0261": "請選擇執行節點", "i18n_d75c02d050": "停止項目", "i18n_d7ac764d3a": "分發間隔時間 （順序重啟、完整順序重啟）方式才生效", "i18n_d7ba18c360": "分發節點是指在編輯完腳本後自動將腳本內容同步節點的腳本中", "i18n_d7bebd0e5e": "狀態操作請到控制枱中控制", "i18n_d7c077c6f6": "命令日誌", "i18n_d7cc44bc02": "用户資料", "i18n_d7d11654a7": "不存在", "i18n_d7ec2d3fea": "名稱", "i18n_d7ee59f327": "私鑰,不填將使用默認的 $HOME/.ssh 目錄中的配置。支持配置文件目錄:file:", "i18n_d7ef19d05b": "還沒有配置模板節點", "i18n_d81bb206a8": "無", "i18n_d82ab35b27": "文件前N行", "i18n_d82b19148f": "請選擇要同步授權的機器節點", "i18n_d83aae15b5": "在線構建文件主要保存，倉庫文件，構建歷史產物等。不支持主動清除，如果文件佔用過大可以配置保留規則和對單個構建配置是否保存倉庫、產物文件等", "i18n_d84323ba8d": "倉庫自動遷移後可能會重複存在請手動解決", "i18n_d85279c536": "上傳時請勿刷新或者關閉瀏覽器窗口。", "i18n_d87940854f": "計劃次數", "i18n_d87f215d9a": "卡片", "i18n_d88651584f": "剩餘空間", "i18n_d8a36a8a25": "編輯 Docker 集羣", "i18n_d8bf90b42b": "其他用户可以配置權限解除限制", "i18n_d8c7e04c8e": "信息", "i18n_d8db440b83": "您需要根據您業務情況來評估是否可以加入 beta。", "i18n_d911cffcd5": "下載地址", "i18n_d921c4a0b6": "真的要刪除“", "i18n_d926e2f58e": "取消任務", "i18n_d937a135b9": "淺色 eclipse", "i18n_d94167ab19": "網絡端口", "i18n_d9435aa802": "解析模式", "i18n_d9531a5ac3": "還沒有配置權限組不能創建用户奧", "i18n_d9569a5d3b": "多IP可以使用", "i18n_d9657e2b5f": "請輸入項目文件夾", "i18n_d9ac9228e8": "創建", "i18n_d9c28e376c": "功能管理", "i18n_da1abf0865": "驗證碼 6 為純數字", "i18n_da1cb76e87": "請輸入腳本內容", "i18n_da317c3682": "【推薦】使用快速安裝方式導入機器並自動新增邏輯節點", "i18n_da4495b1b4": "郵箱：", "i18n_da509a213f": "工作空間用於隔離數據,工作空間下面可以有不同數據,不同權限,不同菜單等來實現權限控制", "i18n_da671a4d16": "微信：", "i18n_da79c2ec32": "配置示例", "i18n_da89135649": "企業服務", "i18n_da8cb77838": "在線升級", "i18n_da99dbfe1f": "分發狀態", "i18n_dab864ab72": "快速綁定", "i18n_dabdc368f5": "請選擇分配類型", "i18n_dacc2e0e62": "硬件硬盤", "i18n_dadd4907c2": "】目錄,", "i18n_daf783c8cd": "分", "i18n_db06c78d1e": "測試", "i18n_db094db837": "修改變量值地址", "i18n_db2d99ed33": "還未配置集羣地址,不能切換集羣", "i18n_db4470d98d": "報警狀態", "i18n_db4b998fbd": "Oauth2 使用提示", "i18n_db5cafdc67": "真的要解綁節點麼？", "i18n_db686f0328": "公鑰,不填將使用默認的 $HOME/.ssh 目錄中的配置。支持配置文件目錄:file:", "i18n_db709d591b": "不同步", "i18n_db732ecb48": "延遲", "i18n_db81a464ba": "執行構建時會生成一個容器來執行，構建結束後會自動刪除對應的容器", "i18n_db9296212a": "定時構建", "i18n_dba16b1b92": "構建事件", "i18n_dbad1e89f7": "兩步驗證", "i18n_dbb166cf29": "服務id", "i18n_dbb2df00cf": "發佈目錄", "i18n_dbba7e107a": "發佈項目", "i18n_dbc0b66ca4": "個結果", "i18n_dc0d06f9c7": "請填寫發佈的二級目錄", "i18n_dc2961a26f": "節點名：", "i18n_dc2c61a605": "自建 Gitlab", "i18n_dc32f465da": "容器地址 tcp", "i18n_dc3356300f": "如果要配置 SSH 請到【系統管理】-> 【資產管理】-> 【SSH 管理】中去配置。", "i18n_dc39b183ea": "確定要忽略綁定兩步驗證嗎？強烈建議超級管理員開啟兩步驗證來保證賬號安全性", "i18n_dcc846e420": "批量構建全部參數舉例", "i18n_dcd72e6014": "獲取單個構建狀態地址", "i18n_dce5379cb9": "隱藏", "i18n_dcf14deb0e": "代理地址 (127.0.0.1:8888)", "i18n_dd1d14efd6": "查看腳本", "i18n_dd23fdf796": "編輯過程中可以切換節點但是要注意數據是否匹配", "i18n_dd4e55c39c": "未開始", "i18n_dd95bf2d45": "正常登錄", "i18n_dda8b4c10f": "分片上傳", "i18n_ddc7d28b7b": "變量", "i18n_dddf944f5f": "重置頁面操作引導、導航成功", "i18n_ddf0c97bce": "請注意備份數據防止數據丟失！！", "i18n_ddf7d2a5ce": "命令", "i18n_de17fc0b78": "硬盤最大的使用率：", "i18n_de3394b14e": "沒有資產機器", "i18n_de4cf8bdfa": "付費加入我們的技術交流羣優先解答您所有疑問", "i18n_de5dadc480": "pull日誌", "i18n_de6bc95d3b": "清空當前目錄文件", "i18n_de78b73dab": "單個觸發器地址", "i18n_debdfce084": "請輸入集羣名稱", "i18n_decef97c7c": "服務端IP授權配置", "i18n_deea5221aa": "標記", "i18n_df011658c3": "範圍", "i18n_df1da2dc59": "容器在一個 CPU 週期內可以獲得的 CPU 時間的微秒。", "i18n_df3833270b": "地址：", "i18n_df39e42127": "自動執行", "i18n_df59a2804d": "禁止掃描", "i18n_df5f80946d": "# node 鏡像源 https://registry.npmmirror.com/-/binary/node/", "i18n_df9497ea98": "存在", "i18n_df9d1fedc5": "節點分發是指,一個項目部署在多個節點中使用節點分發一步完成多個節點中的項目發佈操作", "i18n_dfb8d511c7": "用户名稱", "i18n_dfcc9e3c45": "分發後操作", "i18n_e039ffccc8": "】此文件還原到項目目錄？", "i18n_e049546ff3": "【系統配置目錄】中修改", "i18n_e06497b0fb": "查看當前可用容器", "i18n_e06caa0060": "文件修改時間", "i18n_e074f6b6af": "SMTP 服務器端口", "i18n_e07cbb381c": "沒有任何倉庫", "i18n_e09d0d8c41": "不建議使用常用名稱如", "i18n_e0a0e26031": "使用當前鏡像創建一個容器", "i18n_e0ae638e73": "保留產物是指對在構建完成後是否保留構建產物相關文件，用於回滾", "i18n_e0ba3b9145": "節點腳本", "i18n_e0ce74fcac": "自動滾動", "i18n_e0d6976b48": "請選擇集羣關聯分組", "i18n_e0ea800e34": "打包正式環境 npm i && npm run build:prod", "i18n_e0ec07be7d": "客户端密鑰", "i18n_e0f937d57f": "臨時token", "i18n_e0fcbca309": "工作空間ID：", "i18n_e15f22df2d": "真的要清除構建信息麼？", "i18n_e166aa424d": "關於系統", "i18n_e17a6882b6": "腳本標記", "i18n_e19cc5ed70": "同步節點授權", "i18n_e1c965efff": "請選擇狀態", "i18n_e1fefde80f": "節點賬號密碼默認由系統生成：可以通過插件端數據目錄下 agent_authorize.json 文件查看（如果自定義配置了賬號密碼將沒有此文件）", "i18n_e222f4b9ad": "執行前需要檢查命令中的地址在對應的服務器中是否可以訪問,如果無法訪問將不能自動綁定節點,", "i18n_e235b0d4af": "提交ID: ", "i18n_e257dd2607": "請選擇SSH連接信息", "i18n_e26dcacfb1": " 檢查 ", "i18n_e2adcc679a": "單位 ns 秒", "i18n_e2b0f27424": "項目ID僅會在機器節點中限制唯一，不同工作空間（相同的工作空間）下是允許相同的項目ID", "i18n_e2be9bab6b": "複製下面任意一條命令到還未安裝插件端的服務器中去執行,執行前需要放行", "i18n_e2d8fba259": " 不保存", "i18n_e2f942759e": "會話異常", "i18n_e30a93415b": "使用私人令牌，可以在你不輸入賬號密碼的情況下對你賬號內的倉庫進行管理，你可以在創建令牌時指定令牌所擁有的權限。", "i18n_e319a2a526": "重置為重新生成觸發地址,重置成功後之前的觸發器地址將失效,構建觸發器綁定到生成觸發器到操作人上,如果將對應的賬號刪除觸發器將失效", "i18n_e31ca72849": "上傳壓縮文件", "i18n_e354969500": "令牌導入", "i18n_e362bc0e8a": "路徑：{source}(宿主機) => {destination}(容器)", "i18n_e39de3376e": "分配", "i18n_e39f4a69f4": "腳本數", "i18n_e39ffe99e9": "請輸入密碼", "i18n_e3cf0abd35": "郵箱驗證碼", "i18n_e3e85de50c": "請選擇構建方式", "i18n_e3ead2bd0d": "常見構建命令示例", "i18n_e3ee3ca673": "不追加腳本模板", "i18n_e4013f8b81": "機器名稱", "i18n_e414392917": "集羣信息：", "i18n_e42b99d599": "月", "i18n_e43359ca06": "請選擇 SSH節點", "i18n_e44f59f2d9": "發佈前命令", "i18n_e475e0c655": "證書共享", "i18n_e48a715738": "新建文件", "i18n_e4b51d5cd0": "運行狀態", "i18n_e4bea943de": "倉庫地址", "i18n_e4bf491a0d": "正在下載，請稍等...", "i18n_e4d0ebcd58": "選擇集羣", "i18n_e5098786d3": "args 參數", "i18n_e54029e15b": "退出集羣", "i18n_e54c5ecb54": "編輯構建", "i18n_e5915f5dbb": "(存在兼容問題,實際使用中需要提前測試) python3 sdk 鏡像使用：https://repo.huaweicloud.com/python/{'${PYTHON3_VERSION}'}/Python-{'${PYTHON3_VERSION}'}.tar.xz", "i18n_e5a63852fd": "節點密碼,請查看節點啟動輸出的信息", "i18n_e5ae5b36db": "關鍵詞高亮,支持正則(正則可能影響性能請酌情使用)", "i18n_e5f71fc31e": "搜索", "i18n_e5fae81ed4": "沒有搜索到任何 SSH 信息", "i18n_e60389f6d6": "當前前端打包時間：", "i18n_e60725e762": "週三", "i18n_e63fb95deb": "隊列數", "i18n_e64d788d11": "升級成功", "i18n_e6551a2295": "引用工作空間環境變量可以方便後面多處使用相同的密碼統一修改", "i18n_e6bf31e8e6": "長期token", "i18n_e6cde5a4bc": "沒有檢查到最新版", "i18n_e6e453d730": "請輸入變量名稱", "i18n_e6e5f26c69": "QQ郵箱 SSL", "i18n_e703c7367c": "當前狀態：", "i18n_e710da3487": "用時", "i18n_e72a0ba45a": "用户組", "i18n_e72f2b8806": "輸入倉庫名稱或者倉庫路徑進行搜索", "i18n_e747635151": "Script 名稱", "i18n_e76e6a13dd": "不引用環境變量", "i18n_e78e4b2dc4": "級別", "i18n_e7d83a24ba": "成功次數", "i18n_e7e8d4c1fb": "斷點/分片下載", "i18n_e7ffc33d05": "上傳後執行", "i18n_e8073b3843": "請選擇用户權限組", "i18n_e825ec7800": "協議類型", "i18n_e8321f5a61": "發佈方式：", "i18n_e83a256e4f": "確認", "i18n_e84b981eb4": "配置值 （如：5g）", "i18n_e8505e27f4": "升級前請閲讀更新日誌裏面的説明和注意事項並且", "i18n_e8e3bfbbfe": "確認關閉", "i18n_e8f07c2186": "如果未填寫將解析壓縮包裏面的 txt", "i18n_e9290eaaae": "關閉左側", "i18n_e930e7890f": "表達式類似於Linux的crontab表達式，表達式使用空格分成5個部分，按順序依次為：", "i18n_e95f9f6b6e": "SSL 連接", "i18n_e96705ead1": "如果按鈕不可用則表示當前節點已經關閉啦,需要去編輯中啟用", "i18n_e976b537f1": "緩存監控", "i18n_e97a16a6d7": " ：每兩分鐘執行", "i18n_e9bd4484a7": "發送方郵箱賬號", "i18n_e9c2cb1326": "次要ID", "i18n_e9e9373c6f": "執行任務中", "i18n_e9ea1e7c02": "文件保存天數,默認 3650 天", "i18n_e9ec2b0bee": "並等待上一個項目啟動完成才能關閉下一個項目", "i18n_e9f2c62e54": ",新增默認參數後在手動執行腳本時需要填寫參數值", "i18n_ea15ae2b7f": "選項", "i18n_ea3c5c0d25": "臨時文件佔用空間：", "i18n_ea58a20cda": "機器DOCKER", "i18n_ea7fbabfa1": "請輸入賬户名", "i18n_ea89a319ec": "# 宿主機目錄和容器目錄掛載 /host:/container:ro", "i18n_ea8a79546f": "請輸入發佈的文件id", "i18n_ea9f824647": "拉取倉庫超時時間,單位秒", "i18n_eaa5d7cb9b": "過期天數", "i18n_eadd05ba6a": "中等", "i18n_eaf987eea0": "權重（相對權重）。", "i18n_eb164b696d": "排除發佈", "i18n_eb5bab1c31": "非必填", "i18n_eb79cea638": "週五", "i18n_eb7f9ceb71": "腳本庫：", "i18n_eb969648aa": "請提前備份數據再操作奧", "i18n_ebc2a1956b": "編輯監控", "i18n_ebc96f0a5d": "總內存（內存 + 交換）。 設置為 -1 以禁用交換。", "i18n_ec1f13ff6d": "總數：", "i18n_ec219f99ee": "執行結束", "i18n_ec22193ed1": "請選擇分組", "i18n_ec537c957a": "{slot1}機目錄", "i18n_ec6e39a177": "確認要下載更新最新版本嗎？", "i18n_ec7ef29bdf": "請輸入靜態，回車支持輸入多個路徑，系統會自動過濾 ../ 路徑、不允許輸入根路徑", "i18n_ec989813ed": "狀態信息：", "i18n_eca37cb072": "創建時間", "i18n_ecdf9093d0": "同時展開多個", "i18n_ecff77a8d4": "使用", "i18n_ed145eba38": "硬盤佔用", "i18n_ed19a6eb6f": "在線構建文件佔用空間", "i18n_ed367abd1a": "修改用户資料", "i18n_ed39deafd8": "編輯倉庫", "i18n_ed40308fe9": "# maven 鏡像源 https://mirrors.tuna.tsinghua.edu.cn/apache/maven/maven-3/", "i18n_ed6a8ee039": " ：表示多個定時表達式", "i18n_ed8ea20fe6": "安裝ID", "i18n_edb4275dcd": "gmail郵箱", "i18n_edb881412a": "注意：為了避免不必要的事件執行腳本，選擇的腳本的備註中包含需要實現的事件參數關鍵詞，如果需要執行 success 事件,那麼選擇的腳本的備註中需要包含 success 關鍵詞", "i18n_edc1185b8e": "嘗試自動續簽失敗", "i18n_edd716f524": "請選擇發佈的一級目錄", "i18n_ede2c450d1": "沒有任何登錄日誌", "i18n_ee19907fad": "# 基礎鏡像 目前支持 ubuntu-latest、ubuntu-git、ubuntu-1ms-latest、ubuntu-1ms-git", "i18n_ee4fac2f3c": "為了避免部分節點不能及時響應造成監控阻塞,節點統計超時時間不受節點超時配置影響將採用默認超時時間(10秒)", "i18n_ee6ce96abb": "s 秒", "i18n_ee8ecb9ee0": "優先級", "i18n_ee9a51488f": "請輸入發佈目錄", "i18n_eeb6908870": "上一步", "i18n_eec342f34e": "默認賬號為：jpomAgent", "i18n_eee6510292": "配置授權目錄", "i18n_eee83a9211": "資源", "i18n_eeef8ced69": "解綁會檢查數據關聯性,同時將自動刪除節點項目和腳本緩存信息", "i18n_eef3653e9a": "jvm{slot1},{slot2}.如：-Xms512m -Xmx512m", "i18n_eef4dfe786": "Java 項目（java -Djava.ext.dirs=lib -cp conf:run.jar $MAIN_CLASS）", "i18n_ef016ab402": "確認創建該", "i18n_ef28d3bff2": "頁面內容自動撐開出現屏幕滾動條", "i18n_ef651d15b0": "創建之後不能修改,分發 ID 等同於項目 ID", "i18n_ef734bf850": "更多説明", "i18n_ef7e3377a0": "允許時段", "i18n_ef800ed466": "程序運行的 main 類(jar 模式運行可以不填)", "i18n_ef8525efce": "分配到其他工作空間", "i18n_ef9c90d393": "沒有任何的腳本庫", "i18n_efae7764ac": "賬號登錄", "i18n_efafd0cbd4": "密碼（6-18位數字、字母、符號組合）", "i18n_efb88b3927": "系統運行時間", "i18n_efd32e870d": "插件構建時間", "i18n_efe71e9bec": "真的要解綁當前節點分發麼？", "i18n_efe9d26148": "真的要刪除該證書麼，刪除會將證書文件一併刪除奧？", "i18n_f013ea9dcb": "加載中", "i18n_f038f48ce5": "編輯腳本", "i18n_f04a289502": "svn ssh 必填登錄用户", "i18n_f05e3ec44d": "禁止訪問,當前IP限制訪問", "i18n_f06f95f8e6": "孤獨數據", "i18n_f087eb347c": "構建命令示例", "i18n_f08afd1f82": "已選擇", "i18n_f0a1428f65": "賬號支持引用工作空間變量：", "i18n_f0aba63ae7": "頒發者", "i18n_f0db5d58cb": "開啟兩步驗證使賬號更安全", "i18n_f0eb685a84": "文件來兼容您的機器", "i18n_f105c1d31d": "最後執行人", "i18n_f113c10ade": "排空", "i18n_f11569cfa9": "展示的環境變量可能在實際執行中觸發修改值或者新增變量的情況,請以最終執行變量為準", "i18n_f139c5cf32": "輸入新名稱", "i18n_f175274df0": "登錄名稱,賬號,創建之後不能修改", "i18n_f1a2a46f52": "# 使用哪個 docker 構建,填寫 docker 標籤 默認查詢可用的第一個,如果 tag 查詢出多個也選擇第一個結果", "i18n_f1b2828c75": "安裝插件端", "i18n_f1d8533c7f": "請輸入證書信息或者選擇證書信息,證書信息填寫規則：序列號:證書類型", "i18n_f1e3ef0def": "將鏡像推送到遠程倉庫後自動刪除本地鏡像。如果開啟了發佈到集羣服務不建議開啟此選項，除非集羣服務使用遠程鏡像", "i18n_f1fdaffdf0": "後台構建", "i18n_f240f9d69c": "分支名稱：", "i18n_f26225bde6": "詳情", "i18n_f26ef91424": "下載", "i18n_f27822dd8a": "提交消息: ", "i18n_f282058f75": "靜態文件項目（前端、日誌等）", "i18n_f2d05944ad": "創建 Docker 集羣", "i18n_f30f1859ba": "如果您基於 Jpom 二次開發修改了", "i18n_f332f2c8df": "網關", "i18n_f3365fbf4d": "未獲取到 Docker 或者禁用監控", "i18n_f33db5e0b2": "點擊刷新構建信息", "i18n_f37f8407ec": "文件ID：", "i18n_f3947e6581": "開源不等同於免費", "i18n_f3e93355ee": "重啟項目", "i18n_f425f59044": "系統版本：", "i18n_f4273e1bb4": " 文件ID", "i18n_f49dfdace4": "權限組", "i18n_f4b7c18635": "密碼長度為6-20", "i18n_f4baf7c6c0": "未啟動", "i18n_f4bbbaf882": "分支/標籤", "i18n_f4dd45fca9": "請輸入遠程地址", "i18n_f4edba3c9d": "未知的表格類型", "i18n_f4fb0cbecf": "還沒有任何結果", "i18n_f5399c620e": "真的要在該集羣剔除此節點麼？", "i18n_f562f75c64": "服務地址", "i18n_f56c1d014e": "執行成功", "i18n_f5c3795be5": "官方", "i18n_f5d0b69533": "完整的私鑰內容 如", "i18n_f5d14ee3f8": "磁盤佔用", "i18n_f5f65044ea": "容器安裝的服務端不能使用本地構建（因為本地構建依賴啟動服務端本地的環境，容器方式安裝不便於管理本地依賴插件）", "i18n_f63345630c": "# 將容器中的 node_modules 文件緩存到 docker 卷中", "i18n_f63870fdb0": "請填寫容器名稱", "i18n_f652d8cca7": "嘗試自動續簽...", "i18n_f66335b5bf": "錯誤信息：", "i18n_f66847edb4": "網頁應用ID", "i18n_f668c8c881": "集羣名稱：", "i18n_f685377a22": "腳本庫 ", "i18n_f68f9b1d1b": "最後心跳時間", "i18n_f6d6ab219d": "更新完成後確實成功的時間", "i18n_f6d96c1c8c": "為了兼容Quartz表達式，同時支持6位和7位表達式，其中：", "i18n_f6dee0f3ff": "分發 ID", "i18n_f712d3d040": "備註示例：", "i18n_f71316d0dd": "替換引用", "i18n_f71a30c1b9": "數據目錄佔用空間", "i18n_f7596f3159": "如果需要在其他工作空間需要提前切換生成命令", "i18n_f76540a92e": "準備中", "i18n_f782779e8b": "結束時間", "i18n_f7b9764f0f": "項目啟動,停止,重啟都將請求對應的地址", "i18n_f7e8d887d6": "工作空間環境變量", "i18n_f7f340d946": "真的要清除 SSH 隱藏字段信息麼？（密碼，私鑰）", "i18n_f8460626f0": "節點賬號,請查看節點啟動輸出的信息", "i18n_f86324a429": "使用 ANT 表達式來實現在過濾指定目錄來實現發佈排除指定目錄", "i18n_f89cc4807e": "授權路徑是指項目文件存放到服務中的文件夾", "i18n_f89fa9b6c6": "選擇倉庫", "i18n_f8a613d247": "請選擇節點", "i18n_f8b3165e0d": "當前項目被禁用", "i18n_f8f20c1d1e": "修剪在此時間戳之前創建的對象 例如：24h", "i18n_f8f456eb9a": "類型項目特有的 type：reload、restart", "i18n_f92d505ff5": "注意：系統默認監控 SSH是否正常會多次觸發登錄操作，如果密碼錯誤或者安全防護規則有限制並無法解除限制時候可以配置指定分組禁用監控（詳細配置參考配置文件）", "i18n_f932eff53e": "條數據", "i18n_f9361945f3": "主機名 hostname", "i18n_f967131d9d": "倉庫名稱", "i18n_f976e8fcf4": "監控名稱", "i18n_f97a4d2591": "請選擇要加入到哪個集羣", "i18n_f9898595a0": "注意：同一個分組不建議被多個集羣綁定", "i18n_f98994f7ec": "發佈方式", "i18n_f99ead0a76": "鏡像名稱不正確 不能更新", "i18n_f9ac4b2aa6": "操作人", "i18n_f9c9f95929": "Java 項目（java -classpath）", "i18n_f9cea44f02": "當前工作空間還沒有 Docker", "i18n_f9f061773e": "不填寫則使用節點分發配置的二級目錄", "i18n_fa2f7a8927": "失敗策略", "i18n_fa4aa1b93b": "運行項目", "i18n_fa57a7afad": "容器標籤,如：xxxx:latest 多個使用逗號隔開, 配置附加環境變量文件支持加載倉庫目錄下 .env 文件環境變量 如： xxxx:{'${VERSION}'}", "i18n_fa624c8420": "禁用後該用户不能登錄平台", "i18n_fa7f6fccfd": "項目名稱：", "i18n_fa7ffa2d21": "解鎖", "i18n_fa8e673c50": "編輯工作空間", "i18n_faa1ad5e5c": "協議", "i18n_faaa995a8b": "可以關閉", "i18n_faaadc447b": "序號", "i18n_fabc07a4f1": "請選擇監控操作", "i18n_fad1b9fb87": "新增腳本模版需要到節點管理中去新增", "i18n_fb1f3b5125": "當前工作空間關聯數據統計", "i18n_fb3a2241bb": "狀態描述：", "i18n_fb5bc565f3": "解析文件失敗：", "i18n_fb61d4d708": "真的要回滾該構建歷史記錄麼？", "i18n_fb7b9876a6": "請輸入腳本名稱", "i18n_fb852fc6cc": "進行中", "i18n_fb8fb9cc46": "統計説明", "i18n_fb91527ce5": "節點可用性：", "i18n_fb9d826b2f": "發佈後執行的命令(非阻塞命令),一般是啟動項目命令 如：ps -aux {'|'} grep java", "i18n_fba5f4f19a": "DSL環境變量", "i18n_fbd7ba1d9b": "最後分發時間", "i18n_fbee13a873": "工作空間總數：", "i18n_fbfa6c18bf": "已分配", "i18n_fbfeb76b33": "左邊菜單欄主題切換", "i18n_fc06c70960": "您確定要刪除當前鏡像嗎？", "i18n_fc4e2c6151": "登錄用户", "i18n_fc5fb962da": "郵箱密碼或者授權碼", "i18n_fc92e93523": "生效時間", "i18n_fc954d25ec": "代理", "i18n_fcaef5b17a": "重用另一個容器網絡堆棧", "i18n_fcb4c2610a": "通知異常", "i18n_fcb7a47b70": "阿里雲企業郵箱", "i18n_fcba60e773": "構建", "i18n_fcbf0d0a55": "需要先安裝依賴 yarn && yarn run build", "i18n_fcca8452fe": "集羣地址主要用於切換工作空間自動跳轉到對應的集羣", "i18n_fcef976c7a": "私鑰內容", "i18n_fd6e80f1e0": "正常", "i18n_fd7b461411": "不清空", "i18n_fd7e0c997d": "選擇文件", "i18n_fd93f7f3d7": "可以將腳本分發到機器節點中在 DSL 項目中引用，達到多個項目共用相同腳本", "i18n_fda92d22d9": "關聯節點會自動識別服務器中是否存在 java 環境,如果沒有 Java 環境不能快速安裝節點", "i18n_fdba50ca2d": "如果端口暴露到公網很", "i18n_fdbac93380": "SMTP 地址：smtp.mxhichina.com，端口使用 465 並且開啟 SSL，用户名需要和郵件發送人一致，密碼為郵箱的登錄密碼", "i18n_fdbc77bd19": "安全", "i18n_fdcadf68a5": "SMTP 端口", "i18n_fde1b6fb37": "需要提前為機器配置授權目錄", "i18n_fdfd501269": "java sdk 鏡像使用：https://mirrors.tuna.tsinghua.edu.cn/ 支持版本有：8, 9, 10, 11, 12, 13, 14, 15, 16, 17", "i18n_fe1b192913": "目錄創建成功後需要手動刷新右邊樹才能顯示出來喲", "i18n_fe231ff92f": "關閉頁面操作引導、導航", "i18n_fe2df04a16": "版本", "i18n_fe32def462": "活躍", "i18n_fe7509e0ed": "值", "i18n_fe828cefd9": "項目文件夾是項目實際存放的目錄名稱", "i18n_fe87269484": "集羣修改時間", "i18n_fea996d31e": "請填寫構建名稱", "i18n_fec6151b49": "賬户名稱", "i18n_feda0df7ef": "賬號郵箱", "i18n_ff17b9f9cd": "企業微信", "i18n_ff1fda9e47": "禁止", "i18n_ff39c45fbc": "使用容器內的主機網絡堆棧。 注意：主機模式賦予容器對本地系統服務（如 D-bus）的完全訪問權限，因此被認為是不安全的。", "i18n_ff3bdecc5e": "文件查看（如果自定義配置了賬號密碼將沒有此文件）", "i18n_ff80d2671c": "秒後刷新", "i18n_ff9814bf6b": "觸發類型", "i18n_ff9dffec4d": "搜索模式", "i18n_ffa9fd37b5": "工作空間管理", "i18n_ffaf95f0ef": "啟動的容器 可以看到很多host上的設備 可以執行mount。 可以在docker容器中啟動docker容器。", "i18n_ffd67549cf": "：範圍：1~12，同時支持不區分大小寫的別名：\"jan\",\"feb\", \"mar\", \"apr\", \"may\",\"jun\", \"jul\", \"aug\",\"sep\",\"oct\", \"nov\", \"dec\"", "i18n_fffd3ce745": "共享"}