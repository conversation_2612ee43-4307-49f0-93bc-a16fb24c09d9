{"i18n_0006600738": "加入 Docker 集群", "i18n_005de9a4eb": "构建历史是用于记录每次构建的信息,可以保留构建产物信息,构建日志。同时还可以快速回滚发布", "i18n_0079d91f95": "确定要将此数据置顶吗？", "i18n_007f23e18f": "关闭 TLS 认证", "i18n_00a070c696": "点击可以复制", "i18n_00b04e1bf0": "发送包", "i18n_00d5bdf1c3": "调用次数", "i18n_00de0ae1da": "文件上传前需要执行的脚本(非阻塞命令)", "i18n_01081f7817": "请输入允许编辑文件的后缀及文件编码，不设置编码则默认取系统编码，多个使用换行。示例：设置编码：txt{'@'}utf-8， 不设置编码：txt", "i18n_010865ca50": "真的要停止项目么？", "i18n_0113fc41fc": "全屏日志", "i18n_01198a1673": "上传小文件", "i18n_01226f48fc": "对于每一个子表达式，同样支持以下形式：", "i18n_0128cdaaa3": "分配类型", "i18n_01ad26f4a9": "重置触发器 token 信息,重置后之前的触发器 token 将失效", "i18n_01b4e06f39": "重启", "i18n_01e94436d1": "原密码", "i18n_020d17aac6": "发送大小", "i18n_020f1ecd62": "开始上传", "i18n_020f31f535": "路径需要配置绝对路径,不支持软链", "i18n_0215b91d97": "构建序号id需要跟进实际情况替换", "i18n_0221d43e46": "远程下载Url不为空", "i18n_0227161b3e": "执行方式", "i18n_022b6ea624": "您确定要删除当前卷吗？", "i18n_0253279fb8": "克隆深度", "i18n_02d46f7e6f": "真的要删除这些构建历史记录么？", "i18n_02d9819dda": "提示", "i18n_02db59c146": "禁止访问的 IP 地址", "i18n_02e35447d4": "下载构建产物,如果按钮不可用表示产物文件不存在,一般是构建没有产生对应的文件或者构建历史相关文件被删除", "i18n_0306ea1908": "删除镜像", "i18n_031020489f": "当前工作空间您触发的构建记录", "i18n_03580275cb": "请选中要重启的项目", "i18n_0360fffb40": "并开启此开关", "i18n_036c0dc2aa": "系统取消分发", "i18n_0373ba5502": "需要您在需要被管理的服务器中安装 agent ，并将 agent 信息新增到系统中", "i18n_03816381ec": "切换视图", "i18n_0390e2f548": "参数{count}描述", "i18n_03a74a9a8a": "日志路径", "i18n_03c1f7c142": "请填选择构建的仓库", "i18n_03d9de2834": "项目运维", "i18n_03dcdf92f5": "隐私变量", "i18n_03e59bb33c": "紧凑", "i18n_03f38597a6": "速度", "i18n_0428b36ab1": "副本", "i18n_04412d2a22": "操作不能撤回奥", "i18n_044b38221e": "Java 项目(示例参考，具体还需要根据项目实际情况来决定)", "i18n_045cd62da3": "型号：", "i18n_045f89697e": "压缩包进行发布", "i18n_047109def4": "待处理", "i18n_04a8742dd7": "插件运行时间", "i18n_04edc35414": "模板节点", "i18n_051fa113dd": "方式连接 docker 是通过终端实现，每次操作 docker 相关 api 需要登录一次终端", "i18n_05510a85b0": "系统中您所有操作日志", "i18n_059ac641c0": "特权：", "i18n_059b86dbe1": "确定要删除该发布任务模板吗？", "i18n_05b52ae2db": "{slot1} 用于容器构建选择容器功能（fromTag）", "i18n_05cfc9af9d": "接收错误", "i18n_05e6d88e29": "分发节点是指在编辑完脚本后自动将脚本内容同步节点的脚本,一般用户节点分发功能中的 DSL 模式", "i18n_05e78c26b1": "单个触发器地址中：第一个随机字符串为命令脚本ID，第二个随机字符串为 token", "i18n_05f6e923af": "执行错误", "i18n_0647b5fc26": "先停止", "i18n_066431a665": "请输入证书描述", "i18n_066f903d75": "操后上移或者下移可能不会达到预期排序", "i18n_067638bede": "CPU数", "i18n_067eb0fa04": "如果这里的报警联系人无法选择，说明这里面的管理员没有设置邮箱，在右上角下拉菜单里面的用户资料里可以设置。", "i18n_0693e17fc1": "有新内容后是否自动滚动到底部", "i18n_06986031a7": "需要到原始工作空间中去控制节点分发", "i18n_06e2f88f42": "请输入名称", "i18n_0703877167": "关闭MFA", "i18n_0719aa2bb0": "重置密码", "i18n_0728fee230": "请输入公告标题", "i18n_072fa90836": "压缩 ", "i18n_0739b9551d": "端口协议", "i18n_07683555af": "当前版本号：", "i18n_0793aa7ba3": "maven sdk 镜像使用：https://mirrors.tuna.tsinghua.edu.cn/apache/maven/maven-3/", "i18n_07a03567aa": "虚拟内存占用", "i18n_07a0e44145": "主机名：", "i18n_07a828310b": "并行度", "i18n_07a8af8c03": "为当前项目实际的进程ID", "i18n_07b6bb5e40": "严格执行脚本（构建命令、事件脚本、本地发布脚本、容器构建命令）执行返回状态码必须是 0、否则将构建状态标记为失败", "i18n_07d2261f82": "默认是当前时间到今年结束", "i18n_080b914139": "上传包", "i18n_0836332bf6": "升级协议", "i18n_083b8a2ec9": "一个物理节点被多个服务端绑定也会产生孤独数据奥", "i18n_08902526f1": "皮肤：", "i18n_0895c740a6": "交换内存占用", "i18n_089a88ecee": "系统时间：", "i18n_08ab230290": "操作说明", "i18n_08ac1eace7": "文件上传成功后需要执行的脚本(非阻塞命令)", "i18n_08b1fa1304": "请输入用户名", "i18n_08b55fea3c": "管理", "i18n_0934f7777a": "新标签终端", "i18n_095e938e2a": "停止", "i18n_09723d428d": "报警联系人", "i18n_09d14694e7": "需要 SSH 监控中能获取到 docker 信息", "i18n_09e7d24952": "实际内存占用率：", "i18n_0a056b0d5a": "动态文件", "i18n_0a1d18283e": "构建确认弹窗", "i18n_0a47f12ef2": "如果孤独数据被工作空间下的其他功能关联，修正后关联的数据将失效对应功能无法查询到关联数据", "i18n_0a54bd6883": "Gmail 邮箱配置", "i18n_0a60ac8f02": "是", "i18n_0a63bf5b41": "软内存限制。", "i18n_0a9634edf2": "地址通配符,* 表示所有地址都将使用代理", "i18n_0aa60d1169": "您还未登录过", "i18n_0aa639865c": "真的要删除机器 SSH 么？", "i18n_0ac4999a4c": "网卡信息", "i18n_0ac9e3e675": "绑定成功后将不再显示,强烈建议保存此二维码或者下面的 MFA key", "i18n_0af04cdc22": "支持两种方式填充：", "i18n_0af5d9f8e8": "当前区域为系统管理、资产管理中心", "i18n_0b23d2f584": "差异构建", "i18n_0b2fab7493": "当前 SSH 的授权目录（文件目录、文件后缀、禁止命令）需要请到 【系统管理】-> 【资产管理】-> 【SSH 管理】-> 操作栏中->关联按钮->对应工作空间->操作栏中->配置按钮", "i18n_0b3edfaf28": "设置内存限制。", "i18n_0b58866c3e": "断点/分片单文件下载", "i18n_0b6811e5b1": "使用语言", "i18n_0b76afbf5d": "允许执行的 CPU（例如，0-3、0", "i18n_0b9d5ba772": "请尊重开源协议，不要擅自修改版本信息，否则可能承担法律责任。", "i18n_0baa0e3fc4": "发布中", "i18n_0bac3db71c": "重启服务端后失效", "i18n_0bbc7458b4": "回到首页", "i18n_0bc45241af": "传入参数有：outGivingId、outGivingName、status、statusMsg、executeTime", "i18n_0bf9f55e9d": "不能关闭", "i18n_0bfcab4978": "node sdk 镜像使用：https://registry.npmmirror.com/-/binary/node", "i18n_0c0633c367": "不能删除默认工作空间", "i18n_0c1de8295a": "独立", "i18n_0c1e9a72b7": "将使用微队列来排队构建，避免几乎同时触发构建被中断构建（一般用户仓库合并代码会触发多次请求）,队列保存在内存中,重启将丢失", "i18n_0c1f1cd79b": "不自动重启", "i18n_0c1fec657f": "秒", "i18n_0c2487d394": "下拉搜索默认搜索关键词相关的前 10 个，以及已经选择的机器节点", "i18n_0c256f73b8": "容器名称：", "i18n_0c4eef1b88": "当为6位时，第一位表示", "i18n_0c5c8d2d11": "基础信息：", "i18n_0c7369bbee": "开启SSH访问", "i18n_0cbf83cc07": "联系我们", "i18n_0ccaa1c8b2": " ：表示匹配这个位置所有的时间", "i18n_0ce54ecc25": "付费社群", "i18n_0cf4f0ba82": "真的要保存当前配置吗？如果配置有误,可能无法启动服务需要手动还原奥！！！ 保存成功后请及时关注重启状态！！", "i18n_0cf81d77bb": "请填写仓库地址", "i18n_0d44f4903a": "真的要释放(删除)当前项目么？", "i18n_0d467f7889": "# 是否开启日志备份功能", "i18n_0d48f8e881": "请输入服务地址", "i18n_0d50838436": "数据目录", "i18n_0d98c74797": "其他", "i18n_0da9b12963": "用户数据", "i18n_0de68f5626": "登录JPOM", "i18n_0e052223a4": "重启服务端需要重新获取", "i18n_0e16902c1e": "查看状态", "i18n_0e1ecdae4a": "完整顺序执行(有执行失败将结束本次)", "i18n_0e25ab3b51": "证书的允许的 IP 需要和 docker host 一致", "i18n_0e44ae17ae": "服务端机器网络", "i18n_0e502fed63": "重启超时,请去服务器查看控制台日志排查问题", "i18n_0e55a594fd": "监控项目", "i18n_0e5f01b9be": "关联工作空间ssh", "i18n_0ea78e4279": "查看日志", "i18n_0ec9eaf9c3": "更多", "i18n_0eccc9451d": "# 备份文件保留个数", "i18n_0ee3ca5e88": "扫码赞赏支持开源项目长期发展", "i18n_0ef396cbcc": "分发结果", "i18n_0f004c4cf7": "第三方登录", "i18n_0f0a5f6107": "正常连接", "i18n_0f189dbaa4": "没有任何用户", "i18n_0f4f503547": "请输入版本", "i18n_0f539ff117": "真的要批量删除选择的镜像吗？已经被容器使用的镜像无法删除！", "i18n_0f59fe5338": "防火墙端口", "i18n_0f5fc9f300": "文件管理中心", "i18n_0f8403d07e": "刷新倒计时", "i18n_0fca8940a8": "没有节点", "i18n_0ff425e276": "文件ID", "i18n_1012e09849": "处理失败", "i18n_10145884ba": "文件后N行", "i18n_1014b33d22": "分组名称", "i18n_101a86bc84": "请输入...", "i18n_1022c545d1": "插件端启动时自动检查项目如未启动将尝试启动", "i18n_102dbe1e39": "注意：环境变量存在作用域：当前工作空间或者全局，不能跨工作空间引用", "i18n_102e8ec6d5": "网络流量信息", "i18n_104000e24a": "模板来源", "i18n_1058a0be42": "开启 TLS 认证,证书信息：", "i18n_1062619d5a": "节点账号密码默认由系统生成：可以通过插件端数据目录下 agent", "i18n_108d492247": "正则语法参考", "i18n_10c385b47e": "一键分发同步多个节点的系统配置", "i18n_10d6dfd112": "显示后N行", "i18n_10f6fc171a": "SSH 名称", "i18n_111e786daa": "填写备注仅本次构建生效", "i18n_1125c4a50b": "真的要删除分发信息么？删除后节点下面的项目也都将删除", "i18n_113576ce91": "产物目录：", "i18n_1149274cbd": "用户总数", "i18n_115cd58b5d": "】备份文件夹么？", "i18n_1160ab56fd": "构建命令：", "i18n_116d22f2ab": "项目ID：", "i18n_11724cd00b": "集群创建时间", "i18n_117a9cbc8d": "语言：", "i18n_11957d12e4": "报警中", "i18n_11e88c95ee": " 查找上一个", "i18n_121e76bb63": "请选择构建对应的分支", "i18n_1235b052ff": "节点地址 (***********00:2123)", "i18n_1278df0cfc": "关联节点如果服务器存在 java 环境,但是插件端未运行则会显示快速安装按钮", "i18n_127de26370": "SMTP 地址：【smtp.qq.com】，用户名一般是QQ号码，密码是邮箱授权码，端口默认 587/465", "i18n_12934d1828": "日志目录是指控制台日志存储目录", "i18n_12afa77947": "开启缓存构建目录将保留仓库文件,二次构建将 pull 代码, 不开启缓存目录每次构建都将重新拉取仓库代码(较大的项目不建议关闭缓存)", "i18n_12d2c0aead": "请将此密码复制告知该用户", "i18n_12dc402a82": "参考数据", "i18n_130318a2a1": "路由无效，无法跳转", "i18n_1303e638b5": "修改时间", "i18n_13627c5c46": "配置ssh", "i18n_138776a1dc": "默认是在插件端数据目录/{'${projectId}'}/{'${projectId}'}.log", "i18n_138a676635": "注意", "i18n_13c76c38b7": "# scriptId 可以引用脚本库中的脚本（G{'@'}xxx）其中 xxx 为脚本库中的脚本标记，前提需要提取将对应脚本同步至对应机器节点", "i18n_13d10a9b78": "没有资产SSH", "i18n_13d947ea19": "需要您先新增资产机器再分配机器节点（逻辑节点）到当前工作空间", "i18n_13f7bb78ef": "默认统计机器中除本地接口（环回或无硬件地址）网卡流量总和", "i18n_13f931c5d9": "查看任务", "i18n_1432c7fcdb": "系统公告", "i18n_143bfbc3a1": "点击重新同步当前工作空间逻辑节点项目信息", "i18n_143d8d3de5": "否则将删除满足条件的所有数据", "i18n_148484b985": "实现您需要配置 docker 容器到服务端中来管理，并且分配到当前工作空间中", "i18n_1498557b2d": "同时只能展开一个菜单", "i18n_14a25beebb": "10秒一次", "i18n_14d342362f": "标签", "i18n_14dcfcc4fa": "还未执行reload", "i18n_14dd5937e4": "附加环境变量  .env 新增多个使用逗号分隔", "i18n_14e6d83ff5": "时间：", "i18n_14ee5b5dc5": "命令文件将在 {'${插件端数据目录}'}/script/xxxx.sh 、bat 执行", "i18n_14feaa5b3a": "刷新倒计时 ", "i18n_1535fcfa4c": "发送", "i18n_156af3b3d1": "菜单配置", "i18n_1593dc4920": "真的要删除该记录么？删除后构建关联的容器标签将无法使用", "i18n_159a3a8037": "更新镜像", "i18n_15c0ba2767": "上传项目文件", "i18n_15c46f7681": "修改接口 HTTP 状态码为 200 并且响应内容为：success 才能确定操作成功反之均可能失败", "i18n_15d5fffa6a": "响应结果", "i18n_15e9238b79": "接收", "i18n_15f01c43e8": "日志备份列表", "i18n_15fa91e3ab": "天级别", "i18n_1603b069c2": "周一", "i18n_1622dc9b6b": "未知", "i18n_162e219f6d": "丢失", "i18n_164cf07e1c": "清空覆盖", "i18n_16646e46b1": "产物文件大小：", "i18n_16a3a4ed35": "模板标记", "i18n_16b5e7b472": "直接构建", "i18n_16f7fa08db": "吗？", "i18n_17006d4d51": "是否自动跳转到系统页面", "i18n_170fc8e27c": "周四", "i18n_174062da44": "分发方式", "i18n_1775ff0f26": "建议新增指定时间范围", "i18n_178ad7e9bc": "参数中的 id 、token 和触发构建一致、buildNumId 构建序号id", "i18n_17a101c23e": "孤独数据是指机器节点里面存在数据，但是无法和当前系统绑定上关系（关系绑定=节点ID+工作空间ID对应才行），一般情况下不会出现这样的数据", "i18n_17a74824de": "构建方式", "i18n_17acd250da": "下移", "i18n_17b4c9c631": "没有任何节点", "i18n_17b5e684e5": "需要到 节点管理中的【插件端配置】的授权配置中配置允许编辑的文件后缀", "i18n_17c06f6a8b": "最后执行时间", "i18n_17d444b642": "运行方式", "i18n_1810e84971": "才能使用 SSH 方式连接", "i18n_1818e9c264": "JVM总内存", "i18n_1819d0cdda": "如果开启同步到文件管理中心，在构建发布流程将自动执行同步到文件管理中心的操作。", "i18n_181e1ad17d": "长按可以拖动排序", "i18n_1857e7024c": "系统版本", "i18n_185926bf98": "全屏", "i18n_1862c48f72": "本地状态：", "i18n_1880b85dc5": "黑白 ambiance", "i18n_18b0ab4dd2": "机器SSH名", "i18n_18b34cf50d": "不滚动", "i18n_18c63459a2": "默认", "i18n_18c7e2556e": "如果当前构建信息已经在其他页面更新过，需要点击刷新按钮来获取最新的信息，点击刷新后未保存的数据也将丢失", "i18n_18d49918f5": "账号被锁定", "i18n_18eb76c8a0": "memory 最小 4M", "i18n_192496786d": "事件脚本", "i18n_19675b9d36": "清除代码(仓库目录)为删除服务器中存储仓库目录里面的所有东西,删除后下次构建将重新拉起仓库里面的文件,一般用于解决服务器中文件和远程仓库中文件有冲突时候使用。执行时间取决于源码目录大小和文件数量如超时请耐心等待，或稍后重试", "i18n_1974fe5349": "绑定成功", "i18n_197be96301": "待完善", "i18n_19f974ef6a": "开启差异发布并且开启清空发布时将自动删除项目目录下面有的文件但是构建产物目录下面没有的文件【清空发布差异上传前会先执行删除差异文件再执行上传差异文件】", "i18n_19fa0be4d2": " 官方文档", "i18n_19fcb9eb25": "时间", "i18n_1a2c905e87": "选择单位", "i18n_1a44b9e2f7": "同步到其他工作空间", "i18n_1a55f76ace": "构建命令，构建产物相对路径为：", "i18n_1a56bb2237": "至少选择一个节点和项目", "i18n_1a6aa24e76": "执行", "i18n_1a704f73c2": "请选择一个文件", "i18n_1a8f90122f": "提示信息 ", "i18n_1abf39bdb6": "# 将此目录缓存到全局（多个构建可以共享此缓存目录）", "i18n_1ad696efdc": "构建执行的命令(非阻塞命令)，如：mvn clean package、npm run build。支持变量：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_SOURCE_FILE}'}、{'${BUILD_NUMBER_ID}'}、仓库目录下 .env、工作空间变量", "i18n_1ae2955867": "指定 pom 文件打包 mvn -f xxx/pom.xml clean package", "i18n_1afdb4a364": "隐藏滚动条。纵向滚动方式提醒：滚轮，横行滚动方式：Shift+滚轮", "i18n_1b03b0c1ff": "已经分配到工作空间的 Docker 或者集群无法直接删除，需要到分配到的各个工作空间逐一删除后才能删除资产 Docker 或者集群", "i18n_1b38c0bc86": "备份文件存储目录：", "i18n_1b5266365f": "原始IP", "i18n_1b5bcdf115": "会话已经关闭[node-system-log]", "i18n_1b7cba289a": "数据统计", "i18n_1b8fff7308": "开启 MFA", "i18n_1b963fd303": "【推荐】腾讯身份验证码", "i18n_1b973fc4d1": "分组名称：", "i18n_1ba141c9ac": "请选择软链的项目", "i18n_1ba584c974": "配置容器", "i18n_1baae8183c": "是否解压", "i18n_1c040e6b87": "一般情况下不建议降级操作", "i18n_1c10461124": "示例：key,key1 或者 key=value,key1=value1", "i18n_1c13276448": "当前工作空间关联构建", "i18n_1c2e9d0c76": "没有任何构建", "i18n_1c3cf7f5f0": "关联", "i18n_1c61dfb86f": "挂载点", "i18n_1c8190b0eb": "请填写项目 DSL 配置内容,可以点击上方切换 tab 查看配置示例", "i18n_1c83d79715": "执行失败", "i18n_1c9d3cb687": "用户名ID", "i18n_1cc82866a4": "分片操作数", "i18n_1d0269cb77": "已经分配到工作空间的 SSH 无法直接删除，需要到分配到的各个工作空间逐一删除后才能删除资产 SSH", "i18n_1d263b7efb": "该选项仅本次构建生效", "i18n_1d38b2b2bc": "请选择项目授权路径", "i18n_1d53247d61": "请选择逻辑节点", "i18n_1d650a60a5": "硬盘", "i18n_1d843d7b45": "此节点暂无项目", "i18n_1dc518bddb": "项目存储的文件夹", "i18n_1dc9514548": "不等同于 PING 测试，此处测试成功表示网络一定通畅，此处测试失败网络不一定不通畅", "i18n_1de9b781bd": "使用容器构建，docker 容器所在的宿主机需要有公网,因为需要远程下载环境依赖的 sdk 和镜像", "i18n_1e07b9f9ce": "请选择要同步系统配置的机器节点", "i18n_1e4a59829d": "插件端开机自启", "i18n_1e5533c401": "配置目录", "i18n_1e5ca46c26": "排除发布 ANT 表达式,多个使用逗号分隔", "i18n_1e88a0cfaf": "不发布到 docker 集群", "i18n_1e93bdad2a": "搜索项目名", "i18n_1eb378860a": "真的要 Kill 这个进程么？", "i18n_1eba2d93fc": "禁用原因", "i18n_1ece1616bf": "如果插件端正常运行但是连接失败请检查端口是否开放,防火墙规则,云服务器的安全组入站规则", "i18n_1ed46c4a59": "分发名称（项目名称）", "i18n_1f08329bc4": "搜索命令名称", "i18n_1f0c93d776": " ：每分钟执行", "i18n_1f0d13a9ad": "服务端分发同步的脚本不能直接删除,需要到服务端去操作", "i18n_1f1030554f": "总计 {total} 条", "i18n_1f130d11d1": "SMTP 服务器", "i18n_1f4c1042ed": "文件夹", "i18n_1fa23f4daa": "过期时间", "i18n_1fd02a90c3": "用户", "i18n_200707a186": "创建后构建方式不支持修改", "i18n_2025ad11ee": "真的要解绑节点脚本么？", "i18n_2027743b8d": "系统名称:", "i18n_204222d167": "网络延迟", "i18n_2064fc6808": "不显示", "i18n_207243d77a": "如果要将工作空间分配给其他用户还需要到权限组管理", "i18n_207d9580c1": "表示周六", "i18n_209f2b8e91": "请输入登录密码", "i18n_20a9290498": "您来到系统管理中心", "i18n_20c8dc0346": "演示账号", "i18n_20e0b90021": "真的要删除监控么？", "i18n_20f32e1979": "角色：", "i18n_211354a780": "内的root只是外部的一个普通用户权限。默认false", "i18n_21157cbff8": "毫秒", "i18n_211a60b1d6": "编辑容器的一些基础参数", "i18n_2141ffaec9": "状态数据是异步获取有一定时间延迟", "i18n_2168394b82": "文件id,精准搜索", "i18n_2171d1b07d": "默认参数", "i18n_2191afee6e": "升级超时,请去服务器查看控制台日志排查问题", "i18n_21d81c6726": "为当前工作空间中的容器配置标签", "i18n_21da885538": "可以使用节点脚本：", "i18n_21dd8f23b4": "开源协议", "i18n_21e4f10399": "优先判断禁用时段", "i18n_21efd88b67": "暂无数据", "i18n_220650a1f5": "配置后将保存到当前构建", "i18n_2213206d43": "点击延迟可以查看对应节点网络延迟历史数据", "i18n_222316382d": "关联节点", "i18n_2223ff647d": "清空发布", "i18n_2245cf01a3": "您没有权限访问", "i18n_2246d128cb": "企业微信通知地址", "i18n_22482533ff": "私钥内容,不填将使用默认的 $HOME/.ssh 目录中的配置。支持配置文件目录:file:/xxxx/xx", "i18n_224aef211c": "构建信息", "i18n_224e2ccda8": "配置", "i18n_2256690a28": "节点ID：", "i18n_22670d3682": "请选择要使用的脚本", "i18n_226a6f9cdd": "请检查是否开启 ws 代理", "i18n_226b091218": "类型", "i18n_2296651945": "# 目前支持的 uses 插件端 java、maven、node、go、python3、gradle 。如果不满足需求，可自行配置插件", "i18n_22b03c024d": "二维码", "i18n_22c799040a": "容器", "i18n_22cf31df5d": "当前访问IP：", "i18n_22e4da4998": "表示项目当前未运行", "i18n_22e888c2df": "到期时间", "i18n_2300ad28b8": "读写", "i18n_2314f99795": "检测到新版本 ", "i18n_231f655e35": "当前程序打包时间：", "i18n_23231543a4": "修正", "i18n_2331a990aa": "扫码转账支持开源项目长期发展", "i18n_233fb56ab2": "在 设置-->安全设置-->私人令牌 中获取", "i18n_234e967afe": "发布前执行的命令(非阻塞命令),一般是关闭项目命令,支持变量替换：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_RESULT_FILE}'}、{'${BUILD_NUMBER_ID}'}", "i18n_2351006eae": "附加环境变量", "i18n_23559b6453": "# 将容器中的 maven 仓库文件缓存到 docker 卷中", "i18n_2356fe4af2": "配合脚本模版实现自定义项目管理", "i18n_2358e1ef49": "所属工作空间： ", "i18n_235f0b52a1": "发送错误", "i18n_23b38c8dad": "会话已经关闭[upgrade]", "i18n_23b444d24c": "快速配置", "i18n_23eb0e6024": "昵称", "i18n_242d641eab": "后缀", "i18n_2432b57515": "备注", "i18n_24384ba6c1": "确定要重新同步当前节点项目缓存信息吗？", "i18n_24384dab27": "请输入 value 的值", "i18n_244d5a0ed8": "构建参数", "i18n_2456d2c0f8": "如果容器以非零退出代码退出，则重新启动容器。可以指定次数：on-failure:2", "i18n_2457513054": "周六", "i18n_2482a598a3": "插件版本号", "i18n_248c9aa7aa": "构建状态", "i18n_2493ff1a29": "自定义进程类型", "i18n_2499b03cc5": "保留产物：", "i18n_249aba7632": "天", "i18n_24ad6f3354": "如果垮机器（资产机器）迁移之前机器中的项目数据仅是逻辑删除（项目文件和日志均会保留）", "i18n_24cc0de832": "执行命令", "i18n_24d695c8e2": "集群主机名", "i18n_250688d7c9": "发布失败", "i18n_250a999bb2": "容器标签,如：xxxx:latest 多个使用逗号隔开", "i18n_25182fb439": "工作空间菜单", "i18n_251a89efa9": "查看当前状态", "i18n_252706a112": "【推荐】微信小程序搜索 数盾OTP", "i18n_2527efedcd": "用户信息 url", "i18n_2560e962cf": "请选择分发项目", "i18n_257dc29ef7": "搜索配置参考", "i18n_25b6c22d8a": "为避免显示内容太多而造成浏览器卡顿,读取日志最后多少行日志。修改后需要回车才能重新读取，小于 1 则读取所有", "i18n_25be899f66": "筛选之后本次发布操作只发布筛选项,并且只对本次操作生效", "i18n_25c6bd712c": "请输入获取的计划运行次数", "i18n_25f29ebbe6": "脚本日志数：", "i18n_25f6a95de3": "确定要取消构建 【名称：", "i18n_2606b9d0d2": "分发机器", "i18n_260a3234f2": "请选择SSH", "i18n_2611dd8703": "当目标工作空间不存在对应的节点时候将自动创建一个新的节点（逻辑节点）", "i18n_26183c99bf": "文件中心", "i18n_2646b813e8": "登录密码", "i18n_267bf4bf76": "分发到节点中需要注意跨工作空间重名将被最后一次同步覆盖", "i18n_2684c4634d": "版本：", "i18n_26a3378645": "请选择运行方式", "i18n_26b5bd4947": "加载中...", "i18n_26bb841878": "新建", "i18n_26bd746dc3": "真的要清空项目目录和文件么？", "i18n_26c1f8d83e": "最后操作人", "i18n_26ca20b161": "来源", "i18n_26eccfaad1": "镜像：", "i18n_26f95520a5": "执行命令包含：", "i18n_26ffe89a7f": "项目名：", "i18n_27054fefec": "执行脚本传入参数有：startReady、pull、executeCommand、release、done、stop、success", "i18n_2770db3a99": "加载项目数据中...", "i18n_2780a6a3cf": "TLS 认证", "i18n_27b36afd36": "状态码为 0 的操作大部分为没有操作结果或者异步执行", "i18n_27ba6eb343": "网关：", "i18n_27ca568be2": "继续", "i18n_27d0c8772c": "如果误操作会产生冗余数据！！！", "i18n_27f105b0c3": "请选择要升级的节点", "i18n_280379cee4": "保存并关闭", "i18n_282c8cda1f": "如果上报的节点信息包含多个 IP 地址需要用户确认使用具体的 IP 地址信息", "i18n_288f0c404c": "清空", "i18n_28b69f9233": "构建镜像的过程不使用缓存", "i18n_28b988ce6a": "文件类型", "i18n_28bf369f34": "发布后的文件名是：文件ID.后缀，并非文件真实名称 （可以使用上传后脚本随意修改）", "i18n_28c1c35cd9": "主节点不能直接剔除", "i18n_28e0fcdf93": "还没有容器或者未配置标签不可以使用容器构建奥", "i18n_28e1c746f7": "ssh 名", "i18n_28e1eec677": "授权路径", "i18n_28f6e7a67b": "静态文件", "i18n_29139c2a1a": "文件名", "i18n_2926598213": "项目日志", "i18n_293cafbbd3": "裁剪", "i18n_2953a9bb97": "您需要创建一个账户用以后续登录管理系统,请牢记超级管理员账号密码", "i18n_295bb704f5": "语言", "i18n_29b48a76be": "请选择发布方式", "i18n_29efa328e5": "未分发", "i18n_2a049f4f5b": "分发失败", "i18n_2a0bea27c4": "执行域", "i18n_2a0c4740f1": "文件", "i18n_2a1d1da97a": "打包测试环境包 mvn clean package -Dmaven.test.skip=true -Ptest", "i18n_2a24902516": "集群ID：", "i18n_2a38b6c0ae": "未升级成功：", "i18n_2a3b06a91a": "虚拟MAC", "i18n_2a3e7f5c38": "手动", "i18n_2a6a516f9d": "填写运行命令", "i18n_2a813bc3eb": "立即下载", "i18n_2ad3428664": "请选择发布到集群的服务名", "i18n_2adbfb41e9": "参数如果传入", "i18n_2ae22500c7": "禁用时段", "i18n_2b04210d33": "进程号：", "i18n_2b0623dab9": "独立容器", "i18n_2b0aa77353": "您确定要启动当前容器吗？", "i18n_2b0f199da0": "不执行，也不编译测试用例 mvn clean package -Dmaven.test.skip=true", "i18n_2b1015e902": "参数描述没有实际作用", "i18n_2b21998b7b": "确定要关闭两步验证吗？关闭后账号安全性将受到影响,关闭后已经存在的 mfa key 将失效", "i18n_2b36926bc1": "没有任何构建历史", "i18n_2b4bb321d7": "内容区域主题切换", "i18n_2b4cf3d74e": "请选择要使用的构建", "i18n_2b52fa609c": "发生异常", "i18n_2b607a562a": "逐行执行", "i18n_2b696d1fec": "沉默时间", "i18n_2b6bc0f293": "操作", "i18n_2b788a077e": "等常用用户名，避免被其他用户有意或者无意操作造成登录失败次数过多从而超级管理员账号被异常锁定", "i18n_2b94686a65": "# 给容器新增环境变量", "i18n_2ba4c81587": "请输入邮箱地址", "i18n_2bb1967887": "请找我们授权，否则会有法律风险。", "i18n_2be2175cd7": "执行容器 标签", "i18n_2be75b1044": "全局", "i18n_2bef5b58ab": "不填写则不更新", "i18n_2c014aeeee": "打包时间", "i18n_2c5b0e86e6": "用户密码重置成功", "i18n_2c635c80ec": "发布操作是指,执行完构建命令后将构建产物目录中的文件用不同的方式发布(上传)到对应的地方", "i18n_2c74d8485f": "下载完成后需要手动选择更新到节点才能完成节点更新奥", "i18n_2c8109fa0b": "当前目录: ", "i18n_2c921271d5": "vue 项目(示例参考，具体还需要根据项目实际情况来决定)", "i18n_2cdbbdabf1": "构建产物目录,相对仓库的路径,如 java 项目的 target/xxx.jar vue 项目的 dist", "i18n_2cdcfcee15": "功能丰富 专为两步验证码", "i18n_2ce44aba57": "日志目录", "i18n_2d05c9d012": "关键词,支持正则", "i18n_2d2238d216": "账号新增成功", "i18n_2d3fd578ce": "确定要取批量消选中的构建吗？注意：取消/停止构建不一定能正常关闭所有关联进程", "i18n_2d455ce5cd": "下载中", "i18n_2d58b0e650": "选择构建的标签,不选为最新提交", "i18n_2d7020be7d": "比如常见的 .env 文件", "i18n_2d711b09bd": "内容", "i18n_2d842318fb": "周期", "i18n_2d94b9cf0e": "Dockerfile 构建方式不支持在这里回滚", "i18n_2d9569bf45": "参数值,新增默认参数后在手动执行脚本时需要填写参数值", "i18n_2d9e932510": "新增目录", "i18n_2de0d491d0": "小时", "i18n_2e0094d663": "真的要删除该集群信息么？1", "i18n_2e1f215c5d": "自动创建用户", "i18n_2e505d23f7": "下载导入模板", "i18n_2e51ca19eb": "如果节点选项是禁用，则表示对应数据有推荐关联节点（低版本项目数据可能出现此情况）", "i18n_2e740698cf": "集群IP", "i18n_2ea7e70e87": "命令文件将上传至 {'${user.home}'}/.jpom/xxxx.sh 执行完成将自动删除", "i18n_2ef1c35be8": "执行的 CPU", "i18n_2f4aaddde3": "删除", "i18n_2f5e828ecd": "别名码", "i18n_2f5e885bc6": "获取单个构建日志地址", "i18n_2f67a19f9d": "需要选发布到集群中的对应的服务名，需要提前去集群中创建服务", "i18n_2f6989595f": "管理列表：", "i18n_2f8d6f1584": "昨天", "i18n_2f8dc4fb66": "真的要释放分发信息么？释放之后节点下面的项目信息还会保留，如需删除项目还需要到节点管理中操作", "i18n_2f8fd34058": "脚本模版是存储在服务端中的命令脚本用于在线管理一些脚本命令，如初始化软件环境、管理应用程序等", "i18n_2f97ed65db": "占用", "i18n_2fc0d53656": "机器状态(缓存)", "i18n_2ff65378a4": "真的要删除对应工作空间的 SSH 么？", "i18n_2fff079bc7": "发布成功", "i18n_3006a3da65": "系统版本:", "i18n_300fbf3891": "发布前停止是指在发布文件到项目文件时先将项目关闭，再进行文件替换。避免 windows 环境下出现文件被占用的情况", "i18n_302ff00ddb": "超级管理员", "i18n_3032257aa3": "详情信息", "i18n_30849b2e10": "进程/端口", "i18n_30aaa13963": "序列号 (SN)", "i18n_30acd20d6e": "用户ID", "i18n_30d9d4f5c9": "新增关联", "i18n_30e6f71a18": "自定义标签通配表达式", "i18n_30e855a053": "取消分发", "i18n_30ff009ab3": "# java 镜像源 https://mirrors.tuna.tsinghua.edu.cn/Adoptium/", "i18n_3103effdfd": "请输入账号名", "i18n_31070fd376": "手动回滚", "i18n_310c809904": "绑定到当前工作空间", "i18n_312e044529": " ：范围：0(Sunday)~6(Saturday)，7也可以表示周日，同时支持不区分大小写的别名：\"sun\",\"mon\", \"tue\", \"wed\",\"thu\",\"fri\", \"sat\"，", "i18n_312f45014a": "创建时间：", "i18n_31353ecf96": " 下载授权码： ", "i18n_314f5aca4e": "单个触发器地址中：第一个随机字符串为构建ID，第二个随机字符串为 token", "i18n_315eacd193": "上移", "i18n_31691a647c": "{slot1}端口", "i18n_3174d1022d": "容器构建注意", "i18n_3181790b4b": "服务端系统配置", "i18n_318ce9ea8b": "用户密码提示", "i18n_31aaaaa6ec": "构建ID：", "i18n_31ac8d3a5d": "线程同步器", "i18n_31bca0fc93": "加入 beta 计划可以及时获取到最新的功能、一些优化功能、最快修复 bug 的版本，但是 beta版也可能在部分新功能上存在不稳定的情况。您需要根据您业务情况来评估是否可以加入 beta，在使用 beta版过程中遇到问题可以随时反馈给我们，我们会尽快为您解答。", "i18n_31eb055c9c": "并行度,同一时间升级的容器数量", "i18n_31ecc0e65b": "项目", "i18n_3200fba1c6": "下载源：", "i18n_32112950da": "批量取消", "i18n_3241c7c05f": "建议使用服务端脚本分发到脚本：", "i18n_32493aeef9": "构建中", "i18n_329e2e0b2e": "指定目录打包 yarn && yarn --cwd xxx build", "i18n_32a19ce88b": "控制台日志路径", "i18n_32ac152be1": "更新", "i18n_32c65d8d74": "标题", "i18n_32cb0ec70e": "请输入节点名称", "i18n_32d0576d85": "的令牌", "i18n_32dcc6f36e": "重启策略：no、always、unless-stopped、on-failure", "i18n_32e05f01f4": "集群信息", "i18n_32f882ae24": "匹配零个或多个字符", "i18n_330363dfc5": "成功", "i18n_3306c2a7c7": "读取默认", "i18n_33130f5c46": "操作成功", "i18n_3322338140": "请选择发布后操作", "i18n_332ba869d9": "一般用于节点环境一致的情况", "i18n_334a1b5206": "安装节点", "i18n_335258331a": "已经读取默认配置文件到编辑器中", "i18n_33675a9bb3": "集群关联的 docker 信息丢失,不能继续使用管理功能", "i18n_339097ba2e": "准备分发", "i18n_33c9e2388e": "项目ID", "i18n_3402926291": "当前日志文件大小：", "i18n_340eb70415": "日志编码格式", "i18n_346008472d": "匹配包含 异常 的行", "i18n_3477228591": "镜像", "i18n_35134b6f94": "查看节点脚本", "i18n_3517aa30c2": "脚本里面支持的变量有：{'${PROJECT_ID}'}、{'${PROJECT_NAME}'}、{'${PROJECT_PATH}'}", "i18n_353707f491": "可以到【节点分发】=>【分发授权配置】修改", "i18n_353c7f29da": "请选择模版节点", "i18n_35488f5ba8": "请选择节点项目", "i18n_354a3dcdbd": "30秒一次", "i18n_3574d38d3e": "剩余内存：", "i18n_35b89dbc59": "确认要下载最新版本吗？", "i18n_35cb4b85a9": "【目前只使用匹配到的第一项】", "i18n_35fbad84cb": "描述根据创建时间升序第一个", "i18n_3604566503": "请填写容器地址", "i18n_364bea440e": "请选择要引用的脚本", "i18n_368ffad051": "{slot1}目录", "i18n_36b3f3a2f6": "报警标题", "i18n_36b5d427e4": "请输入工作空间描述", "i18n_36d00eaa3f": "差异构建：", "i18n_36d4046bd6": "引用脚本模板", "i18n_36df970248": "# version 需要在对应镜像源中存在", "i18n_3711cbf638": "预占资源", "i18n_37189681ad": "数据Id", "i18n_373a1efdc0": "请选中要关闭的项目", "i18n_374cd1f7b7": "创建集群", "i18n_375118fad1": "物理节点脚本模板数据：", "i18n_375f853ad6": "硬件信息", "i18n_3787283bf4": "真的要删除当前文件么？", "i18n_37b30fc862": "请选择皮肤", "i18n_37c1eb9b23": "配置文件路径", "i18n_37f031338a": "上传压缩包并自动解压", "i18n_37f1931729": "数据目录占用空间：", "i18n_383952103d": "此分片上传是采用简单逻辑实现，当上传第一个分片时候使用覆盖模式，后续分片使用追加模式。如果上传中断对应的文件是一个非完整文件将无法正常使用。", "i18n_384f337da1": "同步机制采用", "i18n_3867e350eb": "环境变量", "i18n_386edb98a5": "自定义脚本项目（python、nodejs、go、接口探活、es）【推荐】", "i18n_38a12e7196": "选择证书文件", "i18n_38aa9dc2a0": "更多配置", "i18n_38ce27d846": "下一步", "i18n_38cf16f220": "确定", "i18n_38da533413": "下面命令将在", "i18n_3904bfe0db": "设置一个超级管理员账号", "i18n_3929e500e0": "通常情况为项目迁移工作空间、迁移物理机器等一些操作可能产生孤独数据", "i18n_396b7d3f91": "文件大小", "i18n_398ce396cd": "工作空间同步", "i18n_39b68185f0": "节点地址为插件端的 IP:PORT 插件端端口默认为：2123", "i18n_39c7644388": "端口号", "i18n_39e4138e30": "集群创建时间：", "i18n_39f1374d36": "耗时", "i18n_3a1052ccfc": "引用环境变量", "i18n_3a17b7352e": "分钟", "i18n_3a3778f20c": "任务ID", "i18n_3a3c5e739b": "批量构建参数", "i18n_3a3ff2c936": "卷标签", "i18n_3a536dcd7c": "126邮箱", "i18n_3a57a51660": "脚本版本:{item}", "i18n_3a6000f345": "正在运行的线程同步器", "i18n_3a6970ac26": "文件共享", "i18n_3a6bc88ce0": "真的要删除文件么？", "i18n_3a6c2962e1": "密钥算法", "i18n_3a71e860a7": "未开启当前终端", "i18n_3a94281b91": "自由脚本是指直接在机器节点中执行任意脚本", "i18n_3aa69a563b": "节点分发是指,一个项目运行需要在多个节点(服务器)中运行,使用节点分发来统一管理这个项目(可以实现分布式项目管理功能)", "i18n_3ac34faf6d": "通配符", "i18n_3adb55fbb5": "迁移工作空间", "i18n_3ae4c953fe": "当定时任务运行到的时间匹配这些表达式后，任务被启动。", "i18n_3ae4ddf245": "真的要删除该 Docker 么？删除只会检查本地系统的数据关联,不会删除 docker 容器中数据", "i18n_3aed2c11e9": "自动", "i18n_3b14c524f6": "读取次数", "i18n_3b19b2a75c": "真的要删除脚本么？", "i18n_3b885fca15": "缓存版本号", "i18n_3b9418269c": "请填写关联容器标签", "i18n_3b94c70734": "项目状态", "i18n_3ba621d736": "处理成功", "i18n_3baa9f3d72": "批量构建参数还支持指定参数,delay（延迟执行构建,单位秒）branchName（分支名）、branchTagName（标签）、script（构建脚本）、resultDirFile（构建产物）、webhook（通知webhook）", "i18n_3bc5e602b2": "邮箱", "i18n_3bcc1c7a20": "最后修改人", "i18n_3bdab2c607": "10分钟", "i18n_3bdd08adab": "描述", "i18n_3bf3c0a8d6": "节点", "i18n_3bf9c5b8af": " 分组名：", "i18n_3c014532b1": "构建耗时：", "i18n_3c070ea334": "如果关联的构建关联的仓库被多个构建绑定（使用）不能迁移", "i18n_3c48d9b970": "批量构建参数 BODY json： [ { \"id\":\"1\", \"token\":\"a\" } ]", "i18n_3c586b2cc0": "自定义 host", "i18n_3c6248b364": "缓存信息", "i18n_3c6fa6f667": "cron表达式", "i18n_3c8eada338": "请选择编码方式", "i18n_3c91490844": "发布操作", "i18n_3c99ea4ec2": "例如 2,3,6/3中，由于“/”优先级高，因此相当于2,3,(6/3)，结果与 2,3,6等价", "i18n_3c9eeee356": "真的要删除日志文件么？", "i18n_3cc09369ad": "真的要删除【", "i18n_3d06693eb5": "资源：", "i18n_3d0a2df9ec": "参数", "i18n_3d3b918f49": "执行构建", "i18n_3d3d3ed34c": "请输入选择关联分组", "i18n_3d43ff1199": "置顶", "i18n_3d48c9da09": "授权配置", "i18n_3d61e4aaf1": "指定标签", "i18n_3d6acaa5ca": "这个容器没有网络", "i18n_3d83a07747": "主机 Host", "i18n_3dc5185d81": "私有", "i18n_3dd6c10ffd": "上传升级包", "i18n_3e445d03aa": "文件不存在啦", "i18n_3e51d1bc9c": "请选择发布的SSH", "i18n_3e54c81ca2": "接收流量", "i18n_3e7ef69c98": "监控操作", "i18n_3e8c9c54ee": "选择分组", "i18n_3ea6c5e8ec": "分发结束", "i18n_3eab0eb8a9": "本地脚本", "i18n_3ed3733078": "终端日志", "i18n_3edddd85ac": "日", "i18n_3ee7756087": "请先选择节点", "i18n_3f016aa454": "镜像标签：", "i18n_3f18d14961": "两步验证码", "i18n_3f1d478da4": "服务端脚本、SSH脚本可以使用 G{'@'}(\"xxxx\") 格式来引用，当存在引用时系统会自动替换引用脚本库中的脚本内容", "i18n_3f2d5bd6cc": "在文件第 2 - 2 行中搜索", "i18n_3f414ade96": "参数描述,{slot1},仅是用于提示参数的含义", "i18n_3f553922ae": "】目录和文件么？", "i18n_3f5af13b4b": "# scriptId 可以是项目路径下脚本文件名或者系统中的脚本模版ID", "i18n_3f719b3e32": "冲突数", "i18n_3f78f88499": "打包时间：", "i18n_3f8b64991f": "解压时候自动剔除压缩包里面多余的文件夹名", "i18n_3f8cedd1d7": "用于静态文件绑定和读取(不建议配置大目录，避免扫描消耗过多资源)", "i18n_3fb2e5ec7b": "登录日志", "i18n_3fb63afb4e": "退出码", "i18n_3fbdde139c": "确认密码", "i18n_3fca26a684": "批量触发参数 BODY json： [ { \"id\":\"1\", \"token\":\"a\" } ]", "i18n_3fea7ca76c": "状态", "i18n_401c396b51": "日志编码格式是指项目日志文件的编码格式", "i18n_402d19e50f": "登录", "i18n_40349f5514": "数：", "i18n_4055a1ee9c": "通用的字段有：createTimeMillis、modifyTimeMillis", "i18n_406a2b3538": "何为孤独数据", "i18n_4089cfb557": "关联分组主要用于资产监控来实现不同服务端执行不同分组下面的资产监控", "i18n_40aff14380": "镜像ID", "i18n_40da3fb58b": "新建状态", "i18n_40f8c95345": "临时文件目录", "i18n_411672c954": "请输入文件描述", "i18n_412504968d": "当目标工作空间不存在对应的 SSH 时候将自动创建一个新的 SSH", "i18n_41298f56a3": "构建失败", "i18n_413d8ba722": "旧版程序包占有空间：", "i18n_413f20d47f": "系统 采用 oshi 库来监控系统，在 oshi 中使用 /proc/meminfo 来获取内存使用情况。", "i18n_41638b0a48": "用于区别文件是否为同一类型,可以针对同类型进行下载管理", "i18n_417fa2c2be": "参数{index}描述", "i18n_4188f4101c": "没有docker", "i18n_41d0ecbabd": "Block IO 权重", "i18n_41e8e8b993": "深色", "i18n_41e9f0c9c6": "工作节点", "i18n_41fdb0c862": "请先上传或者下载新版本", "i18n_4244830033": "请选择证书文件", "i18n_424a2ad8f7": "准备", "i18n_429b8dfb98": "项目分发", "i18n_429d4dbc55": "# 本示例仅供参考实际需要您按照仓库情况和构建流程自行配置", "i18n_42a93314b4": "基础镜像", "i18n_42b6bd1b2f": "仓库路径", "i18n_42f766b273": "挂载分区", "i18n_42fd64c157": "先启动", "i18n_4310e9ed7d": "请选择项目运行方式", "i18n_43250dc692": "触发器管理", "i18n_434d888f6f": "请选择文件中心的文件", "i18n_434d9bd852": "创建用户后自动关联上对应的权限组", "i18n_4360e5056b": "加载数据中", "i18n_436367b066": "项目管理", "i18n_4371e2b426": "请输入项目名称", "i18n_43886d7ac3": "新增运行参数", "i18n_4393b5e25b": "环回", "i18n_43c61e76e7": "注意：目前对 SSH key 访问 git 仓库地址不支持使用 ssh-keygen -t rsa -C", "i18n_43d229617a": "待选择", "i18n_43e534acf9": "宽松", "i18n_43ebf364ed": "请选择备份类型", "i18n_4403fca0c0": "清除", "i18n_44473c1406": "开启缓存构建目录将保留仓库文件,二次构建将 pull 代码, 不开启缓存目录每次构建都将重新拉取仓库代码(较大的项目不建议关闭缓存) 、特别说明如果缓存目录中缺失版本控制相关文件将自动删除后重新拉取代码", "i18n_4482773688": "请输入权限组名称", "i18n_44876fc0e7": "如果不可以选择则表示对应的用户没有配置邮箱", "i18n_449fa9722b": "为了考虑系统安全我们强烈建议超级管理员开启两步验证来确保账号的安全性", "i18n_44a6891817": "新增构建", "i18n_44c4aaa1d9": "运行模式", "i18n_44d13f7017": "限定时间", "i18n_44ed625b19": "网络异常", "i18n_44ef546ded": "项目监控 【暂不支持迁移】", "i18n_44efd179aa": "退出登录", "i18n_45028ad61d": "证书密码", "i18n_4524ed750d": "工作空间名", "i18n_456d29ef8b": "日志", "i18n_458331a965": "确认要上传文件更新到最新版本吗？", "i18n_45a4922d3f": "关联数据", "i18n_45b88fc569": "匹配路径中的零个或多个目录", "i18n_45f8d5a21d": "真的要删除用户么？", "i18n_45fbb7e96a": "项目孤独数据", "i18n_46032a715e": "还没有选择构建方式", "i18n_4604d50234": "错误信息", "i18n_46097a1225": "修正孤独数据", "i18n_46158d0d6e": "禁用监控", "i18n_461e675921": "当前数据为默认状态,操后上移或者下移可能不会达到预期排序,还需要对相关数据都操作后才能达到预期排序", "i18n_461ec75a5a": "路径：", "i18n_461fdd1576": "打包生产环境包 mvn clean package -Dmaven.test.skip=true -Pprod", "i18n_4637765b0a": "未启用", "i18n_463e2bed82": "批量更新", "i18n_4642113bba": "点击仪表盘查看监控历史数据", "i18n_4645575b77": "工作空间描述", "i18n_464f3d4ea3": "角色", "i18n_465260fe80": "年", "i18n_4696724ed3": "触发器", "i18n_46a04cdc9c": "文件描述：", "i18n_46aca09f01": "解绑会检查数据关联性,不会真实请求节点删除项目信息", "i18n_46ad87708f": "ssh名称", "i18n_46c8ba7b7f": "如果按钮不可用,请去资产管理 ssh 列表的关联中新增当前工作空间允许管理的授权文件夹", "i18n_46e3867956": "执行中", "i18n_46e4265791": "构建 ID", "i18n_4705b88497": "作用域", "i18n_47072e451e": "管理节点：", "i18n_470e9baf32": "允许执行的内存节点", "i18n_471c6b19cf": "迁移前您检查迁出机器和迁入机器的连接状态和网络状态避免未知错误或者中断造成流程失败产生冗余数据！！！！", "i18n_4722bc0c56": "终端", "i18n_473badc394": "发布的节点", "i18n_4741e596ac": "报警时间", "i18n_475a349f32": "当前构建还没有生成触发器", "i18n_475cd76aec": "统计的网卡：", "i18n_47768ed092": "极不安全", "i18n_47bb635a5c": "数据可能出现一定时间延迟", "i18n_47d68cd0f4": "服务", "i18n_47dd8dbc7d": "搜索项目ID", "i18n_47e4123886": "新增分发", "i18n_47ff744ef6": "编辑文件", "i18n_481ffce5a9": "匹配秒", "i18n_4826549b41": "命令模版是用于在线管理一些脚本命令，如初始化软件环境、管理应用程序等", "i18n_48281fd3f0": "真的要删除构建信息么？删除也将同步删除所有的构建历史记录信息", "i18n_4838a3bd20": "按住 Ctr 或者 Alt/Option 键点击按钮快速回到第一页", "i18n_4871f7722d": "任务更新时间", "i18n_48735a5187": "剩余空间(未分配)", "i18n_48a536d0bb": "修改容器配置，重新运行", "i18n_48d0a09bdd": "浅色", "i18n_48e79b3340": "】文件么？", "i18n_48fe457960": "(存在兼容问题,实际使用中需要提前测试) go sdk 镜像使用：https://studygolang.com/dl/golang/go{'${GO_VERSION}'}.linux-{'${ARCH}'}.tar.gz", "i18n_4956eb6aaa": "负载", "i18n_49574eee58": "确定要操作吗？", "i18n_49645e398b": "如果配置错误需要重启服务端并新增命令行参数 --rest:ip_config 将恢复默认配置", "i18n_497bc3532b": "JVM 参数", "i18n_497ddf508a": "新建空白文件", "i18n_498519d1af": "刷新数据", "i18n_499f058a0b": "退出登录成功", "i18n_49a9d6c7e6": "通过以下二维码进行一次性捐款赞助，请作者喝一杯咖啡☕️", "i18n_49d569f255": "请输入要检查的 host", "i18n_49e56c7b90": "确认修改", "i18n_4a00d980d5": "简单好用", "i18n_4a0e9142e7": "钉钉", "i18n_4a346aae15": "插件版本:", "i18n_4a4e3b5ae4": "描述：", "i18n_4a5ab3bc72": "操作：", "i18n_4a6f3aa451": " ：每个点钟的5分执行，00:05,01:05……", "i18n_4a98bf0c68": "任务详情", "i18n_4aac559105": "权重", "i18n_4ab578f3df": "环境变量：", "i18n_4ad6e58ebc": "机器SSH", "i18n_4af980516d": "为了您的账号安全系统要求必须开启两步验证来确保账号的安全性", "i18n_4b027f3979": "提醒", "i18n_4b0cb10d18": "请输入 SMTP host", "i18n_4b1835640f": "在 Settings-->Developer settings-->Personal access tokens 中获取", "i18n_4b386a7209": "获取变量值地址", "i18n_4b404646f4": "容器标签,如：key1=values1&keyvalue2", "i18n_4b5e6872ea": "驻留集", "i18n_4b96762a7e": "最后修改时间", "i18n_4b9c3271dc": "重置", "i18n_4ba304e77a": "钉钉账号登录", "i18n_4bbc09fc55": "在文件第 3 - 20 行中搜索", "i18n_4c096c51a3": "端口号：", "i18n_4c0eead6ff": "新增参数", "i18n_4c28044efc": "确认要将选中的  ", "i18n_4c69102fe1": "再判断允许时段。配置允许时段后用户只能在对应的时段执行相应功能的操作", "i18n_4c7c58b208": "请选择节点状态", "i18n_4c7e4dfd33": "当目标工作空间不存在对应的节点时候将自动创建一个新的docker（逻辑docker）", "i18n_4c83203419": "跳转到第三方系统中", "i18n_4c9bb42608": "前缀", "i18n_4cbc136874": "文件夹：", "i18n_4cbc5505c7": "差异构建是指构建时候是否判断仓库代码有变动，如果没有变动则不执行构建", "i18n_4ccbdc5301": "菜单", "i18n_4cd49caae4": "分发耗时", "i18n_4ce606413e": "仓库类型", "i18n_4cfca88db8": "选择分发文件", "i18n_4d18dcbd15": "真的要还原备份信息么？", "i18n_4d351f3c91": "禁止 IP", "i18n_4d49b2a15f": "自动执行：docker", "i18n_4d775d4cd7": "显示", "i18n_4d7dc6c5f8": "写", "i18n_4d85ac1250": "系统管理", "i18n_4d85c37f0d": "工作空间：", "i18n_4d9c3a0ed0": "Script 内容", "i18n_4dc781596b": "中使用了如下开源软件，我们衷心感谢有了他们的开源 Jpom 才能更完善", "i18n_4df483b9c7": "项目文件 ", "i18n_4e33dde280": "当前目录:", "i18n_4e54369108": "文件类型没有触发器功能", "i18n_4e7e04b15d": "服务名称必填", "i18n_4ed1662cae": "请选择连接方式", "i18n_4ee2a8951d": "接口响应 ContentType 均为：text/plain", "i18n_4ef719810b": "没有任何运行中的任务", "i18n_4effdeb1ff": "在文件第 1 - 2 行中搜索", "i18n_4f08d1ad9f": "算法 OID", "i18n_4f095befc0": "此配置仅对服务端管理生效, 工作空间的 ssh 配置需要单独配置", "i18n_4f35e80da6": "路径", "i18n_4f4c28a1fb": "文件内容格式要求：env_name=xxxxx 不满足格式的行将自动忽略", "i18n_4f50cd2a5e": "紧凑模式", "i18n_4f52df6e44": "关闭中", "i18n_4f8a2f0b28": "未运行", "i18n_4f8ca95e7b": "名", "i18n_4f9e3db4b8": "选择构建", "i18n_4fb2400af7": "容器是运行中可以进入终端", "i18n_4fb95949e5": "开启中", "i18n_4fdd2213b5": "项目 ID", "i18n_500789168c": "清空还原将会先删除项目目录中的文件再将对应备份文件恢复至当前目录", "i18n_5011e53403": "发布集群", "i18n_503660aa89": "排除：", "i18n_50411665d7": "保留个数", "i18n_50453eeb9e": "当前工作空间还没有逻辑节点不能创建节点脚本奥", "i18n_504c43b70a": "端口/PID", "i18n_5068552b18": "历史监控图表", "i18n_50940ed76f": "下载成功", "i18n_50951f5e74": "请选择分支", "i18n_50a299c847": "构建名称", "i18n_50c7929dd9": " 欢迎 ", "i18n_50d2671541": "确定是同一个脚本", "i18n_50ed14e70b": "深色 dracula", "i18n_50f472ee4e": "单位秒，默认 10 秒,最小 3 秒", "i18n_50f975c08e": "构建产物保留天数，小于等于 0 为跟随全局保留配置。注意自动清理仅会清理记录状态为：（构建结束、发布中、发布失败、发布失败）的数据避免一些异常构建影响保留个数", "i18n_50fb61ef9d": "脚本名称", "i18n_50fe3400c7": "真的要删除该执行记录吗？", "i18n_50fefde769": "是否为压缩包", "i18n_512e1a7722": "请选择操作者", "i18n_51341b5024": "服务端分发的脚本", "i18n_514b320d25": "如何选择构建方式", "i18n_5169b9af9d": "信息丢失", "i18n_5177c276a0": "集群不能手动创建，创建需要多个服务端使用通一个数据库，并且配置不同的集群 id 来自动创建集群信息", "i18n_518df98392": "从尾搜索", "i18n_5195c0d198": "可以管理{count}个工作空间", "i18n_51c92e6956": "同步系统配置", "i18n_51d47ddc69": "回调 url", "i18n_51d6b830d4": "在线构建目录", "i18n_52409da520": "联系人", "i18n_527466ff94": "请求参数", "i18n_527f7e18f1": "上传前请阅读更新日志里面的说明和注意事项并且更新前", "i18n_52a8df6678": "】文件夹么？", "i18n_52b526ab9e": "清空浏览器缓存配置将恢复默认", "i18n_52b6b488e2": "脚本模版是存储在节点(插件端),执行也都将在节点里面执行,服务端会定时去拉取执行日志,拉取频率为 100 条/分钟", "i18n_52c6af8174": "请输入客户端密钥 [clientSecret]", "i18n_52d24791ab": "真的要删除这些文件么？", "i18n_52eedb4a12": "报警方式", "i18n_52ef46c618": "不发布：只执行构建流程并且保存构建历史", "i18n_532495b65b": "副本数", "i18n_53365c29c8": "下载状态：", "i18n_534115e981": "信息不完整不能编辑", "i18n_5349f417e9": "搜关键词", "i18n_536206b587": "当前机器还未监控到任何数据", "i18n_537b39a8b5": "必填", "i18n_53bdd93fd6": "查看脚本库", "i18n_541e8ce00c": "关于开源软件", "i18n_542a0e7db4": "同步授权", "i18n_543296e005": "请输入授权 url [authorizationUri]", "i18n_543a5aebc8": "真的删除当前变量吗？", "i18n_543de6ff04": "分发状态消息", "i18n_54506fe138": "重置选择", "i18n_5457c2e99f": "# 使用 copy 文件的方式缓存，反之使用软链的形式。copy 文件方式缓存 node_modules 可以避免 npm WARN reify Removing non-directory", "i18n_547ee197e5": "新建目录", "i18n_5488c40573": "节点项目", "i18n_54f271cd41": "脚本模板", "i18n_5516b3130c": "飞书账号登录", "i18n_551e46c0ea": " 名称： ", "i18n_55405ea6ff": "导出", "i18n_556499017a": "项目文件会存放到", "i18n_5569a840c8": "请输入IP禁止,多个使用换行,支持配置IP段 ***********/*************,***********/24", "i18n_55721d321c": "参数描述", "i18n_55939c108f": "输入文件或者文件夹名", "i18n_55abea2d61": "服务端", "i18n_55b2d0904f": "在执行多节点分发时候使用 顺序重启、完整顺序重启 时候需要保证项目能正常重启", "i18n_55cf956586": "加入集群", "i18n_55d4a79358": "配置需要声明使用具体的 docker 来执行构建相关操作(建议使用服务端所在服务器中的 docker)", "i18n_55da97b631": " ，范围0~59，但是第一位不做匹配当为7位时，最后一位表示", "i18n_55e690333a": "当前工作空间还没有 Docker 集群", "i18n_55e99f5106": "钉钉通知地址", "i18n_55f01e138a": "微信赞赏", "i18n_56071a4fa6": "超时时间", "i18n_56230405ae": "解绑不会真实请求节点删除脚本信息", "i18n_562d7476ab": "周日", "i18n_56469e09f7": "请到【系统管理】-> 【资产管理】-> 【Docker管理】新增Docker，或者将已新增的Docker授权关联、分配到此工作空间", "i18n_56525d62ac": "扫描", "i18n_566c67e764": "已经分配到工作空间的机器无法直接删除，需要到分配到的各个工作空间逐一删除后才能删除资产机器", "i18n_5684fd7d3d": "账号新密码为：", "i18n_56bb769354": "下载前请阅读更新日志里面的说明和注意事项并且更新前", "i18n_56d9d84bff": "工作空间中逻辑节点中的项目数量：", "i18n_570eb1c04f": "硬盘占用率：", "i18n_5734b2db4e": "读取行数", "i18n_576669e450": "请选中要启动的项目", "i18n_5785f004ea": "请勿手动删除数据目录下面文件,如果需要删除需要提前备份或者已经确定对应文件弃用后才能删除", "i18n_578adf7a12": "请仔细确认后配置，ip配置后立即生效。配置时需要保证当前ip能访问！127.0.0.1 该IP不受访问限制.支持配置IP段 ***********/*************,***********/24", "i18n_578ca5bcfd": "163邮箱", "i18n_57978c11d1": "日志弹窗会非全屏打开", "i18n_579a6d0d92": "命令值", "i18n_57b7990b45": "当目标工作空间已经存在 SSH 时候将自动同步 SSH 账号、密码、私钥等信息", "i18n_57c0a41ec6": "当前数据为默认状态", "i18n_57cadc4cf3": "会使用 PING 检查", "i18n_5805998e42": "重启策略", "i18n_5854370b86": "跟踪文件", "i18n_585ae8592f": "重建容器", "i18n_5866b4bced": "集群数：", "i18n_587a63264b": "覆盖还原", "i18n_588e33b660": "账号如果开启 MFA(两步验证)，使用 Oauth2 登录不会验证 MFA(两步验证)", "i18n_589060f38e": "升级中，请稍候...", "i18n_5893fa2280": "邮箱账号", "i18n_58cbd04f02": "SSH 是指,通过 SSH 命令的方式对产物进行发布或者执行多条命令来实现发布(需要到 SSH 中提前去新增)", "i18n_58e998a751": "删除会检查数据关联性,并且节点不存在项目或者脚本", "i18n_58f9666705": "大小", "i18n_590b9ce766": "目前支持都插件有（更多插件尽情期待）：", "i18n_590dbb68cf": "结束时间：", "i18n_590e5b46a0": "自动备份", "i18n_592c595891": "开始时间", "i18n_5936ed11ab": "脚本库用于存储管理通用的脚本,脚本库中的脚本不能直接执行。", "i18n_593e04dfad": "菜单主题", "i18n_597b1a5130": "更新状态", "i18n_59a15a0848": "同步机制采用 IP+PORT+连接方式 确定是同一个服务器", "i18n_59c316e560": "分发文件", "i18n_59c75681b4": "通知对象", "i18n_59cf15fe6b": "模板", "i18n_59d20801e9": "在文件第 17 - 20 行中搜索", "i18n_5a0346c4b1": "编辑用户", "i18n_5a1367058c": "返回首页", "i18n_5a1419b7a2": "数据名称", "i18n_5a42ea648d": "自建 gitlab 访问地址", "i18n_5a5368cf9b": "密码错误", "i18n_5a63277941": "的值有：stop、beforeStop、start、beforeRestart、fileChange", "i18n_5a7ea53d18": "docker信息", "i18n_5a8727305e": "请不要优先退出管理节点", "i18n_5a879a657b": "交换内存", "i18n_5aabec5c62": "父级ID", "i18n_5ab90c17a3": "任务结束", "i18n_5ab9bf3591": " 是否需要使用 sudo 执行：docker system dial-stdio ", "i18n_5ad7f5a8b2": "结果", "i18n_5ae4a8f177": "请输入沉默时间", "i18n_5afe5e7ed4": "编辑关联项目", "i18n_5b1f0fd370": "用于创建节点分发项目、文件中心发布文件", "i18n_5b3ffc2910": "分发中", "i18n_5b47861521": "名称：", "i18n_5baaef6996": "点击重新同步当前工作空间逻辑节点脚本模版信息", "i18n_5badae1d90": "没有任何脚本", "i18n_5bb162ecbb": "JVM剩余内存", "i18n_5bb5b33ae4": "所以这里 我们", "i18n_5bca8cf7ee": "自定义host, xxx:192.168.0.x", "i18n_5bcda1b4d7": "会话已经关闭[system-log]", "i18n_5bd1d267a9": "在 preferences-->Access Tokens 中获取", "i18n_5c3b53e66c": "修改文件", "i18n_5c4d3c836f": "需要验证 MFA", "i18n_5c502af799": "容器名称必填", "i18n_5c56a88945": "停用", "i18n_5c89a5353d": "分配节点", "i18n_5c93055d9c": "一般用于服务器无法连接且已经确定不再使用", "i18n_5ca6c1b6c7": "请填写集群名称", "i18n_5cb39287a8": "监控功能", "i18n_5cc7e8e30a": "修改文件权限", "i18n_5d07edd921": "请填写集群IP", "i18n_5d14e91b01": "主要ID", "i18n_5d368ab0a5": "执行命令将自动替换为 sh 命令文件、并自动加载环境变量：/etc/profile、/etc/bashrc、~/.bashrc、~/.bash_profile", "i18n_5d414afd86": "从尾搜索、文件前2行、文件后3行", "i18n_5d459d550a": "处理中", "i18n_5d488af335": "远程下载文件", "i18n_5d5fd4170f": "的值有：1", "i18n_5d6f47d670": "项目为静态文件夹", "i18n_5d803afb8d": "不能和节点正常通讯", "i18n_5d817c403e": "没有选择任何数据", "i18n_5d83794cfa": "节点名称：", "i18n_5d9c139f38": "内容主题", "i18n_5dc09dd5bd": "重连 ", "i18n_5dc1f36a27": "证书描述", "i18n_5dc78cb700": "构建产物保留个数，小于等于 0 为跟随全局保留配置（如果数值大于 0 将和全局配置对比最小值来参考）。注意自动清理仅会清理记录状态为：（构建结束、发布中、发布失败、发布失败）的数据避免一些异常构建影响保留个数。 将在创建新的构建记录时候检查保留个数", "i18n_5dc7b04caa": "查看的进程数量", "i18n_5dff0d31d0": "如果需要定时自动执行则填写,cron 表达式.默认未开启秒级别,需要去修改配置文件中:[system.timerMatchSecond]）", "i18n_5e32f72bbf": "刷新文件表格", "i18n_5e46f842d8": "监控用户", "i18n_5e9f2dedca": "是否成功", "i18n_5ecc709db7": "执行时候默认不加载全部环境变量、需要脚本里面自行加载", "i18n_5ed197a129": "重置初始化在启动时候传入参数", "i18n_5ef040a79d": "丢弃包", "i18n_5ef72bdfce": "命令内容支持工作空间环境变量", "i18n_5effe31353": "剔除文件夹", "i18n_5f4c724e61": "请输入任务名", "i18n_5f5cd1bb1e": "新增关联项目是指,将已经在节点中创建好的项目关联为节点分发项目来实现统一管理", "i18n_5fafcadc2d": "会话已经关闭[node-script-consloe]", "i18n_5fbde027e3": "可以引用工作空间的环境变量 变量占位符 {'${xxxx}'} xxxx 为变量名称", "i18n_5fc6c33832": " 跳至行", "i18n_5fea80e369": "没有资产DOCKER", "i18n_5fffcb255d": "插件运行", "i18n_601426f8f2": "推送到仓库", "i18n_603dc06c4b": "您访问的页面不存在", "i18n_60585cf697": " 欢迎", "i18n_607558dbd4": "项目数", "i18n_607e7a4f37": "查看", "i18n_609b5f0a08": "时", "i18n_60b4c08f5c": "您确定要停止当前容器吗？", "i18n_6106de3d87": "JDK版本", "i18n_61341628ab": " ：表示列表", "i18n_6143a714d0": "编码格式", "i18n_616879745d": "凌晨0点和中午12点", "i18n_61955b0e4b": "没有项目状态以及控制等功能", "i18n_61a3ec6656": "介绍", "i18n_61bfa4e925": "需要在仓库里面 dockerfile,如果多文件夹查看可以指定二级目录如果 springboot-test-jar:springboot-test-jar/Dockerfile", "i18n_61c0f5345d": "SMTP 地址：【smtp.163.com, smtp.126.com...】，密码是邮箱授权码，端口默认 25，SSL 端口 465", "i18n_61e7fa1227": "编辑节点", "i18n_61e84eb5bb": "开始时间：", "i18n_620489518c": "参数{index}值", "i18n_620efec150": "更多开源说明", "i18n_62170d5b0a": "搜索参考", "i18n_6228294517": "菜单配置只对非超级管理员生效", "i18n_622d00a119": "执行脚本的路径", "i18n_624f639f16": "通用邮箱", "i18n_625aa478e2": "从尾搜索、文件前0行、文件后3行", "i18n_625fb26b4b": "取消", "i18n_627c952b5e": "总空间", "i18n_6292498392": " 查找下一个", "i18n_629a6ad325": "安全管理", "i18n_629f3211ca": "修剪类型", "i18n_631d5b88ab": "请输入项目存放路径授权，回车支持输入多个路径，系统会自动过滤 ../ 路径、不允许输入根路径", "i18n_632a907224": "重置为重新生成触发地址,重置成功后之前的触发器地址将失效,触发器绑定到生成触发器到操作人上,如果将对应的账号删除触发器将失效", "i18n_6334eec584": "5秒一次", "i18n_635391aa5d": "下载产物", "i18n_637c9a8819": "至少选择1个节点项目", "i18n_638cddf480": "创建人,全匹配", "i18n_639fd37242": "目前使用的 docker swarm 集群，需要先创建 swarm 集群才能选择", "i18n_63b6b36c71": "选择证书", "i18n_63c9d63eeb": "可以同时展开多个菜单", "i18n_63dd96a28a": "密码支持引用工作空间变量：", "i18n_63e975aa63": "安装ID:", "i18n_640374b7ae": "挂载卷", "i18n_641796b655": "构建完成", "i18n_6428be07e9": "配置系统公告", "i18n_643f39d45f": "非悬空", "i18n_6446b6c707": "昵称长度为2-10", "i18n_646a518953": "请输入项目ID", "i18n_6470685fcd": "：表示匹配这个位置任意的时间（与\"*\"作用一致）", "i18n_649231bdee": "文件后缀", "i18n_64933b1012": "存储选项", "i18n_6496a5a043": "命令名称", "i18n_649d7fcb73": "新集群需要手动配置集群管理资产分组、集群访问地址", "i18n_649d90ab3c": "关闭右侧", "i18n_649f8046f3": "请选择SSH节点", "i18n_64c083c0a9": "结果描述", "i18n_64eee9aafa": "开机时间", "i18n_652273694e": "主机", "i18n_65571516e2": "构建备注：", "i18n_657969aa0f": "编辑  Docker", "i18n_657f3883e3": "不执行发布流程", "i18n_65894da683": "发布方式:", "i18n_65cf4248a8": "不能初始化", "i18n_65f66dfe97": "清空当前缓冲区内容", "i18n_66238e0917": "已经存在的账号与外部系统账号不一致时不支持绑定外部系统账号", "i18n_663393986e": "解绑", "i18n_6636793319": "真的要删除节点么？删除会检查数据关联性,并且节点不存在项目或者脚本", "i18n_664b37da22": "备份", "i18n_664c205cc3": "真的要清除仓库隐藏字段信息么？（密码，私钥）", "i18n_667fa07b52": "个节点升级到", "i18n_66aafbdb72": "最新构建ID", "i18n_66ab5e9f24": "新增", "i18n_66b71b06c6": "上传压缩文件（自动解压）", "i18n_66c15f2815": "匹配包含数字的行", "i18n_66e9ea5488": "日志名称", "i18n_6707667676": "主机名", "i18n_6709f4548f": "随机生成", "i18n_67141abed6": "项目授权路径+项目文件夹", "i18n_67425c29a5": "超时时间(s)", "i18n_674a284936": "当isMatchSecond为 true 时才会匹配秒部分默认都是关闭的", "i18n_674e7808b5": "mfa 验证码", "i18n_679de60f71": "请填写日志项目名称", "i18n_67aa1c0169": "请填写构建命令", "i18n_67aa2d01b9": "工作空间的菜单、环境变量、节点分发授权需要逐一配置", "i18n_67b667bf98": "部分备份", "i18n_67e3d3e09c": "批量构建", "i18n_67e7f9e541": "监控周期", "i18n_6816da19f3": "关闭其他", "i18n_6835ed12b9": "环境变量的key", "i18n_685e5de706": "容器构建", "i18n_6863e2a7b5": "脚本执行历史", "i18n_686a19db6a": "自动删除", "i18n_68a1faf6e2": "批量构建传入其他参数将同步执行修改", "i18n_68af00bedb": "表格视图才能使用工作空间同步功能", "i18n_68c55772ca": "请输入授权方的网页应用ID", "i18n_69056f4792": "部分操作状态码可能为 0", "i18n_690a3d1a69": "执行容器", "i18n_691b11e443": "当前工作空间", "i18n_6928f50eb3": "支持配置系统参数：", "i18n_69384c9d71": "点击查看历史趋势", "i18n_693a06987c": "请填写用户账号", "i18n_6948363f65": "取消定时,不再定时执行（支持 ! 前缀禁用定时执行，如：!0 0/1 * * * ?）", "i18n_694fc5efa9": "刷新", "i18n_695344279b": "文件上传id生成失败：", "i18n_6953a488e3": "选择逻辑节点", "i18n_697d60299e": "检测到当前已经登录账号", "i18n_69c3b873c1": "本地构建", "i18n_69c743de70": "节点的IP", "i18n_69de8d7f40": "还原", "i18n_6a359e2ab3": "scriptId也可以引入脚本库中的脚本,需要提前同步至机器节点中", "i18n_6a49f994b1": "构建过程执行对应的脚本,开始构建,构建完成,开始发布,发布完成,构建异常,发布异常", "i18n_6a4a0f2b3b": "同步机制采用节点地址确定是同一个服务器（节点）", "i18n_6a588459d0": "工作空间名称", "i18n_6a620e3c07": "同步", "i18n_6a658517f3": "任务日志", "i18n_6a66d4cdf3": "延迟,容器回滚间隔时间", "i18n_6a6c857285": "分发节点", "i18n_6a8402afcb": "解析文件,准备上传中 ", "i18n_6a8c30bd06": "加载编辑器中", "i18n_6a922e0fb6": "插件端端口", "i18n_6a9231c3ba": "函数 args 参数，非必填", "i18n_6aa7403b18": "如果使用 SSH 方式但是 SSH 无法选择，是表示系统没有监测到 docker 服务", "i18n_6aab88d6a3": "保存并重启", "i18n_6ab78fa2c4": "邮箱地址", "i18n_6ac61b0e74": "建议还原和当前版本一致的文件或者临近版本的文件", "i18n_6ad02e7a1b": "页面资源加载中....", "i18n_6adcbc6663": "配置方式：SSH列表->操作栏中->关联按钮->对应工作空间->操作栏中->配置按钮", "i18n_6af7686e31": "分钟刷新一次", "i18n_6b0bc6432d": "操作者", "i18n_6b189bf02d": "容器数：", "i18n_6b29a6e523": "启动项目", "i18n_6b2e348a2b": "定时执行", "i18n_6b46e2bfae": "真的当前工作空间么", "i18n_6b4fd0ca47": "支持配置发送方：遵循RFC-822标准 发件人可以是以下形式：", "i18n_6b6d6937d7": "163邮箱 SSL", "i18n_6bb5ba7438": "限制禁止在在线终端执行的命令", "i18n_6be30eaad7": "请输入超时时间", "i18n_6bf1f392c0": "当前状态", "i18n_6c08692a3a": "密码若没修改可以不用填写", "i18n_6c14188ba0": "不能下载目录", "i18n_6c24533675": "请选择一位报警联系人或者填写webhook", "i18n_6c72e9d9de": "编辑分发项目", "i18n_6c776e9d91": "项目启动,停止,重启,文件变动都将请求对应的地址,非必填，GET请求", "i18n_6d110422ce": "（需要选择以什么方式去重存储发布模板，默认每一种方式只会存储保留最新的发布模板,别名模板优先级最高）", "i18n_6d5f0fb74b": "镜像构建成功后是否需要推送到远程仓库", "i18n_6d68bd5458": "全量备份", "i18n_6d7f0f06be": "请选择发布操作", "i18n_6d802636ab": "隐私", "i18n_6da242ea50": "任务Id", "i18n_6dcf6175d8": "现在就去", "i18n_6de1ecc549": "查看服务端脚本", "i18n_6e02ee7aad": "周期的长度，以微秒为单位。", "i18n_6e2d78a20e": "从尾搜索、文件前20行、文件后3行", "i18n_6e60d2fc75": "页面启用紧凑模式", "i18n_6e69656ffb": "文件不能为空", "i18n_6e70d2fb91": "构建参数,如：key1=value1&key2=value2", "i18n_6ea1fe6baa": "基础信息", "i18n_6eb39e706c": "编辑机器", "i18n_6ef90ec712": "请填写要拉取的镜像名称", "i18n_6f15f0beea": "两次密码不一致...", "i18n_6f32b1077d": "请输入工作空间备注,留空使用默认的名称", "i18n_6f5b238dd2": " SSH、本地命令发布都执行变量替换,系统预留变量有：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_RESULT_FILE}'}、{'${BUILD_NUMBER_ID}'}", "i18n_6f6ee88ec4": "支持开源", "i18n_6f73c7cf47": "保留天数：", "i18n_6f7ee71e77": "静态目录", "i18n_6f854129e9": "分组/标签", "i18n_6f8907351b": "同步节点配置", "i18n_6f8da7dcca": "节点地址格式为：IP:PORT (示例：***********00:2123)", "i18n_6f9193ac80": "启用两步验证", "i18n_6fa1229ea9": "一键分发同步多个节点的授权配置", "i18n_6ffa21d235": "分发节点是指将变量同步到对应节点，在节点脚本中也可以使用当前变量", "i18n_7006410585": "无论返回什么退出代码，始终重新启动容器。", "i18n_7010264d22": "没有开启任何认证", "i18n_702430b89d": "页面启用宽松模式", "i18n_702afc34a0": "差异发布：", "i18n_7030ff6470": "错误", "i18n_7035c62fb0": "账号", "i18n_704f33fc74": "从头搜索、文件前0行、文件后3行", "i18n_706333387b": "此功能不能保证新增的容器和之前容器参数完全一致请慎重使用。", "i18n_7088e18ac9": "卷", "i18n_708c9d6d2a": "请选择", "i18n_70a6bc1e94": "当前系统已经初始化过啦,不能重复初始化", "i18n_70b3635aa3": "执行时间", "i18n_70b5b45591": "快速安装", "i18n_70b9a2c450": "真的要退出系统么？", "i18n_710ad08b11": "禁用", "i18n_7114d41b1d": "超级管理员没有任何限制", "i18n_712cdd7984": "合作咨询", "i18n_713c986135": "容器构建会在 docker 中生成相关挂载目录,一般情况不需要人为操作", "i18n_7156088c6e": "编码方式", "i18n_71584de972": "非服务器开机自启,如需开机自启建议配置", "i18n_715ec3b393": "用于快捷同步其他机器节点的配置", "i18n_7173f80900": "拒绝", "i18n_71a2c432b0": "编辑变量", "i18n_71bbc726ac": "跟随系统", "i18n_71c6871780": "定时任务表达式", "i18n_71dc8feb59": "未配置", "i18n_71ee088528": "桥接模式：", "i18n_7220e4d5f9": "方式", "i18n_7229ecc631": "次", "i18n_7293bbb0ff": "总 inode 数", "i18n_729eebb5ff": "没有对应的SSH", "i18n_72d14a3890": "请选择用户的权限组", "i18n_72d46ec2cf": "登录信息过期", "i18n_72d4ade571": ",仅是用于提示参数的含义", "i18n_72e7a5d105": "镜像id", "i18n_72eae3107e": "灰绿 abbott", "i18n_72ebfe28b0": "定时", "i18n_7307ca1021": "自动启动", "i18n_7327966572": "彻底删除", "i18n_7329a2637c": "集群ID", "i18n_73485331c2": "文件信息", "i18n_73578c680e": "数据目录是指程序在运行过程中产生的文件以及数据存储目录", "i18n_73651ba2db": "批量重启", "i18n_7370bdf0d2": "脚本日志", "i18n_738a41f965": "项目名称", "i18n_73a87230e0": "文件系统类型", "i18n_73b7b05e6e": " 将脚本分发到对应的机器节点中，对应的机器节点可以引用对应的脚本 ", "i18n_73b7e8e09e": "在使用 beta 版过程中遇到问题可以随时反馈给我们，我们会尽快为您解答。", "i18n_73c980987a": "加载容器可用标签中....", "i18n_73d8160821": "以 yaml/yml 格式配置,scriptId 为项目路径下的脚本文件的相对路径或者脚本模版ID，可以到脚本模版编辑弹窗中查看 scriptId", "i18n_73ed447971": "强烈建议您使用 TLS 证书", "i18n_73f798a129": "免费社群", "i18n_7457228a61": "远程下载地址", "i18n_74bdccbb5d": "我的工作空间", "i18n_74c5c188ae": "操作成功接口 HTTP 状态码为 200", "i18n_74d5f61b9f": "构建触发", "i18n_74d980d4f4": "在单页列表里面 file 类型项目将自动排序到最后", "i18n_74dc77d4f7": "容器id", "i18n_74dd7594fc": "发生报警时候请求", "i18n_74ea72bbd6": "集群管理", "i18n_751a79afde": "30分钟", "i18n_7527da8954": "普通用户", "i18n_7548ea6316": "点击可以折叠左侧菜单栏", "i18n_75528c19c7": "自动重启", "i18n_7561bc005e": "构建过程请求,非必填，GET请求", "i18n_75769d1ac8": "读", "i18n_757a730c9e": "无法连接", "i18n_758edf4666": "从头搜索、文件前2行、文件后3行", "i18n_75c63f427a": "此选项为一个实验属性实际效果基本无差异", "i18n_75fc7de737": "路由", "i18n_7617455241": "文件中如果存在：MemAvailable、MemTotal 这两个字段，那么 oshi 直接使用，所以本系统 中内存占用计算方式：内存占用=(total-available)/total", "i18n_762e05a901": "差异发布是指对应构建产物和项目文件夹里面的文件是否存在差异,如果存在增量差异那么上传或者覆盖文件。", "i18n_7650487a87": "地址", "i18n_76530bff27": "请输入私人令牌", "i18n_7653297de3": "跳转", "i18n_765592aa05": "如果未挂载容器数据目录请提前备份数据后再使用此功能。", "i18n_765d09eea5": "当前文件不可读,需要配置可读文件授权", "i18n_767fa455bb": "目录", "i18n_768e843a3e": "类 192", "i18n_769d88e425": "完成", "i18n_76aebf3cc6": "日志大小", "i18n_76ebb2be96": "1分钟", "i18n_77017a3140": "关联容器标签", "i18n_770a07d78f": "当目标工作空间不存在对应的 脚本 时候将自动创建一个新的 脚本", "i18n_771d897d9a": "状态码", "i18n_77373db7d8": "接收报警消息,非必填，GET请求", "i18n_7737f088de": "批量重新启动", "i18n_773b1a5ef6": "请选择语言模式", "i18n_775fde44cf": "进程端口缓存：", "i18n_7760785daf": "自由脚本", "i18n_7764df7ccc": "开启差异发布但不开启清空发布时相当于只做增量和变动更新", "i18n_77688e95af": "容器重建是指使用已经创建的容器参数重新创建一个相同的容器。", "i18n_776bf504a4": "上传提示", "i18n_7777a83497": "请输入构建备注,长度小于 240", "i18n_77834eb6f5": "您使用本系統", "i18n_7785d9e038": "低版本项目数据未存储节点ID，对应项目数据也将出来在孤独数据中（此类数据不影响使用）", "i18n_77b9ecc8b1": "备份名称", "i18n_77c1e73c08": "脚本存放路径：{'${user.home}'}/.jpom/xxxx.sh，执行脚本路径：{'${user.home}'}，执行脚本方式：bash {'${user.home}'}/.jpom/xxxx.sh par1 par2", "i18n_77c262950c": "使用 Access Token 一次导入多个项目", "i18n_77e100e462": "还没有状态消息", "i18n_77e501b44b": "日志文件：", "i18n_780afeac65": "是否开启", "i18n_780fb9f3d0": "更新时间：", "i18n_7824ed010c": "真的取消当前发布任务吗？", "i18n_7854b52a88": "启用", "i18n_787fdcca55": "系统配置", "i18n_788a3afc90": "已失联", "i18n_78a4b837e3": "能通讯的IP", "i18n_78b2da536d": "构建过程请求对应的地址,开始构建,构建完成,开始发布,发布完成,构建异常,发布异常", "i18n_78ba02f56b": "真的要彻底删除分发信息么？删除后节点下面的项目也都将彻底删除，彻底项目会自动删除项目相关文件奥(包含项目日志，日志备份，项目文件)", "i18n_78caf7115c": "任务名称", "i18n_78dccb6e97": "所有节点(插件端)", "i18n_79076b6882": "真的要批量删除这些构建信息么？删除也将同步删除所有的构建历史记录信息，如果中途删除失败将终止删除操作", "i18n_7912615699": "连接状态", "i18n_791870de48": "仓库密码", "i18n_791b6d0e62": "排名按照字母 a-z 排序", "i18n_79698c57a2": "当前工作空间还没有节点", "i18n_798f660048": "模版节点", "i18n_799ac8bf40": "支持变量引用：{'${TASK_ID}'}、{'${FILE_ID}'}、{'${FILE_NAME}'}、{'${FILE_EXT_NAME}'}、{'${RELEASE_PATH}'}", "i18n_79a7072ee1": "令牌 url", "i18n_79c6b6cff7": "关联分组", "i18n_79d3abe929": "复制", "i18n_7a28e9cd4a": "当监控到持续异常时监控通知发送成功后在一段时间内不重复发送报警通知", "i18n_7a30792e2a": "编辑 SSH", "i18n_7a3c815b1e": "文件目录", "i18n_7a4ecc606c": "镜像标签,如：key1=values1&keyvalue2 使用 URL 编码", "i18n_7a5dd04619": "注意执行相关命令需要所在服务器中存在对应的环境", "i18n_7a7e25e9eb": "确定要将此数据下移吗？下移操作可能因为列表后续数据没有排序值操作无效！", "i18n_7a811cc1e5": "复制 ", "i18n_7a93e0a6ae": "选择企业版本或者购买授权：", "i18n_7aa81d1573": "请输入文件名称", "i18n_7aaee3201a": "如果需要删除需要提前备份或者已经确定对应文件弃用后才能删除 !!!!", "i18n_7afb02ed93": "当前没有可以引用的环境变量", "i18n_7b2cbfada9": "发布前停止：", "i18n_7b36b18865": "分区ID", "i18n_7b61408779": "# 项目文件备份路径", "i18n_7b8e7d4abc": "真的要删除执行记录么？", "i18n_7b961e05d0": "表示月的最后一天", "i18n_7bcbf81120": "接收包", "i18n_7bcc3f169c": "# 内置变量 {'${JPOM_WORKING_DIR}'} {'${JPOM_BUILD_ID}'}", "i18n_7bf62f7284": "手动取消分发", "i18n_7c0ee78130": "构建日志", "i18n_7c223eb6e9": "发布系统公告", "i18n_7c9bb61536": "日志项目名称", "i18n_7cb8d163bb": "变量名称", "i18n_7cc3bb7068": "不会真实请求节点删除项目信息", "i18n_7ce511154f": "创建之后不能修改", "i18n_7d23ca925c": "服务端时间", "i18n_7d3f2fd640": "在文件第 3 - 2147483647 行中搜索", "i18n_7ddbe15c84": "网络", "i18n_7dde69267a": "未绑定集群的分组：", "i18n_7de5541032": "如果 ssh 没有配置授权目录是不能选择的哟", "i18n_7dfc7448ec": "真的要删除仓库信息么？", "i18n_7dfcab648d": "产物", "i18n_7e000409bb": "容易出现挖矿情况", "i18n_7e1b283c57": " 添加", "i18n_7e2b40fc86": "选择节点", "i18n_7e300e89b1": "分发成功", "i18n_7e33f94952": "，如果想要切换路径后执行命令则需要", "i18n_7e359f4b71": "硬盘总量：", "i18n_7e58312632": "编辑日志搜索", "i18n_7e866fece6": "请输入两步验证码", "i18n_7e930b95ef": "发布文件", "i18n_7e951d56d9": "操作时间", "i18n_7e9f0d2606": "项目是指,节点中的某一个项目,需要提前在节点中创建项目", "i18n_7ef30cfd31": "附加环境变量是指读取仓库指定环境变量文件来新增到执行构建运行时", "i18n_7f0abcf48d": "需要到编辑中去为一个节点绑定一个 ssh信息才能启用该功能", "i18n_7f3809d36b": "构建结束", "i18n_7f5bcd975b": "cpu占用", "i18n_7f7c624a84": "批量操作", "i18n_7f7ee903da": "发布隐藏文件", "i18n_7fb5bdb690": "软件致谢", "i18n_7fb62b3011": "批量删除", "i18n_7fbc0f9aae": "执行时间开始", "i18n_7fc88aeeda": "修改密码", "i18n_800dfdd902": "今天", "i18n_80198317ff": "并向您的朋友推荐或分享：", "i18n_8023baf064": "通知状态", "i18n_80669da961": "CPU占用", "i18n_807ed6f5a6": "暂无任何数据", "i18n_8086beecb3": "标签名称：", "i18n_808c18d2bb": "值为 true 表示项目当前为运行中", "i18n_809b12d6a0": "请耐心等待暂时不用刷新页面", "i18n_80cfc33cbe": "确认重置", "i18n_81301b6813": "打开终端", "i18n_81485b76d8": "请输入主机地址", "i18n_814dd5fb7d": "真的要删除备份信息么？", "i18n_815492fd8d": "旧版程序包占有空间", "i18n_8160b4be4e": "异常关闭", "i18n_819767ada1": "用户名", "i18n_8198e4461a": "项目:", "i18n_81afd9e713": "队列等待", "i18n_81c1dff69c": "解决办法", "i18n_81d7d5cd8a": "命令详细描述", "i18n_81e4018e9d": "悬空类型", "i18n_82416714a8": "需要测试的端口", "i18n_824607be6b": "保留天数", "i18n_824914133f": "没有任何脚本库", "i18n_8283f063d7": "项目完整目录", "i18n_828efdf4e5": "开启MFA数", "i18n_82915930eb": "并发执行", "i18n_829706defc": "在线构建（构建关联仓库、构建历史）", "i18n_829abe5a8d": "分组", "i18n_82b89bd049": "日志弹窗会全屏打开", "i18n_82d2c66f47": "批量分配", "i18n_8306971039": "所属用户", "i18n_8309cec640": "请选择节点项目,可能是节点中不存在任何项目,需要去节点中创建项目", "i18n_833249fb92": "当前文件用时", "i18n_8347a927c0": "修改", "i18n_835050418f": "确认要上传最新的插件包吗？", "i18n_8351876236": "别名 ", "i18n_83611abd5f": "发布", "i18n_8363193305": "请输入回调重定向 url [redirectUri]", "i18n_8388c637f6": "自启动", "i18n_83aa7f3123": "分发id", "i18n_83c61f7f9e": "请选择监控用户", "i18n_83ccef50cd": "当目标工作空间已经存在 脚本 时候将自动同步 脚本内容、默认参数、定时执行、描述", "i18n_83f25dbaa0": "绑定节点", "i18n_8400529cfb": "重置自定义的进程名信息", "i18n_8432a98819": "操作功能", "i18n_843f05194a": "显示所有", "i18n_84415a6bb1": "重置下载 token 信息,重置后之前的下载 token 将失效", "i18n_844296754e": "虚拟内存", "i18n_84592cd99c": "可以理解为项目打包的目录。 如 Jpom 项目执行（构建命令）", "i18n_84597bf5bc": "阿里云企业邮箱配置", "i18n_84632d372f": "点击查看详情", "i18n_84777ebf8b": "安全提醒", "i18n_847afa1ff2": "请输入IP授权,多个使用换行,0.0.0.0 是开放所有IP,支持配置IP段 ***********/*************,***********/24", "i18n_848c07af9b": "管理面板", "i18n_848e4e21da": "如：--server", "i18n_8493205602": "开", "i18n_84aa0038cf": "系统日志", "i18n_84b28944b7": "超时时间(S)", "i18n_84d331a137": "秒 (值太小可能会取不到节点状态)", "i18n_84e12f7434": "会话已经关闭[ssh-terminal]", "i18n_853d8ab485": "正在构建", "i18n_85451d2eb5": "请输入变量值", "i18n_8580ad66b0": "真的要彻底删除项目么？彻底项目会自动删除项目相关文件奥(包含项目日志，日志备份，项目文件)", "i18n_85be08c99a": "未查询到任何数据", "i18n_85cfcdd88b": "本地构建是指直接在服务端中的服务器执行构建命令", "i18n_85da2e5bb1": "重启中，请稍候...", "i18n_85ec12ccd3": "延迟,容器升级间隔时间", "i18n_85f347f9d0": "用户限制用户只能对应的工作空间里面操作对应的功能", "i18n_85fe5099f6": "集群", "i18n_86048b4fea": "移除", "i18n_860c00f4f7": "每小时", "i18n_863a95c914": "真的要保存当前配置吗？如果配置有误,可能无法启动服务需要手动还原奥！！！", "i18n_867cc1aac4": " ：范围：0~23", "i18n_869b506d66": "独立的项目分发请到分发管理中去修改", "i18n_869ec83e33": "未使用", "i18n_86b7eb5e83": "删除前需要将关联数据都删除后才能删除当前工作空间？", "i18n_86c1eb397d": "切换账号", "i18n_86cd8dcead": "启动时间", "i18n_86e9e4dd58": "仓库lastcommit", "i18n_86f3ec932c": "读取大小", "i18n_86fb7b5421": "节点账号", "i18n_8704e7bdb7": "请输入令牌 url [accessTokenUri]", "i18n_871cc8602a": "二级目录", "i18n_8724641ba8": "间隔（/） &gt; 区间（-） &gt; 列表（,）", "i18n_872ad6c96e": "支持引用全局脚本库（G{'@'}(\"xx\") xx 为脚本标记）", "i18n_8756efb8f4": "真的要删除当前文件夹么？", "i18n_87659a4953": "确认要关闭 beta 计划吗？", "i18n_8780e6b3d1": "文件管理", "i18n_878aebf9b2": "登录名称", "i18n_87d50f8e03": "容器ID", "i18n_87db69bd44": "限制资源", "i18n_87dec8f11e": "错误的工作空间数据", "i18n_87e2f5bf75": "追加脚本模板", "i18n_87eb55155a": "行数：", "i18n_8813ff5cf8": "如果是在启动服务端后安装并配置的环境变量需要通过终端命令来重启服务端才能生效", "i18n_883848dd37": "实际内存占用", "i18n_8844085e15": "凌晨0点", "i18n_884ea031d3": "请输入变量描述", "i18n_8887e94cb7": "顺序执行(有执行失败将继续)", "i18n_888df7a89e": "不推荐", "i18n_88ab27cfd0": "分组/标签:", "i18n_88b4b85562": "打包预发布环境 npm i && npm run build:stage", "i18n_88b79928e7": "证书丢失", "i18n_88c5680d0d": "管理状态：", "i18n_88c85a2506": "新增的节点(插件端)将自动", "i18n_88e6615734": "解绑会检查数据关联性,同时将自动删除节点项目和脚本缓存信息,一般用于服务器无法连接且已经确定不再使用", "i18n_88f5c7ac4a": "请选择排序字段", "i18n_8900539e06": "写入大小", "i18n_89050136f8": "发布后操作", "i18n_891db2373b": "自动刷新", "i18n_897d865225": "镜像数：", "i18n_89944d6ccb": "标签限制为字母数字且长度 1-10", "i18n_899dbd7b9a": "CPU型号", "i18n_899fe0c5dd": "节点地址为插件端的 IP:PORT 插件端端口默认为：212", "i18n_89a40a1a8b": "分发过程请求,非必填，GET请求", "i18n_89cfb655e0": "容器名标签", "i18n_89d18c88a3": "请输入文件任务名", "i18n_89f5ca6928": "支持通配符", "i18n_8a1767a0d2": "开启此选项后可以正常发布隐藏文件", "i18n_8a3e316cd7": "不编码", "i18n_8a414f832f": "集群地址", "i18n_8a49e2de39": "QQ 邮箱配置", "i18n_8a4dbe88b8": "点击进入节点管理", "i18n_8a745296f4": "开机时间：", "i18n_8aa25f5fbe": "发布类型", "i18n_8ae2b9915c": "请填写第", "i18n_8aebf966b2": "集群访问地址", "i18n_8b1512bf3a": "如果端口", "i18n_8b2e274414": "上次重载结果", "i18n_8b3db55fa4": "集群ID:", "i18n_8b63640eee": "账号被禁用", "i18n_8b6e758e4c": "悬停到仪表盘上显示具体含义", "i18n_8b73b025c0": "简单易用，但不支持密钥导出备份", "i18n_8b83cd1f29": "要拉取的镜像名称", "i18n_8ba971a184": "私人令牌", "i18n_8ba977b4b7": "# 限制备份指定文件后缀（支持正则）", "i18n_8bd3f73502": "节点密码", "i18n_8be76af198": "163 邮箱配置", "i18n_8be868ba1b": "类 10", "i18n_8c0283435b": "：表示连续区间，例如在分上，表示2,3,4,5,6,7,8分", "i18n_8c24b5e19c": "请使用应用扫码绑定令牌,然后输入验证码确认绑定才生效", "i18n_8c2da7cce9": "没有任何证书", "i18n_8c4db236e1": "请输入脚本标记，标记只能是字母或者数字长度需要小于 20 并且全局唯一", "i18n_8c61c92b4b": "备份类型", "i18n_8c66392870": "需要使用 ssh-keygen -m PEM -t rsa -b 4096 -C", "i18n_8c67370ee5": "如果产物同步到文件中心,当前值会共享", "i18n_8c7c7f3cfa": "服务端脚本", "i18n_8c7ce1da57": "开启 dockerTag 版本递增后将在每次构建时自动将版本号最后一位数字同步为构建序号ID, 如：当前构建为第 100 次构建 testtag:1.0 -> testtag:1.100,testtag:1.0.release -> testtag:1.100.release。如果没有匹配到数字将忽略递增操作", "i18n_8c7d19b32a": "允许执行的内存节点 (MEM) (0-3, 0,1)。 仅在 NUMA 系统上有效。", "i18n_8cae9cb626": "浅色 idea", "i18n_8ccbbb95a4": "请填写远程URL", "i18n_8cd628f495": "所有的IP：", "i18n_8d0fa2ee2d": "请输入端口号", "i18n_8d1286cd2e": "没有任何分发日志", "i18n_8d13037eb7": "状态消息：", "i18n_8d202b890c": "批量触发器地址", "i18n_8d3d771ab6": "编辑集群", "i18n_8d5956ca2a": "以 yaml/yml 格式配置,scriptId 为项目路径下的脚本文件的相对路径或者服务端脚本模版ID，可以到服务端脚本模版编辑弹窗中查看 scriptId", "i18n_8d5c1335b6": "容器名称数字字母,且长度大于1", "i18n_8d62b202d9": "请选择要使用的文件", "i18n_8d63ef388e": "暂停", "i18n_8d6d47fbed": "#  在指定目录执行: ./ 项目目录  /root/ 特定目录 默认在 {'${jpom_agent_data_path}'}/script_run_cache ", "i18n_8d6f38b4b1": "文件描述", "i18n_8d90b15eaf": "# 宿主机文件上传到容器 /host:/container:true", "i18n_8d92fb62a7": "请选择模板节点", "i18n_8d9a071ee2": "导入", "i18n_8da42dd738": "秒级别（默认未开启秒级别,需要去修改配置文件中:[system.timerMatchSecond]）", "i18n_8dbe0c2ffa": "占用空间：", "i18n_8dc09ebe97": "获取", "i18n_8dc8bbbc20": "文件系统", "i18n_8de2137776": "集群任务", "i18n_8e2ed8ae0d": "【独立分发】", "i18n_8e331a52de": "只允许访问的 IP 地址", "i18n_8e34aa1a59": "以此机器节点配置为模板", "i18n_8e389298e4": "导出镜像", "i18n_8e38d55231": "真的要彻底退出系统么？彻底退出将退出登录和清空浏览器缓存", "i18n_8e54ddfe24": "启动", "i18n_8e6184c0d3": "项目可能支持关联如下数据：", "i18n_8e6a77838a": "请选择要分发到的机器节点", "i18n_8e872df7da": "注意是整行不能包含空格", "i18n_8e89763d95": "宿主机ip", "i18n_8e8bcfbb4f": "确定要修剪对应的信息吗？修剪会自动清理对应的数据", "i18n_8e9bd127fb": "需要提前为工作空间配置授权目录", "i18n_8ea4c3f537": "从尾搜索、文件前100行、文件后100行", "i18n_8ea93ff060": "节点脚本模版是存储在节点中的命令脚本用于在线管理一些脚本命令，如初始化软件环境、管理应用程序等", "i18n_8ef0f6c275": "关闭 beta 计划", "i18n_8f0bab9a5a": "在读取的日志文件数", "i18n_8f0c429b46": "迁移操作不具有事务性质，如果流程被中断或者限制条件不满足可能产生冗余数据！！！！", "i18n_8f36f2ede7": "工作空间名称：", "i18n_8f3747c057": "服务名称", "i18n_8f40b41e89": "过期时间：", "i18n_8f7a163ee9": "快速安装插件端", "i18n_8f8f88654f": "暂无节点信息", "i18n_8fb7785809": "如果在生成私钥的过程中有加密，那么需要把加密密码填充到上面的密码框中", "i18n_8fbcdbc785": "请输入别名码", "i18n_8fd9daf8e9": "保证在内网中使用可以忽略 TLS 证书", "i18n_8fda053c83": "写入次数", "i18n_8ffded102f": "如果需要定时自动构建则填写,cron 表达式.默认未开启秒级别,需要去修改配置文件中:[system.timerMatchSecond]）", "i18n_900c70fa5f": "警告", "i18n_9014d6d289": "备份列表", "i18n_90154854b6": "请输入host", "i18n_901de97cdb": "方式请求接口参数传入到请求体 ContentType 请使用：text/plain", "i18n_903b25f64e": "未知状态", "i18n_904615588b": "文件类型没有控制台功能", "i18n_9057ac9664": "请选择触发类型", "i18n_9065a208e8": "通过 URL 下载远程文件到项目文件夹,需要到对应的工作空间下授权目录配置中配置允许的 HOST 授权", "i18n_906f6102a7": "重启成功", "i18n_9086111cff": "关联工作空间 docker", "i18n_90b5a467c1": "刷新目录", "i18n_90c0458a4c": "导入备份", "i18n_90eac06e61": "宿主机目录", "i18n_912302cb02": "浏览器", "i18n_9136e1859a": "Docker镜像", "i18n_913ef5d129": "执行重启", "i18n_916cde39c4": "所有参数将拼接成字符串以空格分隔形式执行脚本,需要注意参数顺序和未填写值的参数将自动忽略", "i18n_916ff9eddd": "请输入昵称", "i18n_917381e4a5": "当前下载源：", "i18n_91985e3574": "自动探测", "i18n_91a10b8776": " 脚本库 ", "i18n_920f05031b": "状态描述", "i18n_922b76febd": "运行模式必填", "i18n_923f8d2688": "发布后命令", "i18n_9255f9c68f": "会话已经关闭[tail-file]", "i18n_92636e8c8f": "跳过", "i18n_9282b1e5da": "企业微信扫码", "i18n_929e857766": "证书类型", "i18n_92c6aa6db9": "如果您 SSH 机器中存在 docker 但是系统没有监测到，您需要到【配置管理】-", "i18n_92dde4c02b": "广告投放", "i18n_92e3a830ae": "帮助", "i18n_92f0744426": "容器构建是指使用 docker 容器执行构建,这样可以达到和宿主机环境隔离不用安装依赖环境", "i18n_92f3fdb65f": "仓库：", "i18n_92f9a3c474": "切换语言后页面将自动刷新", "i18n_9300692fac": "标记引用", "i18n_9302bc7838": "请输入要检查的端口", "i18n_930882bb0a": "个", "i18n_9308f22bf6": "单个触发器地址中：第一个随机字符串为脚本ID，第二个随机字符串为 token", "i18n_930fdcdf90": "配置名 （如：size）", "i18n_9324290bfe": "如：key1", "i18n_932b4b7f79": "注意：在每一个子表达式中优先级：", "i18n_934156d92c": "创建分发项目", "i18n_9341881037": "确定要取批量构建吗？注意：同时运行多个构建将占用较大的资源,请慎重使用批量构建,如果批量构建的数量超多构建任务队列等待数，构建任务将自动取消", "i18n_935b06789f": "您还未执行操作", "i18n_9362e6ddf8": "危险操作！！！", "i18n_938246ce8b": "任务模板", "i18n_938dd62952": "执行路径", "i18n_939d5345ad": "提交", "i18n_93e1df604a": "机器分组", "i18n_93e894325d": "批量启动", "i18n_9402665a2c": " 持续搜索（对话框不会自动关闭，按 Enter 查找下一个，按 Shift-Enter 查找上一个）", "i18n_9412eb8f99": "请填写平台地址", "i18n_9443399e7d": " ，范围1970~2099，但是第7位不做解析，也不做匹配", "i18n_94763baf5f": "可以到节点管理中的【插件端配置】=>【授权配置】修改", "i18n_947d983961": "温馨提示", "i18n_948171025e": "会话已经关闭[docker-log]", "i18n_949934d97c": "超大", "i18n_949a8b7bd2": "列设置", "i18n_94aa195397": "证书文件", "i18n_94ca71ae7b": "请选择要使用的证书", "i18n_94d4fcca1b": "创建账号", "i18n_952232ca52": "构建历史可能占有较多硬盘空间,建议根据实际情况配置保留个数", "i18n_953357d914": "忽略校验 state", "i18n_953ec2172b": "未重启成功：", "i18n_954fb7fa21": "真的要删除项目么？删除项目不会删除项目相关文件奥,建议先清理项目相关文件再删除项目", "i18n_956ab8a9f7": "配置文件吗？配置文件一旦创建不能通过管理页面删除的奥？", "i18n_957c1b1c50": "集群节点", "i18n_95a43eaa59": "创建人", "i18n_95b351c862": "编辑", "i18n_95c5c939e4": "可选择的列表和项目授权目录是一致的，即相同配置", "i18n_95dbee0207": "远程下载安全HOST", "i18n_96283fc523": "备份文件不存在", "i18n_964d939a96": " 长名称：", "i18n_969098605e": "环境变量是指配置在系统中的一些固定参数值，用于脚本执行时候快速引用。", "i18n_96972aa0df": "# 是否开启差异备份（仅备份变动过的文件）", "i18n_96b78bfb6a": "请勿手动删除数据目录下面文件 !!!!", "i18n_96c1c8f4ee": "灰绿 abcdef", "i18n_96c28c4f17": "加入到哪个集群", "i18n_96d46bd22e": "手动刷新统计", "i18n_96e6f43118": "容器 runtime", "i18n_974be6600d": "密码必须包含数字，字母，字符，且大于6位", "i18n_977bfe8508": "标签(TAG)", "i18n_979b7d10b0": "构建中断", "i18n_97a19328a8": "立即开启", "i18n_97cb3c4b2e": "工作空间环境变量用于构建命令相关", "i18n_97d08b02e7": "网络端口测试", "i18n_97ecc1bbe9": "输出流量", "i18n_981cbe312b": "至", "i18n_9829e60a29": "实时版本号", "i18n_98357846a2": "表格视图才能使用批量操作功能", "i18n_983f59c9d4": "验证码", "i18n_9878af9db5": "请到【系统管理】-> 【资产管理】-> 【Docker管理】新增Docker并创建集群，或者将已存在的的 Docker 集群授权关联、分配到此工作空间", "i18n_9880bd3ba1": "此工具用于检查 cron 表达式是否正确,以及计划运行时间", "i18n_989f1f2b61": "真的要重启项目么？", "i18n_98a315c0fc": "授权", "i18n_98cd2bdc03": "表格视图才能使用同步配置功能", "i18n_98d69f8b62": "工作空间", "i18n_98e115d868": "运行中的定时任务", "i18n_9914219dd1": "从头搜索", "i18n_9932551cd5": "内存", "i18n_993a5c7eee": "#配置说明：https://docs.docker.com/engine/api/v1.43/#tag/Container/operation/ContainerCreate", "i18n_99593f7623": "客户端ID", "i18n_9964d6ed3f": "挂载", "i18n_996dc32a98": "系统类型", "i18n_9970ad0746": "主题", "i18n_9971192b6a": "。如：http", "i18n_9973159a4d": "匹配一个字符", "i18n_998b7c48a8": "查看容器", "i18n_99b3c97515": "小时级别", "i18n_99cba05f94": "真的要删除 SSH 么？当前 ssh 关联的脚本在删除后均将失效", "i18n_99d3e5c718": " 开始搜索", "i18n_99f0996c0a": "请选择日志目录", "i18n_9a00e13160": "单个触发器地址中：第一个随机字符串为项目ID(服务端)，第二个随机字符串为 token", "i18n_9a0c5b150c": "编辑 命令", "i18n_9a2ee7044f": "变量值", "i18n_9a436e2a53": "修剪具有指定标签的对象,多个使用逗号分隔", "i18n_9a4b872895": "集群操作", "i18n_9a56bb830e": "用户昵称", "i18n_9a77f3523e": "镜像 tag", "i18n_9a7b52fc86": "所有", "i18n_9a8eb63daf": "配置工作空间权限", "i18n_9ab433e930": "其他配置", "i18n_9ac4765895": "一个整数值，表示此容器相对于其他容器的相对 CPU 权重。", "i18n_9adf43e181": "正在构建数", "i18n_9ae40638d2": "部署证书", "i18n_9af372557e": "服务端端口", "i18n_9aff624153": "监控", "i18n_9b0bc05511": "在文件第 1 - 100 行中搜索", "i18n_9b1c5264a0": "上传后", "i18n_9b280a6d2d": "节点:", "i18n_9b3e947cc9": "节点状态：", "i18n_9b5f172ebe": "绑定集群", "i18n_9b7419bc10": "QQ邮箱", "i18n_9b74c734e5": "节点账号密码为插件端的账号密码,并非用户账号(管理员)密码", "i18n_9b78491b25": "请输入授权路径，回车支持输入多个路径，系统会自动过滤 ../ 路径、不允许输入根路径", "i18n_9b7ada2613": " ：范围：1~31，", "i18n_9b9e426d16": "在读取的日志文件数：", "i18n_9ba71275d3": ",请耐心等待暂时不用刷新页面,升级成功后会自动刷新", "i18n_9baca0054e": "修改人", "i18n_9bbb6b5b75": "真的要删除日志搜索么？", "i18n_9bd451c4e9": "节点已经存在", "i18n_9be8ff8367": "支持变量替换：{'${BUILD_ID}'}、{'${BUILD_NAME}'}、{'${BUILD_RESULT_FILE}'}、{'${BUILD_NUMBER_ID}'}", "i18n_9bf4e3c9de": "创建分发项目是指,全新创建一个属于节点分发到项目,创建成功后项目信息将自动同步到对应的节点中,修改节点分发信息也自动同步到对应的节点中", "i18n_9bf5aa6672": "web socket 错误,请检查是否开启 ws 代理", "i18n_9c19a424dc": "请输入原密码", "i18n_9c2a917905": "搜索命令", "i18n_9c2f1d3f39": "内的root拥有真正的root权限。", "i18n_9c3a3e1b03": "父级不存在自动删除", "i18n_9c3a5e1dad": "请到【系统管理】-> 【资产管理】-> 【机器管理】新增节点，或者将已新增的机器授权关联、分配到此工作空间", "i18n_9c3c05d91b": "节点地址建议使用内网地址", "i18n_9c55e8e0f3": "允许执行的 CPU（例如，0-3、0,1）。", "i18n_9c66f7b345": "真的要删除机器么？删除会检查数据关联性", "i18n_9c84cd926b": "新机器还需要绑定工作空间，因为我们建议将不同集群资源分配到不同的工作空间来管理", "i18n_9c942ea972": "证书生成方式可以参考文档）来连接 docker 提升安全性", "i18n_9c99e8bec9": "不填写则发布至项目的根目录", "i18n_9cac799f2f": "选择分组名", "i18n_9caecd931b": "字段", "i18n_9cd0554305": "如果不需要保留较多构建历史信息可以到服务端修改构建相关配置参数", "i18n_9ce5d5202a": "运行的Jar包：", "i18n_9d577fe51b": "文件来源", "i18n_9d5b1303e0": "创建集群会将尝试获取 docker 中集群信息，如果存在集群信息将自动同步集群信息到系统，反之不存在集群信息将自动创建 swarm 集群", "i18n_9d7d471b77": "请选择节点角色", "i18n_9d89cbf245": "分发名称", "i18n_9dd62c9fa8": "终端命令无限制", "i18n_9ddaa182bd": "插件端时间：", "i18n_9de72a79fe": "查看文件", "i18n_9e09315960": "重建", "i18n_9e0c797c04": "选择监控使用的语言", "i18n_9e2e02ef08": "分发类型", "i18n_9e4ae8a24f": "钉钉扫码", "i18n_9e560a4162": "方式生成的 SSH key", "i18n_9e5ffa068e": "基本信息", "i18n_9e6b699597": "nanoCPUs 最小 1000000", "i18n_9e78f02aad": "参数描述,参数描述没有实际作用,仅是用于提示参数的含义", "i18n_9e96d9c8d3": "系统负载:", "i18n_9e98fa5c0d": "文件名栏支持右键菜单", "i18n_9ec961d8cb": "选择构建产物", "i18n_9ee0deb3c8": "网络开了小差！请重试...:", "i18n_9ee9d48699": "创建后不支持修改", "i18n_9f01272a10": " 法律风险", "i18n_9f0de3800b": "请填写仓库名称", "i18n_9f4a0d67c6": "文件名建议不要出现空白、制表、其他空白、\\、/、：、*、？、\"、<、>、\\、\"{'|'}\" 等特殊字符", "i18n_9f52492fbc": "配置详情请参考配置示例", "i18n_9f6090c819": "传入参数有：buildId、buildName、type、statusMsg、triggerTime", "i18n_9f6fa346d8": "请输入 SSH 名称", "i18n_9f70e40e04": "运行时间", "i18n_9fb12a2d14": "发布后执行的命令(非阻塞命令),一般是启动项目命令 如：ps -aux 2 {'|'} grep java, 支持变量替换：{'${ BUILD_ID }'}、{'${ BUILD_NAME }'}、{'${ BUILD_RESULT_FILE }'}、{'${ BUILD_NUMBER_ID }'} ", "i18n_9fb61a9936": "版本递增", "i18n_9fc2e26bfa": "请选择项目", "i18n_9fca7c455f": "登录时间", "i18n_9febf31146": "请选择文件", "i18n_9ff5504901": "传入环境变量有：buildId、buildName、type、statusMsg、triggerTime、buildNumberId、buildSourceFile", "i18n_a001a226fd": "更新时间", "i18n_a03c00714f": "批量关闭", "i18n_a03ea1e864": "请选择分发到的节点", "i18n_a04b7a8f5d": "单个触发器请求支持将参数解析为环境变量传入脚本执行，比如传入参数名为 abc=efg 在脚本中引入则为：{'${trigger_abc}'}", "i18n_a050cbc36d": "您的浏览器版本太低，不支持该功能", "i18n_a056d9c4b3": "选择脚本", "i18n_a05c1667ca": "构建历史", "i18n_a08cbeb238": "并且配置正确的环境变量", "i18n_a09375d96c": "悬空", "i18n_a093ae6a6e": "自动续期", "i18n_a0a111cbbd": "分发项目-", "i18n_a0a3d583b9": "总内存：", "i18n_a0b9b4e048": "请输入客户端ID [clientId]", "i18n_a0d0ebc519": "全局代理", "i18n_a0e31d89ff": "一般建议 10 秒以上", "i18n_a0f1bfad78": "数据目录大小包含：临时文件、在线构建文件、数据库文件等", "i18n_a11cc7a65b": "请输入内容", "i18n_a13d8ade6a": "关联节点数据是异步获取有一定时间延迟", "i18n_a14da34559": "资源监控异常", "i18n_a156349591": " 查看", "i18n_a1638e78e8": "匹配包含 a 或者 b 的行", "i18n_a17450a5ff": "选择压缩文件", "i18n_a17b5ab021": "当前文件已经存在啦", "i18n_a17b905126": "关键词时自动识别为隐私变量", "i18n_a17bc8d947": "控制台日志只是启动项目输出的日志信息,并非项目日志。可以关闭控制台日志备份功能：", "i18n_a189314b9e": "不发布", "i18n_a1a3a7d853": "现成生成", "i18n_a1b745fba0": "请输入备份名称", "i18n_a1bd9760fc": "定时任务", "i18n_a1c4a75c2d": "是一款开源软件您使用这个项目并感觉良好，或是想支持我们继续开发，您可以通过如下方式支持我们：", "i18n_a1da57ab69": "支付宝转账", "i18n_a1e24fe1f6": "用户时间", "i18n_a1f58b7189": "参数{count}值", "i18n_a1fb7f1606": "脚本管理", "i18n_a20341341b": "显示前N行", "i18n_a24d80c8fa": "项目启动,停止,重启,文件变动都将请求对应的地址", "i18n_a25657422b": "变量名", "i18n_a2741f6eb3": "系统环境变量中变量名包含：", "i18n_a2a0f52afe": "不填将使用默认的 $HOME/.ssh 目录中的配置,使用优先级是：id_dsa>id_rsa>identity", "i18n_a2ae15f8a7": "构建流程", "i18n_a2e62165dc": "真的要保存当前配置吗？IP 授权请慎重配置奥( 授权是指只允许访问的 IP ),配置后立马生效 如果配置错误将出现无法访问的情况,需要手动恢复奥！！！", "i18n_a2ebd000e4": "不做任何操作", "i18n_a3296ef4f6": "全屏终端", "i18n_a33a2a4a90": "服务端同步的脚本不能在此修改", "i18n_a34545bd16": "构建参数,如：key1=values1&keyvalue2 使用 URL 编码", "i18n_a34b91cdd7": "环境变量还可以用于仓库账号密码、ssh密码引用", "i18n_a34c24719b": "开始执行任务", "i18n_a35740ae41": "操作提示", "i18n_a3751dc408": " ：12点的每分钟执行", "i18n_a37c573d7b": "可以是 Unix 时间戳、日期格式的时间戳或 Go 持续时间字符串（例如 10m、1h30m），相对于守护进程机器的时间计算。", "i18n_a38ed189a2": "上传更新前请阅读更新日志里面的说明和注意事项并且", "i18n_a39340ec59": "禁止命令", "i18n_a396da3e22": "当前工作空间还没有项目并且也没有任何节点", "i18n_a3d0154996": "文件状态", "i18n_a3f1390bf1": "修改后如果有原始关联数据将失效，需要重新配置关联", "i18n_a4006e5c1e": "创建备份", "i18n_a421ec6187": "编辑环境变量", "i18n_a4266aea79": "真的要删除此服务么？", "i18n_a436c94494": "飞书扫码", "i18n_a472019766": "节点Id", "i18n_a497562c8e": "执行人", "i18n_a4f5cae8d2": "开启状态", "i18n_a4f629041c": "路径需要配置绝对路径", "i18n_a50fbc5a52": "支持指定网卡名称来绑定：", "i18n_a51cd0898f": "容器名称", "i18n_a51d8375b7": "选择静态文件", "i18n_a52a10123f": "如果升级失败需要手动恢复奥", "i18n_a52aa984cd": "真的要删除权限组么？", "i18n_a53d137403": "会话已经关闭[free-script]", "i18n_a55ae13421": "请配置下载授权码", "i18n_a5617f0369": "SSH连接信息", "i18n_a577822cdd": "保存并构建", "i18n_a59d075d85": "自定义克隆深度，避免大仓库全部克隆", "i18n_a5d1c511d7": "模板名称", "i18n_a5d550f258": "间隔时间", "i18n_a5daa9be44": "上传前请检查包是否完整,否则可能出现更新后无法正常启动的情况！！", "i18n_a5e9874a96": "请选择发布到哪个 docker 集群", "i18n_a5f84fd99c": "非隐私", "i18n_a6269ede6c": "管理节点", "i18n_a62fa322b4": "证书将打包成 zip 文件上传到对应的文件夹", "i18n_a637a42173": "选择的构建历史产物已经不存在啦", "i18n_a63fe7b615": "分配到工作空间后还需要到关联中进行配置对应工作空间才能完美使用奥", "i18n_a657f46f5b": "周", "i18n_a66644ff47": "类 172", "i18n_a66fff7541": "远程下载URL", "i18n_a6bf763ede": "机器节点", "i18n_a6fc9e3ae6": "上传文件", "i18n_a74b62f4bb": "硬盘信息", "i18n_a75a5a9525": "发布目录,构建产物上传到对应目录", "i18n_a75b96584d": "服务Id", "i18n_a75f781415": "服务端菜单", "i18n_a7699ba731": "上传成功", "i18n_a76b4f5000": "管理员数", "i18n_a77cc03013": "如果引用脚本库需要提前将对应脚本分发到机器节点中才能正常使用", "i18n_a795fa52cd": "彻底退出", "i18n_a7a9a2156a": "请输入确认密码", "i18n_a7c8eea801": "尝试自动续签成功", "i18n_a7ddb00197": "阿里云企业邮箱 SSL", "i18n_a805615d15": "type 的值有：startReady、pull、executeCommand、release、done、stop、success、error", "i18n_a810520460": "密码", "i18n_a823cfa70c": "容器标签", "i18n_a84a45b352": "升级策略", "i18n_a8754e3e90": "填写正确的IP地址", "i18n_a87818b04f": "等待开始", "i18n_a8920fbfad": "命令路径请修改为您的服务器中的实际路径", "i18n_a89646d060": "创建工作空间后还需要在对应工作空间中分别管理对应数据", "i18n_a8f44c3188": "账号是系统特定演示使用的账号", "i18n_a90cf0796b": "信息：", "i18n_a912a83e6f": "插件版本", "i18n_a918bde61d": "您还未构建", "i18n_a91ce167c1": "文件id", "i18n_a9463d0f1a": "搜索模式,默认查看文件最后多少行，从头搜索指从指定行往下搜索，从尾搜索指从文件尾往上搜索多少行", "i18n_a94feac256": "载速度根据网速来确定,如果网络不佳下载会较慢", "i18n_a952ba273f": "联系时请备注来意", "i18n_a9795c06c8": "没有SSH", "i18n_a98233b321": "使用微软全家桶的推荐", "i18n_a9886f95b6": "确定要删除此脚本库吗？", "i18n_a9add9b059": "数据存储目录：", "i18n_a9b50d245b": "不绑定", "i18n_a9c52ffd40": "严格执行：", "i18n_a9c999e0bd": "建议在上传后的脚本中对文件进行自定义更名，SSH 上传默认为：{'${FILE_ID}'}.{'${FILE_EXT_NAME}'}", "i18n_a9de52acb0": "操作方法", "i18n_a9eed33cfb": "如果版本相差大需要重新初始化数据来保证和当前程序里面字段一致", "i18n_a9f94dcd57": "部署", "i18n_aa53a4b93a": "没有任何网络接口信息", "i18n_aa9236568f": "统计趋势", "i18n_aabdc3b7c0": "项目路径", "i18n_aac62bc255": "点击查看日志", "i18n_aacd9caa4a": "查看环境变量", "i18n_aad7450231": "请输入选择绑定的集群", "i18n_aadf9d7028": "用于下载远程文件来进行节点分发和文件上传", "i18n_aaeb54633e": "重载", "i18n_ab006f89e7": "手动删除", "i18n_ab13dd3381": "自建 Gitlab 账号登录", "i18n_ab3615a5ad": "下载安装包", "i18n_ab3725d06b": "会话已经关闭", "i18n_ab7f78ba4c": "空间ID(全匹配)", "i18n_ab968d842f": "构建镜像尝试去更新基础镜像的新版本", "i18n_ab9a0ee5bd": "文件夹路径 需要在仓库里面 dockerfile", "i18n_ab9c827798": "没有docker集群", "i18n_abb6b7260b": "如果多选 ssh 下面目录只显示选项中的第一项，但是授权目录需要保证每项都配置对应目录", "i18n_abba4043d8": "从头搜索、文件前20行、文件后3行", "i18n_abba4775e1": "命令参数", "i18n_abd9ee868a": "网络模式：bridge、container:<name\"{'|'}\"id>、host、container、none", "i18n_abdd7ea830": "请输入新密码", "i18n_abee751418": "容器Id: ", "i18n_ac00774608": "第", "i18n_ac0158db83": "任务id", "i18n_ac2f4259f1": "新版本：", "i18n_ac408e4b03": "请选择证书类型", "i18n_ac5f3bfa5b": "选择要监控的项目,file 类型项目不可以监控", "i18n_ac762710a5": "支持自定义排序字段：sort", "i18n_ac783bca36": "真的要退出并切换账号登录么？", "i18n_acb4ce3592": "请选择静态文件中的文件", "i18n_acd5cb847a": "失败", "i18n_ace71047a0": "请到【系统管理】-> 【资产管理】-> 【SSH管理】新增SSH，或者将已新增的SSH授权关联、分配到此工作空间", "i18n_acf14aad3c": "不用挨个配置。配置后会覆盖之前的配置", "i18n_ad209825b5": "请选择修剪类型", "i18n_ad311f3211": "请选择仓库", "i18n_ad35f58fb3": "占用空间", "i18n_ad4b4a5b3b": "宿主", "i18n_ad780debbc": "回滚策略", "i18n_ad8b626496": "真的要删除构建历史记录么？", "i18n_ad9788b17d": "异常恢复", "i18n_ad9a677940": "指定 settings 文件打包 mvn -s xxx/settings.xml clean package", "i18n_adaf94c06b": "执行结果", "i18n_adbec9b14d": "创建备份信息", "i18n_adcd1dd701": "返回列表", "i18n_add91bb395": "逻辑节点", "i18n_ade63665b2": "文件合并中", "i18n_ae0d608495": "是否使用MFA", "i18n_ae0fd9b9d2": "备份时间", "i18n_ae12edc5bf": "点击复制文件路径", "i18n_ae17005c0c": "未加入", "i18n_ae35be7986": "token,全匹配", "i18n_ae653ec180": "详细描述", "i18n_ae6838c0e6": "节点分发", "i18n_ae809e0295": "后缀,精准搜索", "i18n_aeade8e979": "未初始化", "i18n_aeb44d34e6": "一次性捐款赞助", "i18n_aec7b550e2": "删除工作空间确认", "i18n_aed1dfbc31": "中", "i18n_aef1a0752a": "注意：配置项目日志编码格式后项目的“日志搜索”功能读取日志文件时也将跟随此编码格式读取日志文件", "i18n_aefd8f9f27": "请选择还原方式", "i18n_af013dd9dc": "重启成功后会自动刷新", "i18n_af0df2e295": "需要到 ssh 信息中配置允许编辑的文件后缀", "i18n_af14cd6893": "请填写构建 DSL 配置内容,可以点击上方切换 tab 查看配置示例", "i18n_af3a9b6303": "企业微信扫码账号登录", "i18n_af427d2541": "数据更新时间", "i18n_af4d18402a": "已经断开连接啦", "i18n_af51211a73": "页面内容会出现滚动条", "i18n_af708b659f": "内存:", "i18n_af7c96d2b9": "同步机制采用容器 host 确定是同一个服务器（docker）", "i18n_af83388834": "触发备份 ", "i18n_af924a1a14": "下载异常", "i18n_af98c31607": "物理节点项目数量：", "i18n_afa8980495": "请输入允许编辑文件的后缀及文件编码，不设置编码则默认取系统编码，示例：设置编码：txt{'@'}utf-8， 不设置编码：txt", "i18n_afb9fe400b": "使用率：", "i18n_b04070fe42": "选择代理类型", "i18n_b04209e785": "关联数据：", "i18n_b05345caad": "所有者", "i18n_b07a33c3a8": "请选择分发节点", "i18n_b095ceda99": "选择的脚本需要将监听的事件名配置到脚本描述中才能生效奥", "i18n_b0b9df58fd": "SSH节点", "i18n_b0fa44acbb": "占用率：", "i18n_b10b082c25": "的值有：stop、beforeStop、start、beforeRestart", "i18n_b1192f8f8e": "真的取消当前分发吗？", "i18n_b11b0c93fa": "如果在 Linux 中实际运行内存可能和您直接使用 free -h 命令查询到 free 和 total 字段计算出数值相差过大那么此时就是您当前服务器中的交换内存引起的", "i18n_b12d003367": "隐私字段", "i18n_b153126fc2": "请输入工作空间名称", "i18n_b15689296a": "风险提醒", "i18n_b15d91274e": "关闭", "i18n_b166a66d67": "确定要将此数上移吗？", "i18n_b17299f3fb": "插件端进程ID：", "i18n_b1785ef01e": "节点名称", "i18n_b186c667dc": "分发过程请求对应的地址,开始分发,分发完成,分发失败,取消分发", "i18n_b188393ea7": "发布的SSH", "i18n_b1a09cee8e": "清空还原", "i18n_b1dae9bc5c": "管理员", "i18n_b28836fe97": "分发 ID 等同于项目 ID", "i18n_b28c17d2a6": " (MEM) (0-3, 0,1)。 仅在 NUMA 系统上有效。", "i18n_b29fd18c93": "请选择指定发布的项目", "i18n_b2f296d76a": "5分钟", "i18n_b30d07c036": "批量关闭启动", "i18n_b328609814": "管理员拥有：管理服务端的部分权限", "i18n_b339aa8710": "表格", "i18n_b33c7279b3": "认证方式", "i18n_b3401c3657": "容器目录", "i18n_b341f9a861": "任务时间", "i18n_b343663a14": "清空发布是指在上传新文件前,会将项目文件夹目录里面的所有文件先删除后再保存新文件", "i18n_b36e87fe5b": "不执行，但是编译测试用例 mvn clean package -DskipTests", "i18n_b37b786351": "分组名", "i18n_b384470769": "同步缓存", "i18n_b38d6077d6": "登录IP", "i18n_b38d7db9b0": "下载构建日志,如果按钮不可用表示日志文件不存在,一般是构建历史相关文件被删除", "i18n_b3913b9bb7": "请输入构建环境变量：xx=abc 多个变量回车换行即可", "i18n_b399058f25": "强大安全的密码管理付费应用", "i18n_b39909964f": "请输入邮箱账号", "i18n_b3b1f709d4": "剔除", "i18n_b3bda9bf9e": "请选择工作空间", "i18n_b3ef35a359": "源仓库", "i18n_b3f9beb536": "：3~18分，每5分钟执行一次，即0:03, 0:08, 0:13, 0:18, 1:03, 1:08……", "i18n_b3fe677b5f": "失败率", "i18n_b408105d69": "密码字段和密钥字段在编辑的时候不会返回，如果需要重置或者清空就请点我", "i18n_b437a4d41d": "也支持 URL 参数格式：test_par=123abc&test_par2=abc21", "i18n_b44479d4b8": "可用标签", "i18n_b4750210ef": "集群修改时间：", "i18n_b499798ec5": "禁用分发节点", "i18n_b4a8c78284": "选择工作空间", "i18n_b4c83b0b56": "仓库账号", "i18n_b4dd6aefde": "会话已经关闭[script-console]", "i18n_b4e2b132cf": "插件端运行端口默认使用：", "i18n_b4fc1ac02c": "取消构建", "i18n_b4fd7afd31": "个性配置", "i18n_b513f53eb4": "超时时间 单位秒", "i18n_b515d55aab": "可以通过证书管理中提前上传或者点击后面选择证书去选择/导入证书", "i18n_b53dedd3e0": "发布前执行的命令(非阻塞命令),一般是关闭项目命令", "i18n_b55f286cba": "载前请阅读更新日志里面的说明和注意事项并且", "i18n_b56585aa18": "配置后可以控制想要在某个时间段禁止用户操作某些功能，优先判断禁用时段", "i18n_b57647c5aa": "真的要解绑脚本关联的节点么？", "i18n_b57ecea951": "已经运行时间：", "i18n_b5a1e1f2d1": "触发类型：", "i18n_b5a6a07e48": "周二", "i18n_b5b51ff786": "上传 SQL 文件", "i18n_b5c291805e": "初始化系统", "i18n_b5c3770699": "控制台", "i18n_b5c5078a5d": "所有的IPV4列表", "i18n_b5ce5efa6e": "集群服务", "i18n_b5d0091ae3": "构建ID", "i18n_b5d2cf4a76": "当目标工作空间已经存在 脚本 时候将自动同步 脚本内容、默认参数、自动执行、描述", "i18n_b5fdd886b6": "全屏查看日志", "i18n_b60352bc4f": "虚拟", "i18n_b6076a055f": "登录失败", "i18n_b61a7e3ace": "脚本名称：", "i18n_b63c057330": "真的要删除操作监控么？", "i18n_b650acd50b": "恢复默认名称", "i18n_b6728e74a4": "运行目录：", "i18n_b6a828205d": "缓存构建", "i18n_b6afcf9851": "禁止命令是不允许在终端执行的命令，多个逗号隔开。(超级管理员没有任何限制)", "i18n_b6c9619081": "端口：", "i18n_b6e8fb4106": "平台登录", "i18n_b6ee682dac": "插件数：", "i18n_b714160f52": "分发项目 ID", "i18n_b71a7e6aab": "本地命令", "i18n_b7579706a3": "校验", "i18n_b7c139ed75": "如果项目目录较大或者涉及到深目录，建议关闭扫描避免获取项目目录扫描过长影响性能", "i18n_b7cfa07d78": "确认绑定", "i18n_b7df1586a9": "当目标工作空间已经存在节点时候将自动同步 docker 仓库配置信息", "i18n_b7ea5e506c": "系统信息", "i18n_b7ec1d09c4": "服务ID", "i18n_b7f770d80b": "需要先安装依赖 npm i && npm run build", "i18n_b8545de30e": "请至少选择 1 个节点", "i18n_b85b213579": "发件人名称", "i18n_b86224e030": "节点状态", "i18n_b87c9acca3": "真的要强制退出集群吗？", "i18n_b8915a4933": "真的关闭当前用户的两步验证么？", "i18n_b8ac664d98": "勾选数据表", "i18n_b90a30dd20": "此处不填不会修改密码", "i18n_b91961bf0b": "传入参数有：projectId、projectName、type、result", "i18n_b922323119": "镜像标签,如：key1=value1&key2=value2", "i18n_b939d47e23": "公钥", "i18n_b953d1a8f1": "不能关闭了", "i18n_b96b07e2bb": "仅修剪未使用和未标记的镜像", "i18n_b9a4098131": "触发器地址", "i18n_b9af769752": "镜像名称必填", "i18n_b9b176e37a": "请选择脚本", "i18n_b9bcb4d623": "插件：", "i18n_b9c1616fd5": "SSH方式连接的 docker 不建议用于容器构建（SSH 方式用于构建非常不稳定）", "i18n_b9c4cf7483": " 全部替换", "i18n_b9c52d9a85": "文件名：", "i18n_ba17b17ba2": "没有任何SSH脚本命令", "i18n_ba1f68b5dd": "这样使得", "i18n_ba20f0444c": "强制删除", "i18n_ba311d8a6a": "脚本", "i18n_ba3a679655": "以 yaml/yml 格式配置", "i18n_ba452d57f2": "使用率最大的分区：", "i18n_ba52103711": "剩余 inode 数", "i18n_ba619a0942": "默认构建错误将自动忽略隐藏文件", "i18n_ba6e91fa9e": "权限", "i18n_ba6ea3d480": "页面全屏，高度 100%。局部区域可以滚动", "i18n_ba8d1dca4a": "注意：", "i18n_baafe06808": "安全组规则", "i18n_bab17dc6b1": "选择进程名", "i18n_baef58c283": "请输入标签名 字母数字 长度 1-10", "i18n_baefd3db91": "授权可以直接访问的目录，多个回车换行即可", "i18n_bb316d9acd": "下载速度根据网速来确定,如果网络不佳下载会较慢", "i18n_bb4409015b": "机器 ssh 名", "i18n_bb4740c7a7": "执行 命令", "i18n_bb5aac6004": "构建产物同步到文件中心保留天数", "i18n_bb667fdb2a": "未报警", "i18n_bb7eeae618": "仅统计：", "i18n_bb8d265c7e": "版本需要大于 18", "i18n_bb9a581f48": "登录成功,需要验证 MFA", "i18n_bb9ef827bf": "禁止访问", "i18n_bba360b084": "真的要删除对应的触发器吗？", "i18n_bbbaeb32fc": "机器延迟", "i18n_bbcaac136c": "表中的错误数据吗？", "i18n_bbd63a893c": "自动检测服务端所在服务器中是否存在 docker，如果存在将自动新增到列表中", "i18n_bbf2775521": "镜像名称", "i18n_bc2c23b5d2": "修剪操作会删除相关数据，请谨慎操作。请您再确认本操作后果后再使用", "i18n_bc2f1beb44": "真的要解锁用户么？", "i18n_bc4b0fd88a": "网络 Reachable 测试", "i18n_bc8752e529": "分发项目", "i18n_bcaf69a038": "请选择一个节点", "i18n_bcc4f9e5ca": "如", "i18n_bcf48bf7a8": "授权 url", "i18n_bcf83722c4": "变量描述", "i18n_bd0362bed3": "新增分组", "i18n_bd49bc196c": "编辑项目", "i18n_bd4e9d0ee2": "原始名：", "i18n_bd5d9b3e93": "使用哪个 docker 构建,填写 docker 标签（ 标签在 docker 编辑页面配置） 默认查询可用的第一个,如果tag 查询出多个将依次构建", "i18n_bd6c436195": "请输入脚本描述", "i18n_bd7043cae3": "远程下载", "i18n_bd7c7abc8c": "分组为虚拟逻辑分组，并非独立管理分组数据（如果在此处新增后并未保存相关数据对应的分组不会被保存）", "i18n_bd7c8c96bc": "手动上传", "i18n_bda44edeb5": "不能操作", "i18n_bdc1fdde6c": "beta计划：", "i18n_bdd4cddd22": "将还原【", "i18n_bdd87b63a6": "微信二维码", "i18n_bdd9d38d7e": "列宽", "i18n_be166de983": "软链的项目", "i18n_be1956b246": "深色2 blackboard", "i18n_be2109e5b1": "确定要重置用户密码吗？", "i18n_be24e5ffbe": "Java 项目（java -jar xxx）", "i18n_be28f10eb6": "请选择发布的一级目录和填写二级目录", "i18n_be381ac957": "请选择要使用的仓库", "i18n_be3a4d368e": "分发中、2：分发结束、3：已取消、4：分发失败", "i18n_be4b9241ec": "默认状态码为 200 表示执行成功", "i18n_be5b6463cf": "语法参考", "i18n_be5fbbe34c": "保存", "i18n_beafc90157": "分发到机器节点中的脚本库在节点脚本支持使用 G{'@'}(\"xxxx\")格式来引用，当存在引用时系统会自动替换引用脚本库中的脚本内容", "i18n_bebcd7388f": "加载构建数据中", "i18n_bec98b4d6a": "状态：", "i18n_becc848a54": "私钥文件绝对路径（绝对路径前面新增 file", "i18n_bef1065085": "Chrome 扩展", "i18n_bf0e1e0c16": "输入仓库名称或者仓库路径进行{slot1}", "i18n_bf77165638": "您确定要重启当前容器吗？", "i18n_bf7da0bf02": "新密码", "i18n_bf91239ad7": "命令描述", "i18n_bf93517805": "下列配置信息仅在当前浏览器生效", "i18n_bf94b97d1a": "修改时间：", "i18n_bfacfcd978": "将取消自动加载环境变量", "i18n_bfc04cfda7": "分支", "i18n_bfda12336c": "搜索查看", "i18n_bfe68d5844": "链接", "i18n_bfe8fab5cd": "需要配置授权目录（授权才能正常使用发布）,授权目录主要是用于确定可以发布到哪些目录中", "i18n_bfed4943c5": "参数值", "i18n_c00fb0217d": "请填写用户昵称", "i18n_c03465ca03": "禁用数量", "i18n_c0996d0a94": " ：每周一和周二的11:59执行", "i18n_c0a9e33e29": "请选择构建对应的分支,必选", "i18n_c0ad27a701": "实际可用环境变量", "i18n_c0d19bbfb3": "请输入 key 的值", "i18n_c0d38f475f": "软内存", "i18n_c0d5d68f5f": "忽略", "i18n_c0e498a259": "点击图标查看关联的所有任务", "i18n_c0f4a31865": "逻辑删除", "i18n_c11eb9deff": "文件MD5", "i18n_c12ba6ff43": "我们有权利追诉破坏开源并因此获利的团队个人的全部违法所得，也欢迎给我们提供侵权线索。", "i18n_c163613a0d": "如果当前集群还存在可能出现数据不一致问题奥", "i18n_c1690fcca5": "导入证书", "i18n_c16ab7c424": "】 吗？注意：取消/停止构建不一定能正常关闭所有关联进程", "i18n_c1786d9e11": "节点地址", "i18n_c17aefeebf": "系统名：", "i18n_c18455fbe3": "授权信息错误", "i18n_c195df6308": "异常", "i18n_c1af35d001": "构建产物", "i18n_c1b72e7ded": "为变量名称", "i18n_c23fbf156b": "未选择ssh", "i18n_c26e6aaabb": "擅自修改或者删除版权信息有法律风险", "i18n_c2add44a1d": "一些例子：", "i18n_c2b2f87aca": "脚本孤独数据", "i18n_c2ee58c247": "构建命令", "i18n_c2f11fde3a": "初始化系统账户", "i18n_c31ea1e3c4": "没有任何操作日志", "i18n_c325ddecb1": "CPU 周期的长度，以微秒为单位。", "i18n_c32e7adb20": "请输入远程下载安全HOST，回车支持输入多个路径，示例 https://www.test.com 等", "i18n_c34175dbef": "控制台日志备份路径: ", "i18n_c3490e81bf": "重启创建之前会自动将之前的容器删除掉", "i18n_c34f1dc2b9": "参数中的 id 、token 和触发构建一致", "i18n_c3512a3d09": "请选选择类型", "i18n_c35c1a1330": "排序值", "i18n_c360e994db": "排序", "i18n_c36ab9a223": "为 docker bridge 上的容器创建一个新的网络堆栈", "i18n_c37ac7f024": "清除代码", "i18n_c3aeddb10d": "当前工作空间还没有逻辑节点不能创建节点分发奥", "i18n_c3f28b34bb": "集群名称", "i18n_c43743d213": "推送后删除", "i18n_c446efd80d": "编辑 Script", "i18n_c4535759ee": "系统提示", "i18n_c46938460b": "系统使用 docker http 接口实现和 docker 通讯和管理，但是默认", "i18n_c469afafe0": "您确定要删除当前容器吗？", "i18n_c494fbec77": "手动新增", "i18n_c4a61acace": "命令？", "i18n_c4b5d36ff0": "节点状态会自动识别服务器中是否存在 java 环境,如果没有 Java 环境不能快速安装节点", "i18n_c4cfe11e54": "如果上传的压缩文件是否自动解压 支持的压缩包类型有 tar", "i18n_c4e0c6b6fe": "筛选项目", "i18n_c4e2cd2266": "还需要对相关数据都操作后才能达到预期排序", "i18n_c5099cadcd": "插件数", "i18n_c53021f06d": "填写【xxx", "i18n_c530a094f9": "构建方式:", "i18n_c538b1db4a": "软链项目（类似于项目副本使用相关路径的文件）", "i18n_c583b707ba": "个参数的描述", "i18n_c5a2c23d89": "非全屏", "i18n_c5aae76124": "绑定 SSH", "i18n_c5bbaed670": "状态码错误", "i18n_c5c3583bfc": "发送验证码", "i18n_c5c69827c5": "等网络端口限制", "i18n_c5de93f9c7": "需要手动确认", "i18n_c5e7257212": "当前节点ID：", "i18n_c5f9a96133": "系统默认将对 demo 账号限制很多权限。非演示场景不建议使用 demo 账号", "i18n_c600eda869": "命令文件将在 {'${数据目录}'}/script/xxxx.sh、bat 执行", "i18n_c618659cea": "自定义分支通配表达式", "i18n_c6209653e4": "SMTP 服务器域名", "i18n_c68dc88c51": "请输入监控名称", "i18n_c6a3ebf3c4": "接收大小", "i18n_c6c2497dbe": "请输入项目日志文件的编码格式，默认跟随全局配置", "i18n_c6e4cddba0": "请选择监控的功能", "i18n_c6f1c6e062": "目录不能编辑", "i18n_c6f6a9b234": "迁移到其他工作空间", "i18n_c704d971d6": "请填写构建和产物", "i18n_c7099dabf6": "正在上传文件", "i18n_c71a67ab03": "分发后", "i18n_c75b14a04e": "监控频率可以到服务端配置文件中修改", "i18n_c75d0beca8": "开启页面操作引导、导航", "i18n_c7689f4c9a": "这里构建命令最终会在服务器上执行。如果有多行命令那么将", "i18n_c76cfefe72": "端口", "i18n_c7c4e4632f": ",更新期间允许的失败率", "i18n_c7e0803a17": "密码只会出现一次，关闭窗口后无法再次查看密码", "i18n_c806d0fa38": "压缩包", "i18n_c83752739f": "支持的字段可以通过接口返回的查看", "i18n_c840c88b7c": "真的要清空 【", "i18n_c84ddfe8a6": "执行日志", "i18n_c8633b4b77": "通过私人令牌导入仓库", "i18n_c87bd94cd7": "镜像Id: ", "i18n_c889b9f67d": "新增关联项目", "i18n_c89e9681c7": "临时文件占用空间", "i18n_c8a2447aa9": "加入", "i18n_c8b2aabc07": "文件中如果不存在：MemAvailable，那么 MemAvailable = MemFree+Active(file)+Inactive(file)+SReclaimable，所以本系统 中内存占用计算方式：内存占用=(total-(MemFree+Active(file)+Inactive(file)+SReclaimable))/total", "i18n_c8c452749e": "选择 SQL 文件", "i18n_c8c45e8467": "请根据自身项目启动时间来配置", "i18n_c8c6e37071": "温馨提醒", "i18n_c8ce4b36cb": "重命名", "i18n_c90a1f37ce": "节点id", "i18n_c96b442dfb": "通用邮箱 SSL", "i18n_c96f47ec1b": "异步请求不能保证有序性", "i18n_c972010694": "产物目录", "i18n_c9744f45e7": "否", "i18n_c97e6e823a": "重新启动容器，除非它已被停止", "i18n_c983743f56": "总内存", "i18n_c996a472f7": "每天0/12点刷新一次", "i18n_c99a2f7ed8": "启动命令", "i18n_c9b0f8e8c8": "真的要删除", "i18n_c9b79a2b4f": "全局代理配置后将对服务端的网络生效，代理实现方式：ProxySelector", "i18n_c9daf4ad6b": "多线程", "i18n_ca32cdfd59": "内存使用率：", "i18n_ca527c48cf": "是否将本次发布的信息存储为发布任务模板，方便下次快捷使用相同配置信息来发布任务。", "i18n_ca69dad8fc": "流程执行完脚本后，输出的内容最后一行必须为：running", "i18n_ca774ec5b4": "上次构建基于 commitId：", "i18n_caa9b5cd94": "需要将标签值配置到构建 DSL 中的", "i18n_cab7517cb4": "节点地址：", "i18n_cabdf0cd45": "选择产物", "i18n_cac26240b5": "容器日志", "i18n_cac6ff1d82": "批量获取构建状态地址", "i18n_cad01fe13c": "黑白 ambiance-mobile", "i18n_caed797183": "reserveCount 表示触发器生成的备份保留个数", "i18n_caf335a345": "java信息", "i18n_cb09b98416": "个性配置区", "i18n_cb156269db": "单位秒,最小值 1 秒", "i18n_cb25f04b46": "在文件最后 3 行中搜索", "i18n_cb28aee4f0": "节点分发【暂不支持迁移】", "i18n_cb46672712": "日志阅读 【暂不支持迁移】", "i18n_cb93a1f4a5": "、root、manager", "i18n_cb951984f2": "已存在", "i18n_cb9b3ec760": "批量触发参数 BODY json： [ { \"id\":\"1\", \"token\":\"a\",\"action\":\"status\" } ]", "i18n_cbc44b5663": "执行时间结束", "i18n_cbcc87b3d4": "、名称、版权等", "i18n_cbce8e96cf": "证书信息", "i18n_cbd9ffe1b8": "sudo执行", "i18n_cbdc4f58f6": "请输入机器的名称", "i18n_cbdcabad50": "释放", "i18n_cbee7333e4": "本地命令是指,在服务端本地执行多条命令来实现发布", "i18n_cc3a8457ea": "节点状态是异步获取有一定时间延迟", "i18n_cc42dd3170": "开启", "i18n_cc51f34aa4": "编辑服务", "i18n_cc5dccd757": "工作空间中逻辑节点中脚本模版数量：", "i18n_cc617428f7": "隐私变量是指一些密码字段或者关键密钥等重要信息，隐私字段只能修改不能查看（编辑弹窗中无法看到对应值）。 隐私字段一旦创建后将不能切换为非隐私字段", "i18n_cc637e17a0": "产物:", "i18n_cc92cf1e25": "请填写产物目录", "i18n_cc9a708364": "状态码错误 ", "i18n_cca4454cf8": "请输入公告内容", "i18n_ccb2fdd838": "切换工作空间", "i18n_ccb91317c5": "命令内容", "i18n_ccea973fc7": "当前节点地址：", "i18n_cd1aedc667": "在 设置 --> 应用 --> 生成令牌", "i18n_cd649f76d4": "时间范围", "i18n_cd998f12fa": "当目标工作空间已经存在节点时候将自动同步节点授权信息、代理配置信息", "i18n_cda84be2f6": "操作日志", "i18n_cdc478d90c": "系统名", "i18n_ce043fac7d": "当前工作空间还没有SSH", "i18n_ce07501354": "点击数字查看运行中的任务", "i18n_ce1c5765e4": "查看发布模板", "i18n_ce1ecd8a5b": "还有更多相关依赖开源组件", "i18n_ce23a42b47": "任务名", "i18n_ce40cd6390": "关联脚本", "i18n_ce559ba296": "运行命令", "i18n_ce7e6e0ea9": "当前配置仅对选择的工作空间生效,其他工作空间需要另行配置", "i18n_ce84c416f9": "请输入用户信息 url [userInfoUri]", "i18n_ced3d28cd1": "不扫描", "i18n_ceee1db95a": "容器端口", "i18n_ceffe5d643": "两步验证应用", "i18n_cf38e8f9fd": "当前为节点分发的授权路径配置", "i18n_cfa72dd73a": "请输入要检查的 cron 表达式", "i18n_cfb00269fd": "执行脚本", "i18n_cfbb3341d5": "当前登录的账号是：", "i18n_cfd482e5ef": "暂无数据,请先新增节点项目数据", "i18n_cfeea27648": "创建文件 /xxx/xxx/xxx", "i18n_cfeff30d2c": "目录：", "i18n_d00b485b26": "回滚", "i18n_d0132b0170": "还原过程中不能操作哦...", "i18n_d02a9a85df": "请选择报警联系人", "i18n_d047d84986": "个 / 共", "i18n_d0874922f0": "绑定指定目录可以在线管理，同时构建 ssh 发布目录也需要在此配置", "i18n_d0a864909b": "方式生成公私钥", "i18n_d0b2958432": "版本号", "i18n_d0b7462bdc": "此编辑仅能编辑当前 SSH 在此工作空间的名称信息", "i18n_d0be2fcd05": "：表示间隔时间，例如在分上，表示每两分钟，同样*可以使用数字列表代替，逗号分隔", "i18n_d0c06a0df1": "请输入验证码", "i18n_d0c879f900": "上传前", "i18n_d0eddb45e2": "私钥", "i18n_d0f53484dc": "脚本ID：", "i18n_d1498d9dbf": "构建备注", "i18n_d159466d0a": "关联数据名", "i18n_d175a854a6": "构建目录", "i18n_d17eac5b5e": "我要加入", "i18n_d18d658415": "脚本ID", "i18n_d19bae9fe0": "插件端安装并启动成功后将主动上报节点信息,如果上报的 IP+PROP 能正常通讯将新增节点信息", "i18n_d1aa9c2da9": "子网掩码：", "i18n_d1b8eaaa9e": "需要输入验证码,确认绑定后才生效奥", "i18n_d1f0fac71d": "没有选择节点不能保存脚本", "i18n_d1f56b0a7e": "传入参数有：monitorId、monitorName、nodeId、nodeName、projectId、projectName、title、content、runStatus", "i18n_d242bc3990": "# 您还可以自定义基础镜像来实现复杂业务", "i18n_d263a9207f": "支持 html 格式", "i18n_d27cf91998": "参考地址：", "i18n_d2913cea31": "确定要更改下载授权码码？请您自行确认授权码是否正确，仅到触发下载时才能使用到下载授权码，错误的授权码将不能正常下载更新包", "i18n_d2cac1245d": "是否将【", "i18n_d2e2560089": "文件名称", "i18n_d2f484ff7e": "。如果输出最后一行不是预期格式项目状态将是未运行", "i18n_d2f4a1550a": "没有任何节点脚本", "i18n_d301fdfc20": "开启自动创建用户后第一次登录仅自动创建账号，还需要管理员手动分配权限组", "i18n_d30b8b0e43": "未构建", "i18n_d31d625029": "加入 beta 计划可以及时获取到最新的功能、一些优化功能、最快修复 bug 的版本，但是 beta 版也可能在部分新功能上存在不稳定的情况。", "i18n_d324f8b5c9": " 替换", "i18n_d35a9990f4": "已无更多节点项目，请先创建项目", "i18n_d373338541": "支持IP 地址、域名、主机名", "i18n_d3ded43cee": "查看构建日志", "i18n_d3e480c8c0": "失败次数", "i18n_d3fb6a7c83": ",2 秒后将自动跳转到登录页面", "i18n_d40b511510": "证书", "i18n_d438e83c16": "项目分组", "i18n_d4744ce461": "还没有配置权限组,不能创建用户", "i18n_d47ea92b3a": "编辑证书", "i18n_d4aea8d7e6": "执行次数", "i18n_d4e03f60a9": "插件端启动的时候检查项目状态，如果项目状态是未运行则尝试执行启动项目", "i18n_d5269713c7": "表示构建产物为文件夹时将打包为", "i18n_d57796d6ac": " ：范围：0~59", "i18n_d584e1493b": "搜索ssh名称", "i18n_d58a55bcee": "关", "i18n_d5a73b0c7f": "上传", "i18n_d5c2351c0e": "请选择可以执行的星期", "i18n_d5c68a926e": "执行顺序", "i18n_d5d46dd79b": "分钟级别", "i18n_d615ea8e30": "选择升级文件", "i18n_d61af4e686": "断点/分片别名下载", "i18n_d61b8fde35": "切换集群", "i18n_d64cf79bd4": "确认要加入 beta 计划吗？", "i18n_d65551b090": "分片上传文件", "i18n_d65d977f1d": "填写运行参数", "i18n_d679aea3aa": "运行中", "i18n_d6937acda5": "项目存储的文件夹，jar 包存放的文件夹", "i18n_d6a5b67779": "系统进程", "i18n_d6cdafe552": "会话已经关闭[project-console]", "i18n_d6eab4107a": "Java 项目（java -jar Springboot war）【不推荐】", "i18n_d72471c540": "浏览器标识", "i18n_d731dc9325": "时间戳：", "i18n_d7471c0261": "请选择执行节点", "i18n_d75c02d050": "停止项目", "i18n_d7ac764d3a": "分发间隔时间 （顺序重启、完整顺序重启）方式才生效", "i18n_d7ba18c360": "分发节点是指在编辑完脚本后自动将脚本内容同步节点的脚本中", "i18n_d7bebd0e5e": "状态操作请到控制台中控制", "i18n_d7c077c6f6": "命令日志", "i18n_d7cc44bc02": "用户资料", "i18n_d7d11654a7": "不存在", "i18n_d7ec2d3fea": "名称", "i18n_d7ee59f327": "私钥,不填将使用默认的 $HOME/.ssh 目录中的配置。支持配置文件目录:file:", "i18n_d7ef19d05b": "还没有配置模板节点", "i18n_d81bb206a8": "无", "i18n_d82ab35b27": "文件前N行", "i18n_d82b19148f": "请选择要同步授权的机器节点", "i18n_d83aae15b5": "在线构建文件主要保存，仓库文件，构建历史产物等。不支持主动清除，如果文件占用过大可以配置保留规则和对单个构建配置是否保存仓库、产物文件等", "i18n_d84323ba8d": "仓库自动迁移后可能会重复存在请手动解决", "i18n_d85279c536": "上传时请勿刷新或者关闭浏览器窗口。", "i18n_d87940854f": "计划次数", "i18n_d87f215d9a": "卡片", "i18n_d88651584f": "剩余空间", "i18n_d8a36a8a25": "编辑 Docker 集群", "i18n_d8bf90b42b": "其他用户可以配置权限解除限制", "i18n_d8c7e04c8e": "信息", "i18n_d8db440b83": "您需要根据您业务情况来评估是否可以加入 beta。", "i18n_d911cffcd5": "下载地址", "i18n_d921c4a0b6": "真的要删除“", "i18n_d926e2f58e": "取消任务", "i18n_d937a135b9": "浅色 eclipse", "i18n_d94167ab19": "网络端口", "i18n_d9435aa802": "解析模式", "i18n_d9531a5ac3": "还没有配置权限组不能创建用户奥", "i18n_d9569a5d3b": "多IP可以使用", "i18n_d9657e2b5f": "请输入项目文件夹", "i18n_d9ac9228e8": "创建", "i18n_d9c28e376c": "功能管理", "i18n_da1abf0865": "验证码 6 为纯数字", "i18n_da1cb76e87": "请输入脚本内容", "i18n_da317c3682": "【推荐】使用快速安装方式导入机器并自动新增逻辑节点", "i18n_da4495b1b4": "邮箱：", "i18n_da509a213f": "工作空间用于隔离数据,工作空间下面可以有不同数据,不同权限,不同菜单等来实现权限控制", "i18n_da671a4d16": "微信：", "i18n_da79c2ec32": "配置示例", "i18n_da89135649": "企业服务", "i18n_da8cb77838": "在线升级", "i18n_da99dbfe1f": "分发状态", "i18n_dab864ab72": "快速绑定", "i18n_dabdc368f5": "请选择分配类型", "i18n_dacc2e0e62": "硬件硬盘", "i18n_dadd4907c2": "】目录,", "i18n_daf783c8cd": "分", "i18n_db06c78d1e": "测试", "i18n_db094db837": "修改变量值地址", "i18n_db2d99ed33": "还未配置集群地址,不能切换集群", "i18n_db4470d98d": "报警状态", "i18n_db4b998fbd": "Oauth2 使用提示", "i18n_db5cafdc67": "真的要解绑节点么？", "i18n_db686f0328": "公钥,不填将使用默认的 $HOME/.ssh 目录中的配置。支持配置文件目录:file:", "i18n_db709d591b": "不同步", "i18n_db732ecb48": "延迟", "i18n_db81a464ba": "执行构建时会生成一个容器来执行，构建结束后会自动删除对应的容器", "i18n_db9296212a": "定时构建", "i18n_dba16b1b92": "构建事件", "i18n_dbad1e89f7": "两步验证", "i18n_dbb166cf29": "服务id", "i18n_dbb2df00cf": "发布目录", "i18n_dbba7e107a": "发布项目", "i18n_dbc0b66ca4": "个结果", "i18n_dc0d06f9c7": "请填写发布的二级目录", "i18n_dc2961a26f": "节点名：", "i18n_dc2c61a605": "自建 Gitlab", "i18n_dc32f465da": "容器地址 tcp", "i18n_dc3356300f": "如果要配置 SSH 请到【系统管理】-> 【资产管理】-> 【SSH 管理】中去配置。", "i18n_dc39b183ea": "确定要忽略绑定两步验证吗？强烈建议超级管理员开启两步验证来保证账号安全性", "i18n_dcc846e420": "批量构建全部参数举例", "i18n_dcd72e6014": "获取单个构建状态地址", "i18n_dce5379cb9": "隐藏", "i18n_dcf14deb0e": "代理地址 (127.0.0.1:8888)", "i18n_dd1d14efd6": "查看脚本", "i18n_dd23fdf796": "编辑过程中可以切换节点但是要注意数据是否匹配", "i18n_dd4e55c39c": "未开始", "i18n_dd95bf2d45": "正常登录", "i18n_dda8b4c10f": "分片上传", "i18n_ddc7d28b7b": "变量", "i18n_dddf944f5f": "重置页面操作引导、导航成功", "i18n_ddf0c97bce": "请注意备份数据防止数据丢失！！", "i18n_ddf7d2a5ce": "命令", "i18n_de17fc0b78": "硬盘最大的使用率：", "i18n_de3394b14e": "没有资产机器", "i18n_de4cf8bdfa": "付费加入我们的技术交流群优先解答您所有疑问", "i18n_de5dadc480": "pull日志", "i18n_de6bc95d3b": "清空当前目录文件", "i18n_de78b73dab": "单个触发器地址", "i18n_debdfce084": "请输入集群名称", "i18n_decef97c7c": "服务端IP授权配置", "i18n_deea5221aa": "标记", "i18n_df011658c3": "范围", "i18n_df1da2dc59": "容器在一个 CPU 周期内可以获得的 CPU 时间的微秒。", "i18n_df3833270b": "地址：", "i18n_df39e42127": "自动执行", "i18n_df59a2804d": "禁止扫描", "i18n_df5f80946d": "# node 镜像源 https://registry.npmmirror.com/-/binary/node/", "i18n_df9497ea98": "存在", "i18n_df9d1fedc5": "节点分发是指,一个项目部署在多个节点中使用节点分发一步完成多个节点中的项目发布操作", "i18n_dfb8d511c7": "用户名称", "i18n_dfcc9e3c45": "分发后操作", "i18n_e039ffccc8": "】此文件还原到项目目录？", "i18n_e049546ff3": "【系统配置目录】中修改", "i18n_e06497b0fb": "查看当前可用容器", "i18n_e06caa0060": "文件修改时间", "i18n_e074f6b6af": "SMTP 服务器端口", "i18n_e07cbb381c": "没有任何仓库", "i18n_e09d0d8c41": "不建议使用常用名称如", "i18n_e0a0e26031": "使用当前镜像创建一个容器", "i18n_e0ae638e73": "保留产物是指对在构建完成后是否保留构建产物相关文件，用于回滚", "i18n_e0ba3b9145": "节点脚本", "i18n_e0ce74fcac": "自动滚动", "i18n_e0d6976b48": "请选择集群关联分组", "i18n_e0ea800e34": "打包正式环境 npm i && npm run build:prod", "i18n_e0ec07be7d": "客户端密钥", "i18n_e0f937d57f": "临时token", "i18n_e0fcbca309": "工作空间ID：", "i18n_e15f22df2d": "真的要清除构建信息么？", "i18n_e166aa424d": "关于系统", "i18n_e17a6882b6": "脚本标记", "i18n_e19cc5ed70": "同步节点授权", "i18n_e1c965efff": "请选择状态", "i18n_e1fefde80f": "节点账号密码默认由系统生成：可以通过插件端数据目录下 agent_authorize.json 文件查看（如果自定义配置了账号密码将没有此文件）", "i18n_e222f4b9ad": "执行前需要检查命令中的地址在对应的服务器中是否可以访问,如果无法访问将不能自动绑定节点,", "i18n_e235b0d4af": "提交ID: ", "i18n_e257dd2607": "请选择SSH连接信息", "i18n_e26dcacfb1": " 检查 ", "i18n_e2adcc679a": "单位 ns 秒", "i18n_e2b0f27424": "项目ID仅会在机器节点中限制唯一，不同工作空间（相同的工作空间）下是允许相同的项目ID", "i18n_e2be9bab6b": "复制下面任意一条命令到还未安装插件端的服务器中去执行,执行前需要放行", "i18n_e2d8fba259": " 不保存", "i18n_e2f942759e": "会话异常", "i18n_e30a93415b": "使用私人令牌，可以在你不输入账号密码的情况下对你账号内的仓库进行管理，你可以在创建令牌时指定令牌所拥有的权限。", "i18n_e319a2a526": "重置为重新生成触发地址,重置成功后之前的触发器地址将失效,构建触发器绑定到生成触发器到操作人上,如果将对应的账号删除触发器将失效", "i18n_e31ca72849": "上传压缩文件", "i18n_e354969500": "令牌导入", "i18n_e362bc0e8a": "路径：{source}(宿主机) => {destination}(容器)", "i18n_e39de3376e": "分配", "i18n_e39f4a69f4": "脚本数", "i18n_e39ffe99e9": "请输入密码", "i18n_e3cf0abd35": "邮箱验证码", "i18n_e3e85de50c": "请选择构建方式", "i18n_e3ead2bd0d": "常见构建命令示例", "i18n_e3ee3ca673": "不追加脚本模板", "i18n_e4013f8b81": "机器名称", "i18n_e414392917": "集群信息：", "i18n_e42b99d599": "月", "i18n_e43359ca06": "请选择 SSH节点", "i18n_e44f59f2d9": "发布前命令", "i18n_e475e0c655": "证书共享", "i18n_e48a715738": "新建文件", "i18n_e4b51d5cd0": "运行状态", "i18n_e4bea943de": "仓库地址", "i18n_e4bf491a0d": "正在下载，请稍等...", "i18n_e4d0ebcd58": "选择集群", "i18n_e5098786d3": "args 参数", "i18n_e54029e15b": "退出集群", "i18n_e54c5ecb54": "编辑构建", "i18n_e5915f5dbb": "(存在兼容问题,实际使用中需要提前测试) python3 sdk 镜像使用：https://repo.huaweicloud.com/python/{'${PYTHON3_VERSION}'}/Python-{'${PYTHON3_VERSION}'}.tar.xz", "i18n_e5a63852fd": "节点密码,请查看节点启动输出的信息", "i18n_e5ae5b36db": "关键词高亮,支持正则(正则可能影响性能请酌情使用)", "i18n_e5f71fc31e": "搜索", "i18n_e5fae81ed4": "没有搜索到任何 SSH 信息", "i18n_e60389f6d6": "当前前端打包时间：", "i18n_e60725e762": "周三", "i18n_e63fb95deb": "队列数", "i18n_e64d788d11": "升级成功", "i18n_e6551a2295": "引用工作空间环境变量可以方便后面多处使用相同的密码统一修改", "i18n_e6bf31e8e6": "长期token", "i18n_e6cde5a4bc": "没有检查到最新版", "i18n_e6e453d730": "请输入变量名称", "i18n_e6e5f26c69": "QQ邮箱 SSL", "i18n_e703c7367c": "当前状态：", "i18n_e710da3487": "用时", "i18n_e72a0ba45a": "用户组", "i18n_e72f2b8806": "输入仓库名称或者仓库路径进行搜索", "i18n_e747635151": "Script 名称", "i18n_e76e6a13dd": "不引用环境变量", "i18n_e78e4b2dc4": "级别", "i18n_e7d83a24ba": "成功次数", "i18n_e7e8d4c1fb": "断点/分片下载", "i18n_e7ffc33d05": "上传后执行", "i18n_e8073b3843": "请选择用户权限组", "i18n_e825ec7800": "协议类型", "i18n_e8321f5a61": "发布方式：", "i18n_e83a256e4f": "确认", "i18n_e84b981eb4": "配置值 （如：5g）", "i18n_e8505e27f4": "升级前请阅读更新日志里面的说明和注意事项并且", "i18n_e8e3bfbbfe": "确认关闭", "i18n_e8f07c2186": "如果未填写将解析压缩包里面的 txt", "i18n_e9290eaaae": "关闭左侧", "i18n_e930e7890f": "表达式类似于Linux的crontab表达式，表达式使用空格分成5个部分，按顺序依次为：", "i18n_e95f9f6b6e": "SSL 连接", "i18n_e96705ead1": "如果按钮不可用则表示当前节点已经关闭啦,需要去编辑中启用", "i18n_e976b537f1": "缓存监控", "i18n_e97a16a6d7": " ：每两分钟执行", "i18n_e9bd4484a7": "发送方邮箱账号", "i18n_e9c2cb1326": "次要ID", "i18n_e9e9373c6f": "执行任务中", "i18n_e9ea1e7c02": "文件保存天数,默认 3650 天", "i18n_e9ec2b0bee": "并等待上一个项目启动完成才能关闭下一个项目", "i18n_e9f2c62e54": ",新增默认参数后在手动执行脚本时需要填写参数值", "i18n_ea15ae2b7f": "选项", "i18n_ea3c5c0d25": "临时文件占用空间：", "i18n_ea58a20cda": "机器DOCKER", "i18n_ea7fbabfa1": "请输入账户名", "i18n_ea89a319ec": "# 宿主机目录和容器目录挂载 /host:/container:ro", "i18n_ea8a79546f": "请输入发布的文件id", "i18n_ea9f824647": "拉取仓库超时时间,单位秒", "i18n_eaa5d7cb9b": "过期天数", "i18n_eadd05ba6a": "中等", "i18n_eaf987eea0": "权重（相对权重）。", "i18n_eb164b696d": "排除发布", "i18n_eb5bab1c31": "非必填", "i18n_eb79cea638": "周五", "i18n_eb7f9ceb71": "脚本库：", "i18n_eb969648aa": "请提前备份数据再操作奥", "i18n_ebc2a1956b": "编辑监控", "i18n_ebc96f0a5d": "总内存（内存 + 交换）。 设置为 -1 以禁用交换。", "i18n_ec1f13ff6d": "总数：", "i18n_ec219f99ee": "执行结束", "i18n_ec22193ed1": "请选择分组", "i18n_ec537c957a": "{slot1}机目录", "i18n_ec6e39a177": "确认要下载更新最新版本吗？", "i18n_ec7ef29bdf": "请输入静态，回车支持输入多个路径，系统会自动过滤 ../ 路径、不允许输入根路径", "i18n_ec989813ed": "状态信息：", "i18n_eca37cb072": "创建时间", "i18n_ecdf9093d0": "同时展开多个", "i18n_ecff77a8d4": "使用", "i18n_ed145eba38": "硬盘占用", "i18n_ed19a6eb6f": "在线构建文件占用空间", "i18n_ed367abd1a": "修改用户资料", "i18n_ed39deafd8": "编辑仓库", "i18n_ed40308fe9": "# maven 镜像源 https://mirrors.tuna.tsinghua.edu.cn/apache/maven/maven-3/", "i18n_ed6a8ee039": " ：表示多个定时表达式", "i18n_ed8ea20fe6": "安装ID", "i18n_edb4275dcd": "gmail邮箱", "i18n_edb881412a": "注意：为了避免不必要的事件执行脚本，选择的脚本的备注中包含需要实现的事件参数关键词，如果需要执行 success 事件,那么选择的脚本的备注中需要包含 success 关键词", "i18n_edc1185b8e": "尝试自动续签失败", "i18n_edd716f524": "请选择发布的一级目录", "i18n_ede2c450d1": "没有任何登录日志", "i18n_ee19907fad": "# 基础镜像 目前支持 ubuntu-latest、ubuntu-git、ubuntu-1ms-latest、ubuntu-1ms-git", "i18n_ee4fac2f3c": "为了避免部分节点不能及时响应造成监控阻塞,节点统计超时时间不受节点超时配置影响将采用默认超时时间(10秒)", "i18n_ee6ce96abb": "s 秒", "i18n_ee8ecb9ee0": "优先级", "i18n_ee9a51488f": "请输入发布目录", "i18n_eeb6908870": "上一步", "i18n_eec342f34e": "默认账号为：jpomAgent", "i18n_eee6510292": "配置授权目录", "i18n_eee83a9211": "资源", "i18n_eeef8ced69": "解绑会检查数据关联性,同时将自动删除节点项目和脚本缓存信息", "i18n_eef3653e9a": "jvm{slot1},{slot2}.如：-Xms512m -Xmx512m", "i18n_eef4dfe786": "Java 项目（java -Djava.ext.dirs=lib -cp conf:run.jar $MAIN_CLASS）", "i18n_ef016ab402": "确认创建该", "i18n_ef28d3bff2": "页面内容自动撑开出现屏幕滚动条", "i18n_ef651d15b0": "创建之后不能修改,分发 ID 等同于项目 ID", "i18n_ef734bf850": "更多说明", "i18n_ef7e3377a0": "允许时段", "i18n_ef800ed466": "程序运行的 main 类(jar 模式运行可以不填)", "i18n_ef8525efce": "分配到其他工作空间", "i18n_ef9c90d393": "没有任何的脚本库", "i18n_efae7764ac": "账号登录", "i18n_efafd0cbd4": "密码（6-18位数字、字母、符号组合）", "i18n_efb88b3927": "系统运行时间", "i18n_efd32e870d": "插件构建时间", "i18n_efe71e9bec": "真的要解绑当前节点分发么？", "i18n_efe9d26148": "真的要删除该证书么，删除会将证书文件一并删除奥？", "i18n_f013ea9dcb": "加载中", "i18n_f038f48ce5": "编辑脚本", "i18n_f04a289502": "svn ssh 必填登录用户", "i18n_f05e3ec44d": "禁止访问,当前IP限制访问", "i18n_f06f95f8e6": "孤独数据", "i18n_f087eb347c": "构建命令示例", "i18n_f08afd1f82": "已选择", "i18n_f0a1428f65": "账号支持引用工作空间变量：", "i18n_f0aba63ae7": "颁发者", "i18n_f0db5d58cb": "开启两步验证使账号更安全", "i18n_f0eb685a84": "文件来兼容您的机器", "i18n_f105c1d31d": "最后执行人", "i18n_f113c10ade": "排空", "i18n_f11569cfa9": "展示的环境变量可能在实际执行中触发修改值或者新增变量的情况,请以最终执行变量为准", "i18n_f139c5cf32": "输入新名称", "i18n_f175274df0": "登录名称,账号,创建之后不能修改", "i18n_f1a2a46f52": "# 使用哪个 docker 构建,填写 docker 标签 默认查询可用的第一个,如果 tag 查询出多个也选择第一个结果", "i18n_f1b2828c75": "安装插件端", "i18n_f1d8533c7f": "请输入证书信息或者选择证书信息,证书信息填写规则：序列号:证书类型", "i18n_f1e3ef0def": "将镜像推送到远程仓库后自动删除本地镜像。如果开启了发布到集群服务不建议开启此选项，除非集群服务使用远程镜像", "i18n_f1fdaffdf0": "后台构建", "i18n_f240f9d69c": "分支名称：", "i18n_f26225bde6": "详情", "i18n_f26ef91424": "下载", "i18n_f27822dd8a": "提交消息: ", "i18n_f282058f75": "静态文件项目（前端、日志等）", "i18n_f2d05944ad": "创建 Docker 集群", "i18n_f30f1859ba": "如果您基于 Jpom 二次开发修改了", "i18n_f332f2c8df": "网关", "i18n_f3365fbf4d": "未获取到 Docker 或者禁用监控", "i18n_f33db5e0b2": "点击刷新构建信息", "i18n_f37f8407ec": "文件ID：", "i18n_f3947e6581": "开源不等同于免费", "i18n_f3e93355ee": "重启项目", "i18n_f425f59044": "系统版本：", "i18n_f4273e1bb4": " 文件ID", "i18n_f49dfdace4": "权限组", "i18n_f4b7c18635": "密码长度为6-20", "i18n_f4baf7c6c0": "未启动", "i18n_f4bbbaf882": "分支/标签", "i18n_f4dd45fca9": "请输入远程地址", "i18n_f4edba3c9d": "未知的表格类型", "i18n_f4fb0cbecf": "还没有任何结果", "i18n_f5399c620e": "真的要在该集群剔除此节点么？", "i18n_f562f75c64": "服务地址", "i18n_f56c1d014e": "执行成功", "i18n_f5c3795be5": "官方", "i18n_f5d0b69533": "完整的私钥内容 如", "i18n_f5d14ee3f8": "磁盘占用", "i18n_f5f65044ea": "容器安装的服务端不能使用本地构建（因为本地构建依赖启动服务端本地的环境，容器方式安装不便于管理本地依赖插件）", "i18n_f63345630c": "# 将容器中的 node_modules 文件缓存到 docker 卷中", "i18n_f63870fdb0": "请填写容器名称", "i18n_f652d8cca7": "尝试自动续签...", "i18n_f66335b5bf": "错误信息：", "i18n_f66847edb4": "网页应用ID", "i18n_f668c8c881": "集群名称：", "i18n_f685377a22": "脚本库 ", "i18n_f68f9b1d1b": "最后心跳时间", "i18n_f6d6ab219d": "更新完成后确实成功的时间", "i18n_f6d96c1c8c": "为了兼容Quartz表达式，同时支持6位和7位表达式，其中：", "i18n_f6dee0f3ff": "分发 ID", "i18n_f712d3d040": "备注示例：", "i18n_f71316d0dd": "替换引用", "i18n_f71a30c1b9": "数据目录占用空间", "i18n_f7596f3159": "如果需要在其他工作空间需要提前切换生成命令", "i18n_f76540a92e": "准备中", "i18n_f782779e8b": "结束时间", "i18n_f7b9764f0f": "项目启动,停止,重启都将请求对应的地址", "i18n_f7e8d887d6": "工作空间环境变量", "i18n_f7f340d946": "真的要清除 SSH 隐藏字段信息么？（密码，私钥）", "i18n_f8460626f0": "节点账号,请查看节点启动输出的信息", "i18n_f86324a429": "使用 ANT 表达式来实现在过滤指定目录来实现发布排除指定目录", "i18n_f89cc4807e": "授权路径是指项目文件存放到服务中的文件夹", "i18n_f89fa9b6c6": "选择仓库", "i18n_f8a613d247": "请选择节点", "i18n_f8b3165e0d": "当前项目被禁用", "i18n_f8f20c1d1e": "修剪在此时间戳之前创建的对象 例如：24h", "i18n_f8f456eb9a": "类型项目特有的 type：reload、restart", "i18n_f92d505ff5": "注意：系统默认监控 SSH是否正常会多次触发登录操作，如果密码错误或者安全防护规则有限制并无法解除限制时候可以配置指定分组禁用监控（详细配置参考配置文件）", "i18n_f932eff53e": "条数据", "i18n_f9361945f3": "主机名 hostname", "i18n_f967131d9d": "仓库名称", "i18n_f976e8fcf4": "监控名称", "i18n_f97a4d2591": "请选择要加入到哪个集群", "i18n_f9898595a0": "注意：同一个分组不建议被多个集群绑定", "i18n_f98994f7ec": "发布方式", "i18n_f99ead0a76": "镜像名称不正确 不能更新", "i18n_f9ac4b2aa6": "操作人", "i18n_f9c9f95929": "Java 项目（java -classpath）", "i18n_f9cea44f02": "当前工作空间还没有 Docker", "i18n_f9f061773e": "不填写则使用节点分发配置的二级目录", "i18n_fa2f7a8927": "失败策略", "i18n_fa4aa1b93b": "运行项目", "i18n_fa57a7afad": "容器标签,如：xxxx:latest 多个使用逗号隔开, 配置附加环境变量文件支持加载仓库目录下 .env 文件环境变量 如： xxxx:{'${VERSION}'}", "i18n_fa624c8420": "禁用后该用户不能登录平台", "i18n_fa7f6fccfd": "项目名称：", "i18n_fa7ffa2d21": "解锁", "i18n_fa8e673c50": "编辑工作空间", "i18n_faa1ad5e5c": "协议", "i18n_faaa995a8b": "可以关闭", "i18n_faaadc447b": "序号", "i18n_fabc07a4f1": "请选择监控操作", "i18n_fad1b9fb87": "新增脚本模版需要到节点管理中去新增", "i18n_fb1f3b5125": "当前工作空间关联数据统计", "i18n_fb3a2241bb": "状态描述：", "i18n_fb5bc565f3": "解析文件失败：", "i18n_fb61d4d708": "真的要回滚该构建历史记录么？", "i18n_fb7b9876a6": "请输入脚本名称", "i18n_fb852fc6cc": "进行中", "i18n_fb8fb9cc46": "统计说明", "i18n_fb91527ce5": "节点可用性：", "i18n_fb9d826b2f": "发布后执行的命令(非阻塞命令),一般是启动项目命令 如：ps -aux \"{'|'}\" grep java", "i18n_fba5f4f19a": "DSL环境变量", "i18n_fbd7ba1d9b": "最后分发时间", "i18n_fbee13a873": "工作空间总数：", "i18n_fbfa6c18bf": "已分配", "i18n_fbfeb76b33": "左边菜单栏主题切换", "i18n_fc06c70960": "您确定要删除当前镜像吗？", "i18n_fc4e2c6151": "登录用户", "i18n_fc5fb962da": "邮箱密码或者授权码", "i18n_fc92e93523": "生效时间", "i18n_fc954d25ec": "代理", "i18n_fcaef5b17a": "重用另一个容器网络堆栈", "i18n_fcb4c2610a": "通知异常", "i18n_fcb7a47b70": "阿里云企业邮箱", "i18n_fcba60e773": "构建", "i18n_fcbf0d0a55": "需要先安装依赖 yarn && yarn run build", "i18n_fcca8452fe": "集群地址主要用于切换工作空间自动跳转到对应的集群", "i18n_fcef976c7a": "私钥内容", "i18n_fd6e80f1e0": "正常", "i18n_fd7b461411": "不清空", "i18n_fd7e0c997d": "选择文件", "i18n_fd93f7f3d7": "可以将脚本分发到机器节点中在 DSL 项目中引用，达到多个项目共用相同脚本", "i18n_fda92d22d9": "关联节点会自动识别服务器中是否存在 java 环境,如果没有 Java 环境不能快速安装节点", "i18n_fdba50ca2d": "如果端口暴露到公网很", "i18n_fdbac93380": "SMTP 地址：smtp.mxhichina.com，端口使用 465 并且开启 SSL，用户名需要和邮件发送人一致，密码为邮箱的登录密码", "i18n_fdbc77bd19": "安全", "i18n_fdcadf68a5": "SMTP 端口", "i18n_fde1b6fb37": "需要提前为机器配置授权目录", "i18n_fdfd501269": "java sdk 镜像使用：https://mirrors.tuna.tsinghua.edu.cn/ 支持版本有：8, 9, 10, 11, 12, 13, 14, 15, 16, 17", "i18n_fe1b192913": "目录创建成功后需要手动刷新右边树才能显示出来哟", "i18n_fe231ff92f": "关闭页面操作引导、导航", "i18n_fe2df04a16": "版本", "i18n_fe32def462": "活跃", "i18n_fe7509e0ed": "值", "i18n_fe828cefd9": "项目文件夹是项目实际存放的目录名称", "i18n_fe87269484": "集群修改时间", "i18n_fea996d31e": "请填写构建名称", "i18n_fec6151b49": "账户名称", "i18n_feda0df7ef": "账号邮箱", "i18n_ff17b9f9cd": "企业微信", "i18n_ff1fda9e47": "禁止", "i18n_ff39c45fbc": "使用容器内的主机网络堆栈。 注意：主机模式赋予容器对本地系统服务（如 D-bus）的完全访问权限，因此被认为是不安全的。", "i18n_ff3bdecc5e": "文件查看（如果自定义配置了账号密码将没有此文件）", "i18n_ff80d2671c": "秒后刷新", "i18n_ff9814bf6b": "触发类型", "i18n_ff9dffec4d": "搜索模式", "i18n_ffa9fd37b5": "工作空间管理", "i18n_ffaf95f0ef": "启动的容器 可以看到很多host上的设备 可以执行mount。 可以在docker容器中启动docker容器。", "i18n_ffd67549cf": "：范围：1~12，同时支持不区分大小写的别名：\"jan\",\"feb\", \"mar\", \"apr\", \"may\",\"jun\", \"jul\", \"aug\",\"sep\",\"oct\", \"nov\", \"dec\"", "i18n_fffd3ce745": "共享"}