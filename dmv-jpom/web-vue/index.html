<!--

    Copyright (c) 2019 Of Him Code Technology Studio
    Jpom is licensed under Mulan PSL v2.
    You can use this software according to the terms and conditions of the Mulan PSL v2.
    You may obtain a copy of Mulan PSL v2 at:
    			http://license.coscl.org.cn/MulanPSL2
    THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
    See the Mulan PSL v2 for more details.

-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="<%- base_url %>favicon.ico" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="keywords" content="Jpom,Java项目管理,Jar管理,Java管理系统,服务器项目运维" />
    <meta name="description" content="Jpom-项目管理系统,简而轻的低侵入式在线构建、自动部署、日常运维、项目监控软件" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="build-time" content="<%- build %>" />
    <meta name="build-env" content="<%- env %>" />
    <meta name="jpom-version" content="<%- buildVersion %>" />
    <title><%- title %></title>
    <style>
      .first-loading-wrp {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        min-height: 420px;
        height: 100%;
      }
      .first-loading-wrp > h1 {
        font-size: 128px;
      }
      .first-loading-wrp .loading-wrp {
        padding: 98px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .dot {
        animation: antRotate 1.2s infinite linear;
        transform: rotate(45deg);
        position: relative;
        display: inline-block;
        font-size: 32px;
        width: 32px;
        height: 32px;
        box-sizing: border-box;
      }
      .dot i {
        width: 14px;
        height: 14px;
        position: absolute;
        display: block;
        background-color: #1890ff;
        border-radius: 100%;
        transform: scale(0.75);
        transform-origin: 50% 50%;
        opacity: 0.3;
        animation: antSpinMove 1s infinite linear alternate;
      }
      .dot i:nth-child(1) {
        top: 0;
        left: 0;
      }
      .dot i:nth-child(2) {
        top: 0;
        right: 0;
        -webkit-animation-delay: 0.4s;
        animation-delay: 0.4s;
      }
      .dot i:nth-child(3) {
        right: 0;
        bottom: 0;
        -webkit-animation-delay: 0.8s;
        animation-delay: 0.8s;
      }
      .dot i:nth-child(4) {
        bottom: 0;
        left: 0;
        -webkit-animation-delay: 1.2s;
        animation-delay: 1.2s;
      }
      @keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @-webkit-keyframes antRotate {
        to {
          -webkit-transform: rotate(405deg);
          transform: rotate(405deg);
        }
      }
      @keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
      @-webkit-keyframes antSpinMove {
        to {
          opacity: 1;
        }
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%- title %> doesn't work properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <script>
      window.routerBase = '<routerBase>'
      window.apiTimeout = '<apiTimeout>'
      window.uploadFileSliceSize = '<uploadFileSliceSize>'
      window.uploadFileConcurrent = '<uploadFileConcurrent>'
      window.oauth2Provide = '<oauth2Provide>'
      window.transportEncryption = '<transportEncryption>'
      window.jpomDefaultLocale ='<jpomDefaultLocale>'
    </script>
    <div id="app">
      <div class="first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
        <div style="display: flex; justify-content: center; align-items: center"><%- title %></div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <div id="jpomCommonJs"><!--Don't delete this line, place for public JS --></div>
  </body>
</html>
