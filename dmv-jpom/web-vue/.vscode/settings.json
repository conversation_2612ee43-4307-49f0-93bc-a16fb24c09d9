{"editor.tabSize": 2, "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.enable": true, "eslint.alwaysShowStatus": true, "prettier.enable": true, "stylelint.enable": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}}