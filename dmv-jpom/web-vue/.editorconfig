#
# Copyright (c) 2019 Of Him Code Technology Studio
# Jpom is licensed under Mulan PSL v2.
# You can use this software according to the terms and conditions of the Mulan PSL v2.
# You may obtain a copy of Mulan PSL v2 at:
# 			http://license.coscl.org.cn/MulanPSL2
# THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
# See the Mulan PSL v2 for more details.
#

# http://editorconfig.org

# 和父级配置一致，避免使用新的工作空间打开当前项目，无法获取到配置问题

root = true

[*]
indent_style = space
charset = utf-8
end_of_line = lf
trim_trailing_whitespace = true
insert_final_newline = true
max_line_length = 200

[*.{json,yml}]
indent_style = space
indent_size = 2

[*.md]
insert_final_newline = false
trim_trailing_whitespace = false

[*.{vue,js,ts}]
indent_style = space
indent_size = 2

[*.js]
indent_style = space
indent_size = 2