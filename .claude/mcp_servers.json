{"mcpServers": {"playwright": {"command": "cmd", "args": ["/c", "npx", "@executeautomation/playwright-mcp-server"], "env": {}}, "screenshot": {"command": "cmd", "args": ["/c", "npx", "@modelcontextprotocol/server-puppeteer"], "env": {}}, "snap-happy": {"command": "cmd", "args": ["/c", "npx", "@mario<PERSON><PERSON>ner/snap-happy@latest"], "env": {"SNAP_HAPPY_SCREENSHOT_PATH": "Z:\\IdeaProjects\\erp\\screenshots"}}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp"]}}}